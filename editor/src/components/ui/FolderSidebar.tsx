import {
	Ch<PERSON><PERSON>Down,
	ChevronRight,
	Folder,
	FolderOpen,
	Image as ImageIcon,
	Plus,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import type { ImageFilters, ImageFolder } from "../../types/image";
import { createImageFolder, fetchImageFolders } from "../../utils/apiService";
import { Button } from "./button";
import { Input } from "./input";
import { ScrollArea } from "./scroll-area";

interface FolderSidebarProps {
	selectedFolderId: number | null;
	onFolderSelect: (folderId: number | null) => void;
	filters: ImageFilters;
	onFiltersChange: (filters: Partial<ImageFilters>) => void;
	isCollapsed: boolean;
	onToggleCollapse: () => void;
	className?: string;
	role?: string;
}

interface FolderTreeItem extends ImageFolder {
	children: FolderTreeItem[];
	imageCount?: number;
	isExpanded: boolean;
}

export function FolderSidebar({
	selectedFolderId,
	onFolderSelect,
	onFiltersChange,
	isCollapsed,
	onToggleCollapse,
	className = "",
	role,
}: FolderSidebarProps) {
	const [folders, setFolders] = useState<FolderTreeItem[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreatingFolder, setIsCreatingFolder] = useState(false);
	const [newFolderName, setNewFolderName] = useState("");
	const [newFolderParentId, setNewFolderParentId] = useState<number | null>(
		null,
	);

	// Role-based permissions
	const isAgency = role === "agency";

	const loadFoldersData = useCallback(async () => {
		try {
			setLoading(true);
			const folderData = await fetchImageFolders();

			// Transform flat folder list into tree structure
			const folderMap = new Map<number, FolderTreeItem>();

			// First, create all folder items
			folderData.forEach((folder) => {
				folderMap.set(folder.id, {
					...folder,
					children: [],
					isExpanded: false,
				});
			});

			const rootFolders: FolderTreeItem[] = [];

			// Then, build the tree structure
			folderData.forEach((folder) => {
				const treeItem = folderMap.get(folder.id);
				if (!treeItem) return;
				if (folder.parentId === null) {
					rootFolders.push(treeItem);
				} else {
					const parent = folderMap.get(folder.parentId);
					if (parent) {
						parent.children.push(treeItem);
					}
				}
			});

			setFolders(rootFolders);
		} catch (error) {
			console.error("Failed to load folders:", error);
		} finally {
			setLoading(false);
		}
	}, []);

	useEffect(() => {
		loadFoldersData();
	}, [loadFoldersData]);

	const toggleFolder = (folderId: number) => {
		const updateFolderExpansion = (
			folders: FolderTreeItem[],
		): FolderTreeItem[] => {
			return folders.map((folder) => {
				if (folder.id === folderId) {
					return { ...folder, isExpanded: !folder.isExpanded };
				}
				if (folder.children.length > 0) {
					return {
						...folder,
						children: updateFolderExpansion(folder.children),
					};
				}
				return folder;
			});
		};

		setFolders(updateFolderExpansion(folders));
	};

	const handleFolderClick = (folderId: number | null) => {
		onFolderSelect(folderId);
		onFiltersChange({ folderId });
	};

	const handleCreateFolder = async () => {
		if (!newFolderName.trim()) return;

		try {
			await createImageFolder(newFolderName.trim(), newFolderParentId);
			setNewFolderName("");
			setNewFolderParentId(null);
			setIsCreatingFolder(false);
			await loadFoldersData(); // Refresh folder list
		} catch (err) {
			console.error("Failed to create folder:", err);
			setError("Failed to create folder");
		}
	};

	const cancelCreateFolder = () => {
		setIsCreatingFolder(false);
		setNewFolderName("");
		setNewFolderParentId(null);
	};

	const renderFolder = (folder: FolderTreeItem, level: number = 0) => {
		const isSelected = selectedFolderId === folder.id;
		const hasChildren = folder.children.length > 0;
		const paddingLeft = level * 16 + 8;

		return (
			<div key={folder.id} className="select-none">
				<div
					className={`group flex items-center py-1 px-2 cursor-pointer hover:bg-muted rounded-sm transition-colors ${
						isSelected ? "bg-primary/10 text-primary" : ""
					}`}
					style={{ paddingLeft: `${paddingLeft}px` }}
				>
					{hasChildren && (
						<Button
							variant="ghost"
							size="sm"
							className="h-4 w-4 p-0 mr-1"
							onClick={(e) => {
								e.stopPropagation();
								toggleFolder(folder.id);
							}}
						>
							{folder.isExpanded ? (
								<ChevronDown className="h-3 w-3" />
							) : (
								<ChevronRight className="h-3 w-3" />
							)}
						</Button>
					)}
					{!hasChildren && <div className="w-5" />}

					{isSelected ? (
						<FolderOpen className="h-4 w-4 mr-2 text-primary" />
					) : (
						<Folder className="h-4 w-4 mr-2 text-muted-foreground" />
					)}

					<button
						type="button"
						className="text-sm truncate flex-1 text-left bg-transparent border-none p-0"
						onClick={() => handleFolderClick(folder.id)}
					>
						{folder.folderName}
					</button>

					{folder.imageCount !== undefined && (
						<span className="text-xs text-muted-foreground ml-2">
							{folder.imageCount}
						</span>
					)}
				</div>

				{hasChildren && folder.isExpanded && (
					<div>
						{folder.children.map((child) => renderFolder(child, level + 1))}
					</div>
				)}
			</div>
		);
	};

	if (isCollapsed) {
		return (
			<div className={`w-12 border-r bg-background ${className}`}>
				<div className="p-2">
					<Button
						variant="ghost"
						size="sm"
						onClick={onToggleCollapse}
						className="w-full"
					>
						<Folder className="h-4 w-4" />
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className={`w-64 border-r bg-background flex flex-col ${className}`}>
			<div className="p-3 border-b">
				<div className="flex items-center justify-between mb-2">
					<h3 className="font-medium text-sm">Ordner</h3>
					<div className="flex items-center space-x-1">
						{isAgency && (
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setIsCreatingFolder(true)}
								className="h-6 w-6 p-0"
								title="Neuen Ordner erstellen"
							>
								<Plus className="h-3 w-3" />
							</Button>
						)}
						<Button
							variant="ghost"
							size="sm"
							onClick={onToggleCollapse}
							className="h-6 w-6 p-0"
						>
							<ChevronRight className="h-3 w-3" />
						</Button>
					</div>
				</div>

				{/* Create folder input */}
				{isCreatingFolder && (
					<div className="space-y-2">
						<Input
							placeholder="Ordnername..."
							value={newFolderName}
							onChange={(e) => setNewFolderName(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleCreateFolder();
								} else if (e.key === "Escape") {
									cancelCreateFolder();
								}
							}}
							autoFocus
							className="text-sm h-8"
						/>
						<div className="flex space-x-1">
							<Button
								variant="outline"
								size="sm"
								onClick={handleCreateFolder}
								disabled={!newFolderName.trim()}
								className="h-6 text-xs"
							>
								Erstellen
							</Button>
							<Button
								variant="ghost"
								size="sm"
								onClick={cancelCreateFolder}
								className="h-6 text-xs"
							>
								Abbrechen
							</Button>
						</div>
					</div>
				)}
			</div>

			<ScrollArea className="flex-1">
				{loading ? (
					<div className="p-4 text-sm text-muted-foreground text-center">
						Ordner werden geladen...
					</div>
				) : error ? (
					<div className="p-4 text-sm text-red-500 text-center">{error}</div>
				) : (
					<div className="p-2">
						{/* Root folder (All images) */}
						<button
							type="button"
							className={`flex items-center py-1 px-2 cursor-pointer hover:bg-muted rounded-sm transition-colors mb-2 border-none bg-transparent w-full text-left ${
								selectedFolderId === null ? "bg-primary/10 text-primary" : ""
							}`}
							onClick={() => handleFolderClick(null)}
						>
							<ImageIcon className="h-4 w-4 mr-2" />
							<span className="text-sm">Alle Bilder</span>
						</button>

						{/* Folder tree */}
						{folders.map((folder) => renderFolder(folder))}
					</div>
				)}
			</ScrollArea>
		</div>
	);
}
