import type { Element } from "../../types/element";
import { getShapeWrapperStyles } from "../../utils/elementStyles";

interface ShapeElementProps {
	element: Element;
}

export function ShapeElement({ element }: ShapeElementProps) {
	const wrapperStyle = getShapeWrapperStyles(element);

	return (
		<div style={wrapperStyle}>
			<div
				className="w-full h-full" // Inner div only needs bg color now
				style={{
					backgroundColor: element.backgroundColor || "#e2e8f0",
				}}
			/>
		</div>
	);
}
