import { act, renderHook } from "@testing-library/react";
import { useApiData } from "../useApiData";

describe("useApiData", () => {
	// Test initialization
	it("should initialize with empty state", () => {
		const { result } = renderHook(() => useApiData());

		expect(result.current.apiFormats).toEqual([]);
		expect(result.current.documentVariables).toEqual([]);
		expect(result.current.testData).toEqual([]);
		expect(result.current.versionHistory).toEqual([]);
		expect(result.current.apiFonts).toEqual([]);
		expect(result.current.isLoadingFonts).toBe(true);
		expect(result.current.loadedFonts).toEqual(new Set());
		expect(result.current.apiColors).toEqual([]);
		expect(result.current.isLoadingColors).toBe(true);
		expect(result.current.colorError).toBeNull();
		expect(result.current.templates).toEqual([]);
		expect(result.current.isLoadingTemplates).toBe(true);
		expect(result.current.templatesError).toBeNull();
	});

	// Test state updates
	describe("state updates", () => {
		it("should update apiFormats state", () => {
			const { result } = renderHook(() => useApiData());
			const mockFormats = [
				{
					name: "A4",
					description: "A4 Format",
					width: 210,
					height: 297,
					trim_top: 5,
					trim_right: 5,
					trim_bottom: 5,
					trim_left: 5,
					pagenumber_w: 105,
					pagenumber_y: 280,
					address_x: 20,
					address_y: 50,
				},
			];

			act(() => {
				result.current.setApiFormats(mockFormats);
			});

			expect(result.current.apiFormats).toEqual(mockFormats);
		});

		it("should update documentVariables state", () => {
			const { result } = renderHook(() => useApiData());
			const mockVariables = ["name", "address", "phone"];

			act(() => {
				result.current.setDocumentVariables(mockVariables);
			});

			expect(result.current.documentVariables).toEqual(mockVariables);
		});

		it("should update testData state", () => {
			const { result } = renderHook(() => useApiData());
			const mockTestData = [
				{
					id: "1",
					name: "Test 1",
					data: '{"name":"John","address":"123 Main St"}',
				},
				{
					id: "2",
					name: "Test 2",
					data: '{"name":"Jane","address":"456 Oak Ave"}',
				},
			];

			act(() => {
				result.current.setTestData(mockTestData);
			});

			expect(result.current.testData).toEqual(mockTestData);
		});

		it("should update versionHistory state", () => {
			const { result } = renderHook(() => useApiData());
			const mockVersionHistory = [
				{
					id: "1",
					version: 1.0,
					created_at: "2023-01-01T12:00:00Z",
					user: "user1",
					comment: "Initial version",
				},
				{
					id: "2",
					version: 1.1,
					created_at: "2023-01-02T12:00:00Z",
					user: "user2",
					comment: "Updated layout",
				},
			];

			act(() => {
				result.current.setVersionHistory(mockVersionHistory);
			});

			expect(result.current.versionHistory).toEqual(mockVersionHistory);
		});
	});

	// Test font-related state
	describe("font-related state", () => {
		it("should update apiFonts state", () => {
			const { result } = renderHook(() => useApiData());
			const mockFonts = [
				{
					name: "Arial",
					family: "Arial, sans-serif",
					weight: 400,
					style: "normal",
				},
				{
					name: "Times New Roman",
					family: "Times New Roman, serif",
					weight: 400,
					style: "normal",
				},
			];

			act(() => {
				result.current.setApiFontsState(mockFonts);
			});

			expect(result.current.apiFonts).toEqual(mockFonts);
		});

		it("should update isLoadingFonts state", () => {
			const { result } = renderHook(() => useApiData());

			act(() => {
				result.current.setIsLoadingFonts(false);
			});

			expect(result.current.isLoadingFonts).toBe(false);
		});

		it("should update loadedFonts state", () => {
			const { result } = renderHook(() => useApiData());
			const mockLoadedFonts = new Set(["Arial", "Times New Roman"]);

			act(() => {
				result.current.setLoadedFonts(mockLoadedFonts);
			});

			expect(result.current.loadedFonts).toEqual(mockLoadedFonts);
		});
	});

	// Test color-related state
	describe("color-related state", () => {
		it("should update apiColors state", () => {
			const { result } = renderHook(() => useApiData());
			const mockColors = [
				{
					name: "Red",
					value: "#FF0000",
					hex: "#FF0000",
					rgb: "rgb(255,0,0)",
					cmyk: "cmyk(0,100,100,0)",
				},
				{
					name: "Blue",
					value: "#0000FF",
					hex: "#0000FF",
					rgb: "rgb(0,0,255)",
					cmyk: "cmyk(100,100,0,0)",
				},
			];

			act(() => {
				result.current.setApiColors(mockColors);
			});

			expect(result.current.apiColors).toEqual(mockColors);
		});

		it("should update isLoadingColors state", () => {
			const { result } = renderHook(() => useApiData());

			act(() => {
				result.current.setIsLoadingColors(false);
			});

			expect(result.current.isLoadingColors).toBe(false);
		});

		it("should update colorError state", () => {
			const { result } = renderHook(() => useApiData());
			const mockError = "Failed to load colors";

			act(() => {
				result.current.setColorError(mockError);
			});

			expect(result.current.colorError).toBe(mockError);
		});
	});

	// Test template-related state
	describe("template-related state", () => {
		it("should update templates state", () => {
			const { result } = renderHook(() => useApiData());
			const mockTemplates = [
				{
					id: "1",
					name: "Template 1",
					thumbnail: "thumb1.jpg",
					json: '{"name":"Template 1"}',
					user: "user1",
					createdAt: "2023-01-01T10:00:00Z",
				},
				{
					id: "2",
					name: "Template 2",
					thumbnail: "thumb2.jpg",
					json: '{"name":"Template 2"}',
					user: "user2",
					createdAt: "2023-01-02T10:00:00Z",
				},
			];

			act(() => {
				result.current.setTemplates(mockTemplates);
			});

			expect(result.current.templates).toEqual(mockTemplates);
		});

		it("should update isLoadingTemplates state", () => {
			const { result } = renderHook(() => useApiData());

			act(() => {
				result.current.setIsLoadingTemplates(false);
			});

			expect(result.current.isLoadingTemplates).toBe(false);
		});

		it("should update templatesError state", () => {
			const { result } = renderHook(() => useApiData());
			const mockError = "Failed to load templates";

			act(() => {
				result.current.setTemplatesError(mockError);
			});

			expect(result.current.templatesError).toBe(mockError);
		});
	});
});
