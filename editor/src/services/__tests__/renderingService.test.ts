import { describe, expect, it, vi } from "vitest";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import type { Element } from "../../types/element";
import * as elementService from "../elementService";
import type { SnapGuide } from "../renderingService";
import * as renderingService from "../renderingService";

// Mock elementService functions
vi.mock("../elementService", () => ({
	getTopLevelElementsForPage: vi.fn(),
	sortElementsByZIndex: vi.fn((elements) => elements),
}));

describe("renderingService", () => {
	describe("calculatePageDimensions", () => {
		it("should calculate page dimensions correctly", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210, // A4 width in mm
				height: 297, // A4 height in mm
				trim_left: 1,
				trim_right: 1,
				trim_top: 1,
				trim_bottom: 1,
			});

			// Act
			const dimensions = renderingService.calculatePageDimensions(page);

			// Assert
			expect(dimensions.trimLeftCm).toBe(1);
			expect(dimensions.trimRightCm).toBe(1);
			expect(dimensions.trimTopCm).toBe(1);
			expect(dimensions.trimBottomCm).toBe(1);
			expect(dimensions.outerWidthCm).toBe(212); // 210 + 1 + 1
			expect(dimensions.outerHeightCm).toBe(299); // 297 + 1 + 1

			// Check pixel conversions (using CM_TO_PX = 3.7795275591)
			const CM_TO_PX = 3.7795275591;
			expect(dimensions.pageWidthPx).toBeCloseTo(210 * 10 * CM_TO_PX);
			expect(dimensions.pageHeightPx).toBeCloseTo(297 * 10 * CM_TO_PX);
			expect(dimensions.trimLeftPx).toBeCloseTo(1 * 10 * CM_TO_PX);
			expect(dimensions.trimRightPx).toBeCloseTo(1 * 10 * CM_TO_PX);
			expect(dimensions.trimTopPx).toBeCloseTo(1 * 10 * CM_TO_PX);
			expect(dimensions.trimBottomPx).toBeCloseTo(1 * 10 * CM_TO_PX);

			// Content dimensions should be page dimensions minus trim
			expect(dimensions.contentWidthPx).toBeCloseTo(
				dimensions.pageWidthPx - dimensions.trimLeftPx - dimensions.trimRightPx,
			);
			expect(dimensions.contentHeightPx).toBeCloseTo(
				dimensions.pageHeightPx -
					dimensions.trimTopPx -
					dimensions.trimBottomPx,
			);
		});

		it("should handle pages without trim values", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210,
				height: 297,
				trim_left: undefined,
				trim_right: undefined,
				trim_top: undefined,
				trim_bottom: undefined,
			});

			// Act
			const dimensions = renderingService.calculatePageDimensions(page);

			// Assert
			expect(dimensions.trimLeftCm).toBe(0);
			expect(dimensions.trimRightCm).toBe(0);
			expect(dimensions.trimTopCm).toBe(0);
			expect(dimensions.trimBottomCm).toBe(0);
			expect(dimensions.outerWidthCm).toBe(210);
			expect(dimensions.outerHeightCm).toBe(297);
			expect(dimensions.trimLeftPx).toBe(0);
			expect(dimensions.trimRightPx).toBe(0);
			expect(dimensions.trimTopPx).toBe(0);
			expect(dimensions.trimBottomPx).toBe(0);
			expect(dimensions.contentWidthPx).toBe(dimensions.pageWidthPx);
			expect(dimensions.contentHeightPx).toBe(dimensions.pageHeightPx);
		});
	});

	describe("getPageInfo", () => {
		it("should return correct page info for front page", () => {
			// Arrange
			const page = PageFactory.createPage();
			const index = 0; // First page (front)
			const totalPages = 4;

			// Act
			const pageInfo = renderingService.getPageInfo(page, index, totalPages);

			// Assert
			expect(pageInfo.page).toBe(page);
			expect(pageInfo.index).toBe(0);
			expect(pageInfo.isFrontPage).toBe(true);
			expect(pageInfo.isBackPage).toBe(false);
			expect(pageInfo.isLastPageOfPhysicalPair).toBe(false);
			expect(pageInfo.dimensions).toBeDefined();
		});

		it("should return correct page info for back page", () => {
			// Arrange
			const page = PageFactory.createPage();
			const index = 1; // Second page (back)
			const totalPages = 4;

			// Act
			const pageInfo = renderingService.getPageInfo(page, index, totalPages);

			// Assert
			expect(pageInfo.page).toBe(page);
			expect(pageInfo.index).toBe(1);
			expect(pageInfo.isFrontPage).toBe(false);
			expect(pageInfo.isBackPage).toBe(true);
			expect(pageInfo.isLastPageOfPhysicalPair).toBe(true);
		});

		it("should mark last page as last of physical pair even if it is a front page", () => {
			// Arrange
			const page = PageFactory.createPage();
			const index = 3; // Last page (front in a 4-page document)
			const totalPages = 4;

			// Act
			const pageInfo = renderingService.getPageInfo(page, index, totalPages);

			// Assert
			expect(pageInfo.index).toBe(3);
			expect(pageInfo.isFrontPage).toBe(false);
			expect(pageInfo.isBackPage).toBe(true);
			expect(pageInfo.isLastPageOfPhysicalPair).toBe(true);
		});
	});

	describe("getFormatDescription", () => {
		it("should return description from API formats if available", () => {
			// Arrange
			const formatId = "a4";
			const apiFormats = [
				{ name: "a4", description: "A4 (210×297 mm)" },
				{ name: "a5", description: "A5 (148×210 mm)" },
			];

			// Act
			const description = renderingService.getFormatDescription(
				formatId,
				apiFormats,
			);

			// Assert
			expect(description).toBe("A4 (210×297 mm)");
		});

		it("should create readable name if format not found in API formats", () => {
			// Arrange
			const formatId = "custom_format";
			const apiFormats = [{ name: "a4", description: "A4 (210×297 mm)" }];

			// Act
			const description = renderingService.getFormatDescription(
				formatId,
				apiFormats,
			);

			// Assert
			expect(description).toBe("Custom Format");
		});

		it("should handle empty API formats array", () => {
			// Arrange
			const formatId = "a4";
			const apiFormats: Array<{ name: string; description: string }> = [];

			// Act
			const description = renderingService.getFormatDescription(
				formatId,
				apiFormats,
			);

			// Assert
			expect(description).toBe("A4");
		});
	});

	describe("shouldShowPageNumber", () => {
		it("should return true when all conditions are met", () => {
			// Arrange
			const page = PageFactory.createPage({
				pagenumber_x: 100,
				pagenumber_y: 280,
			});
			const showPageNumbers = true;
			const deletedPageNumberIds = new Set<string>();

			// Act
			const result = renderingService.shouldShowPageNumber(
				page,
				showPageNumbers,
				deletedPageNumberIds,
			);

			// Assert
			expect(result).toBe(true);
		});

		it("should return false when showPageNumbers is false", () => {
			// Arrange
			const page = PageFactory.createPage({
				pagenumber_x: 100,
				pagenumber_y: 280,
			});
			const showPageNumbers = false;
			const deletedPageNumberIds = new Set<string>();

			// Act
			const result = renderingService.shouldShowPageNumber(
				page,
				showPageNumbers,
				deletedPageNumberIds,
			);

			// Assert
			expect(result).toBe(false);
		});

		it("should return false when page ID is in deletedPageNumberIds", () => {
			// Arrange
			const page = PageFactory.createPage({
				id: "page-1",
				pagenumber_x: 100,
				pagenumber_y: 280,
			});
			const showPageNumbers = true;
			const deletedPageNumberIds = new Set<string>(["page-1"]);

			// Act
			const result = renderingService.shouldShowPageNumber(
				page,
				showPageNumbers,
				deletedPageNumberIds,
			);

			// Assert
			expect(result).toBe(false);
		});

		it("should return false when pagenumber_x is not defined", () => {
			// Arrange
			const page = PageFactory.createPage({
				pagenumber_x: undefined,
				pagenumber_y: 280,
			});
			const showPageNumbers = true;
			const deletedPageNumberIds = new Set<string>();

			// Act
			const result = renderingService.shouldShowPageNumber(
				page,
				showPageNumbers,
				deletedPageNumberIds,
			);

			// Assert
			expect(result).toBe(false);
		});

		it("should return false when pagenumber_y is not defined", () => {
			// Arrange
			const page = PageFactory.createPage({
				pagenumber_x: 100,
				pagenumber_y: undefined,
			});
			const showPageNumbers = true;
			const deletedPageNumberIds = new Set<string>();

			// Act
			const result = renderingService.shouldShowPageNumber(
				page,
				showPageNumbers,
				deletedPageNumberIds,
			);

			// Assert
			expect(result).toBe(false);
		});
	});

	describe("calculatePageNumber", () => {
		it("should calculate page number correctly for front pages", () => {
			// Act & Assert
			expect(renderingService.calculatePageNumber(0)).toBe(1); // First page (index 0) -> page 1
			expect(renderingService.calculatePageNumber(2)).toBe(2); // Third page (index 2) -> page 2
			expect(renderingService.calculatePageNumber(4)).toBe(3); // Fifth page (index 4) -> page 3
		});

		it("should calculate page number correctly for back pages", () => {
			// Act & Assert
			expect(renderingService.calculatePageNumber(1)).toBe(1); // Second page (index 1) -> page 1
			expect(renderingService.calculatePageNumber(3)).toBe(2); // Fourth page (index 3) -> page 2
			expect(renderingService.calculatePageNumber(5)).toBe(3); // Sixth page (index 5) -> page 3
		});
	});

	describe("getElementsForPage", () => {
		it("should get top-level elements for a page and sort them by z-index", () => {
			// Arrange
			const pageId = "page-1";
			const elements: Element[] = [
				ElementFactory.createTextElement({ currentPageId: pageId }),
				ElementFactory.createShapeElement({ currentPageId: pageId }),
			];

			const topLevelElements = [elements[0], elements[1]];

			const sortedElements = [
				elements[1], // Assuming this has lower z-index
				elements[0],
			];

			(elementService.getTopLevelElementsForPage as jest.Mock).mockReturnValue(
				topLevelElements,
			);
			(elementService.sortElementsByZIndex as jest.Mock).mockReturnValue(
				sortedElements,
			);

			// Act
			const result = renderingService.getElementsForPage(elements, pageId);

			// Assert
			expect(elementService.getTopLevelElementsForPage).toHaveBeenCalledWith(
				elements,
				pageId,
			);
			expect(elementService.sortElementsByZIndex).toHaveBeenCalledWith(
				topLevelElements,
			);
			expect(result).toBe(sortedElements);
		});
	});

	describe("calculateSnapGuideStyles", () => {
		it("should calculate styles for horizontal guide", () => {
			// Arrange
			const guide: SnapGuide = {
				type: "horizontal",
				position: 100,
				start: 50,
				end: 150,
			};

			// Act
			const styles = renderingService.calculateSnapGuideStyles(guide, true);

			// Assert
			expect(styles.position).toBe("absolute");
			expect(styles.height).toBe("1px");
			expect(styles.left).toBe("50mm");
			expect(styles.width).toBe("100mm"); // 150 - 50
			expect(styles.top).toBe("100mm");
			expect(styles.pointerEvents).toBe("none");
		});

		it("should calculate styles for vertical guide", () => {
			// Arrange
			const guide: SnapGuide = {
				type: "vertical",
				position: 100,
				start: 50,
				end: 150,
			};

			// Act
			const styles = renderingService.calculateSnapGuideStyles(guide, false);

			// Assert
			expect(styles.position).toBe("absolute");
			expect(styles.width).toBe("1px");
			expect(styles.top).toBe("50mm");
			expect(styles.height).toBe("100mm"); // 150 - 50
			expect(styles.left).toBe("100mm");
			expect(styles.pointerEvents).toBe("none");
		});
	});

	describe("calculateSnapGuideContainerStyle", () => {
		it("should calculate container style relative to page", () => {
			// Arrange
			const snapGuides = {
				visible: true,
				guides: [],
				relativeToBlockId: undefined,
			};

			const page = PageFactory.createPage({
				width: 210,
				height: 297,
			});

			const trimTopCm = 1;
			const trimLeftCm = 1;

			// Act
			const style = renderingService.calculateSnapGuideContainerStyle(
				snapGuides,
				page,
				trimTopCm,
				trimLeftCm,
			);

			// Assert
			expect(style.top).toBe("1cm");
			expect(style.left).toBe("1cm");
			expect(style.width).toBe("210cm");
			expect(style.height).toBe("297cm");
			expect(style.zIndex).toBe(9999);
		});

		it("should calculate container style relative to block", () => {
			// Arrange
			const snapGuides = {
				visible: true,
				guides: [],
				relativeToBlockId: "block-1",
				blockX: 50,
				blockY: 100,
				blockWidth: 200,
				blockHeight: 150,
			};

			const page = PageFactory.createPage();
			const trimTopCm = 1;
			const trimLeftCm = 1;

			// Act
			const style = renderingService.calculateSnapGuideContainerStyle(
				snapGuides,
				page,
				trimTopCm,
				trimLeftCm,
			);

			// Assert
			expect(style.top).toBe("11cm"); // (10 + 100) / 10
			expect(style.left).toBe("6cm"); // (10 + 50) / 10
			expect(style.width).toBe("20cm"); // 200 / 10
			expect(style.height).toBe("15cm"); // 150 / 10
			expect(style.zIndex).toBe(9999);
		});
	});

	describe("shouldShowSnapGuides", () => {
		it("should return true when guides are visible and current page matches", () => {
			// Arrange
			const snapGuides = {
				visible: true,
				guides: [],
				relativeToBlockId: undefined,
			};

			const currentPage = "page-1";
			const pageId = "page-1";
			const elements: Element[] = [];

			// Act
			const result = renderingService.shouldShowSnapGuides(
				snapGuides,
				currentPage,
				pageId,
				elements,
			);

			// Assert
			expect(result).toBe(true);
		});

		it("should return true when guides are visible and relative block is on the page", () => {
			// Arrange
			const blockId = "block-1";
			const pageId = "page-1";

			const snapGuides = {
				visible: true,
				guides: [],
				relativeToBlockId: blockId,
			};

			const currentPage = "page-2"; // Different from pageId

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					currentPageId: pageId,
				},
			];

			// Act
			const result = renderingService.shouldShowSnapGuides(
				snapGuides,
				currentPage,
				pageId,
				elements,
			);

			// Assert
			expect(result).toBe(true);
		});

		it("should return false when guides are not visible", () => {
			// Arrange
			const snapGuides = {
				visible: false,
				guides: [],
				relativeToBlockId: undefined,
			};

			const currentPage = "page-1";
			const pageId = "page-1";
			const elements: Element[] = [];

			// Act
			const result = renderingService.shouldShowSnapGuides(
				snapGuides,
				currentPage,
				pageId,
				elements,
			);

			// Assert
			expect(result).toBe(false);
		});

		it("should return false when current page does not match and no relative block", () => {
			// Arrange
			const snapGuides = {
				visible: true,
				guides: [],
				relativeToBlockId: undefined,
			};

			const currentPage = "page-1";
			const pageId = "page-2"; // Different from currentPage
			const elements: Element[] = [];

			// Act
			const result = renderingService.shouldShowSnapGuides(
				snapGuides,
				currentPage,
				pageId,
				elements,
			);

			// Assert
			expect(result).toBe(false);
		});

		it("should return false when relative block is not on the page", () => {
			// Arrange
			const blockId = "block-1";
			const pageId = "page-1";

			const snapGuides = {
				visible: true,
				guides: [],
				relativeToBlockId: blockId,
			};

			const currentPage = "page-2"; // Different from pageId

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					currentPageId: "page-3",
				}, // Not on pageId
			];

			// Act
			const result = renderingService.shouldShowSnapGuides(
				snapGuides,
				currentPage,
				pageId,
				elements,
			);

			// Assert
			expect(result).toBe(false);
		});
	});

	describe("convertMousePosition", () => {
		it("should convert mouse position from page coordinates to content coordinates", () => {
			// Arrange
			const clientX = 100;
			const clientY = 200;
			const trimLeftCm = 1;
			const trimTopCm = 1;

			// Mock HTMLElement with getBoundingClientRect
			const pageElement = {
				getBoundingClientRect: () => ({
					left: 50,
					top: 100,
					width: 500,
					height: 700,
				}),
				scrollLeft: 0,
				scrollTop: 0,
			} as unknown as HTMLElement;

			// Act
			const result = renderingService.convertMousePosition(
				clientX,
				clientY,
				pageElement,
				trimLeftCm,
				trimTopCm,
			);

			// Assert
			// We can't easily predict the exact values due to conversion factors,
			// but we can verify that the function returns reasonable values
			expect(typeof result.x).toBe("number");
			expect(typeof result.y).toBe("number");
			expect(result.x).toBeGreaterThan(0);
			expect(result.y).toBeGreaterThan(0);
		});

		it("should account for scroll position", () => {
			// Arrange
			const clientX = 100;
			const clientY = 200;
			const trimLeftCm = 1;
			const trimTopCm = 1;

			// Create two mock elements with different scroll positions
			const pageElement1 = {
				getBoundingClientRect: () => ({
					left: 50,
					top: 100,
					width: 500,
					height: 700,
				}),
				scrollLeft: 0,
				scrollTop: 0,
			} as unknown as HTMLElement;

			const pageElement2 = {
				getBoundingClientRect: () => ({
					left: 50,
					top: 100,
					width: 500,
					height: 700,
				}),
				scrollLeft: 20,
				scrollTop: 30,
			} as unknown as HTMLElement;

			// Act
			const result1 = renderingService.convertMousePosition(
				clientX,
				clientY,
				pageElement1,
				trimLeftCm,
				trimTopCm,
			);

			const result2 = renderingService.convertMousePosition(
				clientX,
				clientY,
				pageElement2,
				trimLeftCm,
				trimTopCm,
			);

			// Assert
			// The second result should have larger coordinates due to scroll
			expect(result2.x).toBeGreaterThan(result1.x);
			expect(result2.y).toBeGreaterThan(result1.y);
		});
	});

	describe("calculateZoomStyles", () => {
		it("should calculate zoom styles correctly", () => {
			// Arrange
			const zoomLevel = 1.5;
			const editingBlockId = null;

			// Act
			const styles = renderingService.calculateZoomStyles(
				zoomLevel,
				editingBlockId,
			);

			// Assert
			expect(styles.transform).toBe("scale(1.5)");
			expect(styles.transformOrigin).toBe("top center");
			expect(styles.padding).toBe("12px"); // 8 * 1.5
			expect(styles.marginBottom).toBe("75px"); // 50 * 1.5
			expect(styles.pointerEvents).toBe("auto");
			expect(styles.position).toBe("relative");
			expect(styles.zIndex).toBe("auto");
		});

		it("should set higher z-index when editing a block", () => {
			// Arrange
			const zoomLevel = 1;
			const editingBlockId = "block-1";

			// Act
			const styles = renderingService.calculateZoomStyles(
				zoomLevel,
				editingBlockId,
			);

			// Assert
			expect(styles.zIndex).toBe(5001);
		});
	});

	describe("getPageContainerStyles", () => {
		it("should return correct page container styles", () => {
			// Arrange
			const dimensions = {
				outerWidthCm: 212,
				outerHeightCm: 299,
				// Other properties not used in this function
			} as renderingService.PageDimensions;

			// Act
			const styles = renderingService.getPageContainerStyles(dimensions);

			// Assert
			expect(styles.width).toBe("212cm");
			expect(styles.height).toBe("299cm");
			expect(styles.minWidth).toBe("212cm");
			expect(styles.flexShrink).toBe(0);
		});
	});

	describe("getContentAreaStyles", () => {
		it("should return correct content area styles", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210,
				height: 297,
			});

			const dimensions = {
				trimTopCm: 1,
				trimLeftCm: 1,
				// Other properties not used in this function
			} as renderingService.PageDimensions;

			// Act
			const styles = renderingService.getContentAreaStyles(page, dimensions);

			// Assert
			expect(styles.top).toBe("1cm");
			expect(styles.left).toBe("1cm");
			expect(styles.width).toBe("210cm");
			expect(styles.height).toBe("297cm");
		});
	});

	describe("getPageNumberStyles", () => {
		it("should return correct page number styles", () => {
			// Arrange
			const page = PageFactory.createPage({
				pagenumber_x: 10,
				pagenumber_y: 20,
			});

			// Act
			const styles = renderingService.getPageNumberStyles(page);

			// Assert
			const CM_TO_PX = 3.7795275591;
			expect(styles.position).toBe("absolute");
			expect(styles.left).toBe(`${10 * 10 * CM_TO_PX}px`);
			expect(styles.top).toBe(`${20 * 10 * CM_TO_PX}px`);
			expect(styles.fontSize).toBe("12px");
			expect(styles.color).toBe("#666");
			expect(styles.pointerEvents).toBe("none");
			expect(styles.userSelect).toBe("none");
		});

		it("should handle undefined page number coordinates", () => {
			// Arrange
			const page = PageFactory.createPage({
				pagenumber_x: undefined,
				pagenumber_y: undefined,
			});

			// Act
			const styles = renderingService.getPageNumberStyles(page);

			// Assert
			expect(styles.left).toBe("0px");
			expect(styles.top).toBe("0px");
		});
	});
});
