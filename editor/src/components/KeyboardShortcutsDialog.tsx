import { HelpCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";

interface Shortcut {
	action: string;
	keys: string;
}

interface ShortcutGroup {
	title: string;
	shortcuts: Shortcut[];
}

export function KeyboardShortcutsDialog() {
	// Detect platform once on mount
	const [isMac, setIsMac] = useState(false);

	useEffect(() => {
		if (typeof navigator !== "undefined") {
			// macOS platforms report "MacIntel", iOS may report "iPhone", etc.
			setIsMac(/Mac|iPod|iPhone|iPad/.test(navigator.platform));
		}
	}, []);

	const modKey = isMac ? "⌘" : "Strg";
	const shiftKey = isMac ? "⇧" : "Shift";

	const shortcutGroups: ShortcutGroup[] = [
		{
			title: "Allgemein",
			shortcuts: [
				{ action: "<PERSON><PERSON>g<PERSON><PERSON><PERSON>", keys: `${modKey} + Z` },
				{
					action: "Wiederherstellen",
					keys: `${shiftKey} + ${modKey} + Z  /  ${modKey} + Y`,
				},
				{ action: "Vorschau", keys: `${modKey} + P` },
			],
		},
		{
			title: "Element-Bearbeitung",
			shortcuts: [
				{ action: "Kopieren", keys: `${modKey} + C` },
				{ action: "Ausschneiden", keys: `${modKey} + X` },
				{ action: "Einfügen", keys: `${modKey} + V` },
				{ action: "Löschen", keys: "Entf / Backspace" },
				{ action: "Bearbeiten starten", keys: "Enter" },
				{ action: "Bearbeiten beenden", keys: "Esc" },
				{ action: "Bewegen (0,5 mm)", keys: "Pfeiltasten" },
				{ action: "Bewegen (10 mm)", keys: `${shiftKey} + Pfeiltasten` },
				{ action: "Bewegen (0,1 mm)", keys: `${modKey} + Pfeiltasten` },
			],
		},
		{
			title: "Bildverwaltung",
			shortcuts: [
				{ action: "Alle Bilder auswählen", keys: `${modKey} + A` },
				{ action: "Kopieren", keys: `${modKey} + C` },
				{ action: "Ausschneiden", keys: `${modKey} + X` },
				{ action: "Einfügen", keys: `${modKey} + V` },
				{ action: "Schließen / Auswahl aufheben", keys: "Esc" },
			],
		},
		{
			title: "Tabellenbearbeitung",
			shortcuts: [
				{ action: "Navigation", keys: "Pfeiltasten" },
				{
					action: "Mehrfachauswahl erweitern",
					keys: `${shiftKey} + Pfeiltasten`,
				},
				{ action: "Zelle bearbeiten", keys: "Enter" },
				{
					action: "Bearbeitungsmodus verlassen / Auswahl aufheben",
					keys: "Esc",
				},
			],
		},
		{
			title: "Seitennavigation",
			shortcuts: [
				{ action: "Vorherige Seite", keys: "Page Up" },
				{ action: "Nächste Seite", keys: "Page Down" },
			],
		},
	];

	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="outline" className="flex items-center gap-2">
					<HelpCircle className="h-4 w-4" />
					<span>Tastaturkürzel</span>
				</Button>
			</DialogTrigger>
			<DialogContent className="max-w-xl">
				<DialogHeader>
					<DialogTitle>Tastaturkürzel</DialogTitle>
					<DialogDescription>
						Hier findest du eine Übersicht der verfügbaren Tastenkombinationen.
					</DialogDescription>
				</DialogHeader>
				<div className="overflow-auto max-h-[60vh] mt-4">
					<div className="space-y-6">
						{shortcutGroups.map((group) => (
							<div key={group.title}>
								<h3 className="font-semibold mb-2 text-base">{group.title}</h3>
								<table className="w-full text-sm">
									<tbody>
										{group.shortcuts.map((sc) => (
											<tr
												key={`${group.title}-${sc.action}`}
												className="border-b last:border-0"
											>
												<td className="py-1 pr-4 font-medium text-left whitespace-nowrap">
													{sc.action}
												</td>
												<td className="py-1 text-gray-700">{sc.keys}</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
						))}
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
