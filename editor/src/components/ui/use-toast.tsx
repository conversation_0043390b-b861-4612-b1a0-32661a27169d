import { type ReactNode, useState } from "react";
import { ToastContext, type ToastType } from "@/hooks/use-toast";
import {
	Toast,
	ToastClose,
	ToastDescription,
	ToastProvider,
	ToastTitle,
	ToastViewport,
} from "./toast";

const TOAST_REMOVE_DELAY = 3000;

export function ToastContainer({ children }: { children: ReactNode }) {
	const [toasts, setToasts] = useState<ToastType[]>([]);

	const toast = ({
		title,
		description,
		variant,
	}: {
		title?: string;
		description?: string;
		variant?: "default" | "destructive";
	}) => {
		const id = Math.random().toString(36).substring(2, 9);
		setToasts((prev) => [...prev, { id, title, description, variant }]);

		setTimeout(() => {
			setToasts((prev) => prev.filter((toast) => toast.id !== id));
		}, TOAST_REMOVE_DELAY);
	};

	return (
		<ToastContext.Provider value={{ toast, toasts }}>
			{children}
			<ToastProvider>
				{toasts.map(({ id, title, description, variant }) => (
					<Toast key={id} variant={variant}>
						<div className="grid gap-1">
							{title && <ToastTitle>{title}</ToastTitle>}
							{description && (
								<ToastDescription>{description}</ToastDescription>
							)}
						</div>
						<ToastClose />
					</Toast>
				))}
				<ToastViewport />
			</ToastProvider>
		</ToastContext.Provider>
	);
}
