import { Plus } from "lucide-react";
import { useEffect, useState } from "react";
import type { PageFormat } from "@/utils/apiService";
import { Button } from "../ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";

interface PageInsertToolbarProps {
	onAddPage: (format: string) => void;
	apiFormats: PageFormat[];
}

export function PageInsertToolbar({
	onAddPage,
	apiFormats = [],
}: PageInsertToolbarProps) {
	const [format, setFormat] = useState("");

	// Use API formats to set default format
	useEffect(() => {
		if (apiFormats && apiFormats.length > 0) {
			setFormat(apiFormats[0].name);
		} else {
			// If no API formats, default to A4
			setFormat("A4");
		}
	}, [apiFormats]);

	return (
		<div className="flex items-center justify-center my-4 space-x-3">
			<div className="w-52">
				<Select value={format} onValueChange={setFormat}>
					<SelectTrigger>
						<SelectValue placeholder="Format" />
					</SelectTrigger>
					<SelectContent>
						{apiFormats.map((pageFormat) => (
							<SelectItem key={pageFormat.name} value={pageFormat.name}>
								{pageFormat.description}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			<Button
				className="whitespace-nowrap"
				onClick={() => onAddPage(format)}
				disabled={!format}
			>
				<Plus className="w-4 h-4 mr-2" />
				Seite hinzufügen
			</Button>
		</div>
	);
}
