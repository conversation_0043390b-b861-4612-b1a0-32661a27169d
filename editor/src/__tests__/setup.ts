import "@testing-library/jest-dom";
import { cleanup } from "@testing-library/react";
import { afterAll, afterEach, beforeAll, vi } from "vitest";
import "./utils/custom-matchers";

// Global test setup
beforeAll(() => {
	// Mock window.matchMedia
	Object.defineProperty(window, "matchMedia", {
		writable: true,
		value: vi.fn().mockImplementation((query) => ({
			matches: false,
			media: query,
			onchange: null,
			addListener: vi.fn(), // deprecated
			removeListener: vi.fn(), // deprecated
			addEventListener: vi.fn(),
			removeEventListener: vi.fn(),
			dispatchEvent: vi.fn(),
		})),
	});

	// Mock ResizeObserver
	global.ResizeObserver = vi.fn().mockImplementation(() => ({
		observe: vi.fn(),
		unobserve: vi.fn(),
		disconnect: vi.fn(),
	}));

	// Mock IntersectionObserver
	global.IntersectionObserver = vi.fn().mockImplementation(() => ({
		observe: vi.fn(),
		unobserve: vi.fn(),
		disconnect: vi.fn(),
	}));

	// Mock HTMLCanvasElement.getContext
	HTMLCanvasElement.prototype.getContext = vi.fn().mockReturnValue({
		fillRect: vi.fn(),
		clearRect: vi.fn(),
		getImageData: vi.fn(() => ({
			data: new Uint8ClampedArray(4),
		})),
		putImageData: vi.fn(),
		createImageData: vi.fn(() => []),
		setTransform: vi.fn(),
		drawImage: vi.fn(),
		save: vi.fn(),
		fillText: vi.fn(),
		restore: vi.fn(),
		beginPath: vi.fn(),
		moveTo: vi.fn(),
		lineTo: vi.fn(),
		closePath: vi.fn(),
		stroke: vi.fn(),
		translate: vi.fn(),
		scale: vi.fn(),
		rotate: vi.fn(),
		arc: vi.fn(),
		fill: vi.fn(),
		measureText: vi.fn(() => ({ width: 0 })),
		transform: vi.fn(),
		rect: vi.fn(),
		clip: vi.fn(),
	});

	// Mock URL.createObjectURL
	global.URL.createObjectURL = vi.fn(() => "mocked-url");
	global.URL.revokeObjectURL = vi.fn();

	// Mock localStorage
	const localStorageMock = {
		getItem: vi.fn(),
		setItem: vi.fn(),
		removeItem: vi.fn(),
		clear: vi.fn(),
	};
	Object.defineProperty(window, "localStorage", {
		value: localStorageMock,
	});

	// Mock sessionStorage
	const sessionStorageMock = {
		getItem: vi.fn(),
		setItem: vi.fn(),
		removeItem: vi.fn(),
		clear: vi.fn(),
	};
	Object.defineProperty(window, "sessionStorage", {
		value: sessionStorageMock,
	});

	// Mock window.getComputedStyle
	window.getComputedStyle = vi.fn().mockImplementation(() => ({
		getPropertyValue: vi.fn().mockReturnValue(""),
	}));

	// Mock Element.scrollIntoView
	Element.prototype.scrollIntoView = vi.fn();

	// Mock Element.getBoundingClientRect
	Element.prototype.getBoundingClientRect = vi.fn().mockReturnValue({
		width: 0,
		height: 0,
		top: 0,
		left: 0,
		bottom: 0,
		right: 0,
		x: 0,
		y: 0,
		toJSON: vi.fn(),
	});

	// Mock Range methods for text selection
	global.Range.prototype.getBoundingClientRect = vi.fn().mockReturnValue({
		width: 0,
		height: 0,
		top: 0,
		left: 0,
		bottom: 0,
		right: 0,
		x: 0,
		y: 0,
		toJSON: vi.fn(),
	});

	global.Range.prototype.getClientRects = vi.fn().mockReturnValue([]);
});

// Clean up after each test
afterEach(() => {
	cleanup();
	vi.clearAllMocks();
});

// Global cleanup
afterAll(() => {
	vi.restoreAllMocks();
});

// Custom error handler for unhandled promise rejections in tests
process.on("unhandledRejection", (reason) => {
	console.error("Unhandled promise rejection in test:", reason);
});
