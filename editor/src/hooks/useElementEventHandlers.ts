import { useCallback, useEffect, useRef } from "react";
import type { TableProperties } from "@/types/table";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import type { Element } from "../types/element";

interface UseElementEventHandlersProps {
	element: Element;
	onUpdate: (updatedElement: Element, isFinal: boolean) => void;
	onDelete: (elementId: string) => void;
	onCopy: (elementId: string) => void;
	onCut: (elementId: string) => void;
	_onSelect: (elementId: string) => void;
	isSelected: boolean;
	isEditing: boolean;
	setIsEditing: (editing: boolean) => void;
	isLocked: boolean;
}

export function useElementEventHandlers({
	element,
	onUpdate,
	onDelete,
	onCopy,
	onCut,
	isSelected,
	isEditing,
	setIsEditing,
	isLocked,
}: UseElementEventHandlersProps) {
	// Ref to store initial content when entering text edit mode
	const initialContentRef = useRef<string>(element.content || "");
	// eslint-disable-next-line react-hooks/exhaustive-deps
	const handleTextEditStart = useCallback(() => {
		// Store initial content
		initialContentRef.current = element.content || "";
		setIsEditing(true);
		// No immediate history entry for text edit start
	}, [element.content, setIsEditing]);

	const handleTextChange = useCallback(
		(content: string) => {
			// Preserve the verticalAlign property when updating content
			onUpdate(
				{
					...element,
					content,
					// Keep the existing verticalAlign property if it exists
					verticalAlign: element.verticalAlign,
				},
				false,
			); // Intermediate update
		},
		[element, onUpdate],
	);

	// eslint-disable-next-line react-hooks/exhaustive-deps
	const handleTextEditEnd = useCallback(() => {
		// Only add to history if content actually changed
		const currentContent = element.content || "";
		if (currentContent !== initialContentRef.current) {
			onUpdate(
				{
					...element,
					// Preserve verticalAlign
					verticalAlign: element.verticalAlign,
				},
				true,
			); // Save final state
		}
		setIsEditing(false);
	}, [element, onUpdate, setIsEditing]);

	const handleTableEditStart = useCallback(() => {
		let elementToEdit = { ...element };

		// Adjust internal dimensions to match current visual size before entering edit mode
		if (
			element.tableProperties?.columnWidths &&
			element.tableProperties.rowHeights
		) {
			const intrinsicWidthMm = element.tableProperties.columnWidths.reduce(
				(sum, w) => sum + w,
				0,
			);
			const intrinsicHeightMm = element.tableProperties.rowHeights.reduce(
				(sum, h) => sum + h,
				0,
			);

			let scaleX = 1;
			if (intrinsicWidthMm > 0 && element.width > 0) {
				scaleX = element.width / intrinsicWidthMm;
			}
			let scaleY = 1;
			if (intrinsicHeightMm > 0 && element.height > 0) {
				scaleY = element.height / intrinsicHeightMm;
			}

			// Only scale if necessary and scale factors are reasonable
			if (
				(scaleX !== 1 || scaleY !== 1) &&
				Number.isFinite(scaleX) &&
				scaleX > 0 &&
				Number.isFinite(scaleY) &&
				scaleY > 0
			) {
				console.log(
					`[useElementEventHandlers handleTableEditStart] Scaling internal dims. Element W/H: ${element.width}/${element.height}, Intrinsic W/H: ${intrinsicWidthMm}/${intrinsicHeightMm}, Scale X/Y: ${scaleX}/${scaleY}`,
				);

				const scaledColumnWidths = element.tableProperties.columnWidths.map(
					(w) => roundToTwoDecimals(w * scaleX),
				);
				const scaledRowHeights = element.tableProperties.rowHeights.map((h) =>
					roundToTwoDecimals(h * scaleY),
				);

				// We checked element.tableProperties earlier, so it should exist
				const currentProps = elementToEdit.tableProperties;
				if (!currentProps) {
					console.error("Table properties not found");
					return;
				}

				elementToEdit = {
					...elementToEdit,
					tableProperties: {
						// Explicitly include all required properties
						rows: currentProps.rows,
						columns: currentProps.columns,
						borderWidth: currentProps.borderWidth,
						borderStyle: currentProps.borderStyle,
						cells: currentProps.cells,
						// Apply scaled dimensions
						columnWidths: scaledColumnWidths,
						rowHeights: scaledRowHeights,
					},
				};
			}
		}

		// Save the potentially adjusted state (without adding to history yet)
		// This ensures TableEditor receives the correctly scaled internal dimensions
		onUpdate(elementToEdit, false);
		setIsEditing(true);
	}, [element, onUpdate, setIsEditing]);

	const handleTableChange = useCallback(
		(updatedTableProperties: TableProperties) => {
			// When column/row sizes change, the total dimensions need to be recalculated
			const newIntrinsicWidth =
				updatedTableProperties.columnWidths?.reduce((sum, w) => sum + w, 0) ||
				element.width;
			const newIntrinsicHeight =
				updatedTableProperties.rowHeights?.reduce((sum, h) => sum + h, 0) ||
				element.height;

			const widthToApply =
				newIntrinsicWidth > 0 ? newIntrinsicWidth : element.width;
			const heightToApply =
				newIntrinsicHeight > 0 ? newIntrinsicHeight : element.height;

			console.log(
				`[handleTableChange] Updating element ${element.id}. New W/H: ${widthToApply}/${heightToApply}`,
			);
			onUpdate(
				{
					...element,
					tableProperties: updatedTableProperties,
					width: widthToApply,
					height: heightToApply,
				},
				true,
			);
		},
		[element, onUpdate],
	);

	const handleTableEditEnd = useCallback(() => {
		// When exiting table edit mode, clear any lingering cell selection so that other UI (e.g., sidebar)
		// doesn't incorrectly treat the table as if a cell were still selected.
		const cleanedElement: Element = element.tableProperties
			? {
					...element,
					tableProperties: {
						...element.tableProperties,
						selection: null,
					},
				}
			: element;

		// Persist final table state (without selection) to history
		onUpdate(cleanedElement, true);
		setIsEditing(false);
	}, [element, onUpdate, setIsEditing]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLDivElement>) => {
			// Handle keyboard shortcuts
			if (e.key === "Delete" || e.key === "Backspace") {
				if (!isEditing && document.activeElement === e.currentTarget) {
					// If globally locked or address field, prevent element deletion
					if (isLocked || element.isAddressField) {
						e.preventDefault();
						e.stopPropagation();
						console.log(
							"[useElementEventHandlers] Blocked element deletion for locked/address field element:",
							element.id,
						);
						return;
					}
					e.stopPropagation();
					onDelete(element.id);
				}
				// Allow Delete/Backspace for text editing when isEditing is true
			} else if ((e.ctrlKey || e.metaKey) && e.key === "c") {
				if (!isEditing && document.activeElement === e.currentTarget) {
					// If address field, prevent copying the element
					if (element.isAddressField) {
						e.preventDefault();
						e.stopPropagation();
						console.log(
							"[useElementEventHandlers] Blocked copy for address field element:",
							element.id,
						);
						return;
					}
					e.stopPropagation();
					onCopy(element.id);
				}
				// Allow Ctrl+C for text copying when editing
			} else if ((e.ctrlKey || e.metaKey) && e.key === "x") {
				if (!isEditing && document.activeElement === e.currentTarget) {
					// If globally locked or address field, prevent cutting
					if (isLocked || element.isAddressField) {
						e.preventDefault();
						e.stopPropagation();
						console.log(
							"[useElementEventHandlers] Blocked cut for locked/address field element:",
							element.id,
						);
						return;
					}
					e.stopPropagation();
					onCut(element.id);
				}
				// Allow Ctrl+X for text cutting when editing
			} else if (e.key === "Enter") {
				// Enter starts editing for selectable elements when not already editing
				if (
					!isEditing &&
					isSelected &&
					document.activeElement === e.currentTarget
				) {
					e.preventDefault();
					e.stopPropagation();
					if (element.type === "text") {
						handleTextEditStart();
					} else if (element.type === "table") {
						handleTableEditStart();
					}
				}
			} else if (
				["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)
			) {
				// Handle arrow keys for fine positioning
				if (
					!isEditing &&
					isSelected &&
					document.activeElement === e.currentTarget
				) {
					// If globally locked or address field, prevent movement
					if (isLocked || element.isAddressField) {
						e.preventDefault();
						e.stopPropagation();
						console.log(
							"[useElementEventHandlers] Blocked element movement for locked/address field element:",
							element.id,
						);
						return;
					}

					e.preventDefault();
					e.stopPropagation();

					// Determine movement step size based on modifier keys
					let stepSize = 0.5; // Default: 0.5mm
					if (e.shiftKey) {
						stepSize = 10; // Shift: 10mm for larger movements
					} else if (e.ctrlKey || e.metaKey) {
						stepSize = 0.1; // Ctrl/Cmd: 0.1mm for fine movements
					}

					let newX = element.x;
					let newY = element.y;

					switch (e.key) {
						case "ArrowUp":
							newY = roundToTwoDecimals(element.y - stepSize);
							break;
						case "ArrowDown":
							newY = roundToTwoDecimals(element.y + stepSize);
							break;
						case "ArrowLeft":
							newX = roundToTwoDecimals(element.x - stepSize);
							break;
						case "ArrowRight":
							newX = roundToTwoDecimals(element.x + stepSize);
							break;
					}

					// Update element position
					onUpdate(
						{
							...element,
							x: newX,
							y: newY,
						},
						true,
					); // Final update for undo/redo history
				}
			} else if (e.key === "Escape") {
				// Escape ends editing mode if currently editing
				if (isEditing) {
					e.preventDefault();
					e.stopPropagation();
					if (element.type === "text") {
						handleTextEditEnd();
					} else if (element.type === "table") {
						handleTableEditEnd();
					}
				}
			}
		},
		[
			isEditing,
			isLocked,
			element,
			onDelete,
			onCopy,
			onCut,
			isSelected,
			onUpdate,
			handleTextEditStart,
			handleTableEditStart,
			handleTextEditEnd,
			handleTableEditEnd,
		],
	);

	// Effect to exit edit mode when the element is deselected
	useEffect(() => {
		if (!isSelected && isEditing) {
			console.log(
				`[useElementEventHandlers] Deselected while editing. Element ID: ${element.id}, Type: ${element.type}`,
			);
			if (element.type === "text") {
				handleTextEditEnd();
			} else if (element.type === "table") {
				handleTableEditEnd();
			}
		}
	}, [
		isSelected,
		isEditing,
		element.type,
		element.id,
		handleTextEditEnd,
		handleTableEditEnd,
	]);

	// Provide Escape key handling while in editing mode (focus may be inside a rich text editor)
	useEffect(() => {
		if (!isEditing) return;

		const handleEsc = (e: KeyboardEvent) => {
			if (e.key !== "Escape") return;

			e.preventDefault();
			e.stopPropagation();

			if (element.type === "text") {
				handleTextEditEnd();
			} else if (element.type === "table") {
				handleTableEditEnd();
			}
		};

		window.addEventListener("keydown", handleEsc);
		return () => window.removeEventListener("keydown", handleEsc);
	}, [isEditing, element.type, handleTextEditEnd, handleTableEditEnd]);

	return {
		handleTextEditStart,
		handleTextChange,
		handleTextEditEnd,
		handleTableEditStart,
		handleTableChange,
		handleTableEditEnd,
		handleKeyDown,
	};
}
