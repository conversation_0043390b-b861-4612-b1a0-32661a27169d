import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import type { PageFormat, PageOrientation } from "@/types/page";

interface AddPageDialogProps {
	onAddPage: (format: PageFormat, orientation: PageOrientation) => void;
}

export function AddPageDialog({ onAddPage }: AddPageDialogProps) {
	const [open, setOpen] = useState(false);
	const [format, setFormat] = useState<PageFormat>("A4");
	const [orientation, setOrientation] = useState<PageOrientation>("portrait");

	const handleAddPage = () => {
		onAddPage(format, orientation);
		setOpen(false); // Schließe das Dialog nach dem Hinzufügen
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button variant="outline">Neue Seite hinzufügen</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Neue Seite erstellen</DialogTitle>
				</DialogHeader>
				<div className="grid gap-4 py-4">
					<Select
						onValueChange={(value: PageFormat) => setFormat(value)}
						defaultValue={format}
					>
						<SelectTrigger>
							<SelectValue placeholder="Format wählen" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="A4">A4</SelectItem>
							<SelectItem value="A5">A5</SelectItem>
							<SelectItem value="POSTCARD">Postkarte</SelectItem>
						</SelectContent>
					</Select>

					<Select
						onValueChange={(value: PageOrientation) => setOrientation(value)}
						defaultValue={orientation}
					>
						<SelectTrigger>
							<SelectValue placeholder="Ausrichtung wählen" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="portrait">Hochformat</SelectItem>
							<SelectItem value="landscape">Querformat</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<Button onClick={handleAddPage}>Seite hinzufügen</Button>
			</DialogContent>
		</Dialog>
	);
}
