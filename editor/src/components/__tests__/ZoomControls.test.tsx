import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";

import { ZoomControls } from "../ZoomControls";

describe("ZoomControls component", () => {
	const mockOnZoomChange = vi.fn();

	beforeEach(() => {
		mockOnZoomChange.mockReset();
	});

	it("renders with current zoom level", () => {
		render(<ZoomControls zoomLevel={1} onZoomChange={mockOnZoomChange} />);

		expect(screen.getByText("100%")).toBeInTheDocument();

		// Find buttons by their aria-label attributes instead of title
		expect(screen.getByLabelText("Zoom Out")).toBeInTheDocument();
		expect(screen.getByLabelText("Zoom In")).toBeInTheDocument();
		expect(screen.getByLabelText("Reset Zoom")).toBeInTheDocument();

		// Find range input
		expect(screen.getByRole("slider")).toBeInTheDocument();
	});

	it("displays correct percentage for different zoom levels", () => {
		const { rerender } = render(
			<ZoomControls zoomLevel={0.5} onZoomChange={mockOnZoomChange} />,
		);

		expect(screen.getByText("50%")).toBeInTheDocument();

		rerender(<ZoomControls zoomLevel={1.5} onZoomChange={mockOnZoomChange} />);
		expect(screen.getByText("150%")).toBeInTheDocument();

		rerender(<ZoomControls zoomLevel={2} onZoomChange={mockOnZoomChange} />);
		expect(screen.getByText("200%")).toBeInTheDocument();
	});

	it("calls onZoomChange when zoom out button is clicked", async () => {
		const user = userEvent.setup();
		render(<ZoomControls zoomLevel={1} onZoomChange={mockOnZoomChange} />);

		const zoomOutButton = screen.getByLabelText("Zoom Out");
		await user.click(zoomOutButton);

		expect(mockOnZoomChange).toHaveBeenCalledTimes(1);
		expect(mockOnZoomChange).toHaveBeenCalledWith(0.9);
	});

	it("calls onZoomChange when zoom in button is clicked", async () => {
		const user = userEvent.setup();
		render(<ZoomControls zoomLevel={1} onZoomChange={mockOnZoomChange} />);

		const zoomInButton = screen.getByLabelText("Zoom In");
		await user.click(zoomInButton);

		expect(mockOnZoomChange).toHaveBeenCalledTimes(1);
		expect(mockOnZoomChange).toHaveBeenCalledWith(1.1);
	});

	it("calls onZoomChange when reset zoom button is clicked", async () => {
		const user = userEvent.setup();
		render(<ZoomControls zoomLevel={1.5} onZoomChange={mockOnZoomChange} />);

		const resetButton = screen.getByLabelText("Reset Zoom");
		await user.click(resetButton);

		expect(mockOnZoomChange).toHaveBeenCalledTimes(1);
		expect(mockOnZoomChange).toHaveBeenCalledWith(1);
	});

	it("calls onZoomChange when range input is changed", async () => {
		render(<ZoomControls zoomLevel={1} onZoomChange={mockOnZoomChange} />);

		const rangeInput = screen.getByRole("slider");

		// Directly trigger the change event since typing into a range input doesn't work well in tests
		fireEvent.change(rangeInput, { target: { value: "1.5" } });

		expect(mockOnZoomChange).toHaveBeenCalled();
		expect(mockOnZoomChange).toHaveBeenCalledWith(1.5);
	});

	it("respects minimum zoom level", async () => {
		const user = userEvent.setup();
		render(<ZoomControls zoomLevel={0.5} onZoomChange={mockOnZoomChange} />);

		const zoomOutButton = screen.getByLabelText("Zoom Out");
		await user.click(zoomOutButton);

		// Should not go below 0.5
		expect(mockOnZoomChange).toHaveBeenCalledWith(0.5);
	});

	it("respects maximum zoom level", async () => {
		const user = userEvent.setup();
		render(<ZoomControls zoomLevel={2} onZoomChange={mockOnZoomChange} />);

		const zoomInButton = screen.getByLabelText("Zoom In");
		await user.click(zoomInButton);

		// Should not go above 2
		expect(mockOnZoomChange).toHaveBeenCalledWith(2);
	});

	it("applies custom className", () => {
		render(
			<ZoomControls
				zoomLevel={1}
				onZoomChange={mockOnZoomChange}
				className="custom-class"
			/>,
		);

		const container = screen.getByText("100%").closest("div");
		expect(container).toHaveClass("custom-class");
	});
});
