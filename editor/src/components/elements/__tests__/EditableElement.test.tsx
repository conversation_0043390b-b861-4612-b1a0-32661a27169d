import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";

import type { Element } from "@/types/element";
import { EditableElement } from "../EditableElement";

// Mock the hooks used by EditableElement
vi.mock("@/hooks/useElementEventHandlers", () => ({
	useElementEventHandlers: () => ({
		handleTextEditStart: vi.fn(),
		handleTextChange: vi.fn(),
		handleTextEditEnd: vi.fn(),
		handleTableEditStart: vi.fn(),
		handleTableChange: vi.fn(),
		handleTableEditEnd: vi.fn(),
		handleKeyDown: vi.fn(),
	}),
}));

vi.mock("@/hooks/useElementTransform", () => ({
	useElementTransform: () => ({
		isDragging: false,
		isResizing: false,
		activeHandle: null,
		handleDragStart: vi.fn(),
		handleResizeStart: vi.fn(),
	}),
}));

// Mock the utility functions used for styles
vi.mock("@/utils/elementStyles", () => ({
	calculateElementZIndex: () => 6000,
	calculateElementOpacity: () => 1,
	getElementInteractivity: () => ({
		isGenerallyInteractive: true,
		disableSelfInteractionForBlockInEditMode: false,
		isAddressField: false,
	}),
	getElementPointerEvents: (isExportMode, isLocked) =>
		isExportMode || isLocked ? "none" : "auto",
	getElementCursorStyle: (isLocked) =>
		isLocked ? "cursor-default" : "cursor-move",
	getElementPositionStyles: (element) => ({
		left: `calc(${element.x}mm - 0mm)`,
		top: `calc(${element.y}mm - 0mm)`,
		width: `calc(${element.width}mm + 0mm)`,
		height: `calc(${element.height}mm + 0mm)`,
		transform: `rotate(${element.rotation}deg)`,
		transformOrigin: "center center",
	}),
	getTextElementStyles: () => ({}),
	getShapeWrapperStyles: () => ({}),
	getImageStyles: () => ({}),
}));

// Mock the API service to prevent image loading errors
vi.mock("@/utils/apiService", () => ({
	fetchImageAsObjectURL: vi.fn().mockResolvedValue(null),
	revokeImageObjectURL: vi.fn(),
	processContentWithVariables: vi.fn((params) => params.content),
}));

// Mock the child element components
vi.mock("../TextElement", () => ({
	TextElement: () => <div data-testid="text-element-mock">Text Element</div>,
}));

vi.mock("../ShapeElement", () => ({
	ShapeElement: () => <div data-testid="shape-element-mock">Shape Element</div>,
}));

vi.mock("../ImageElement", () => ({
	ImageElement: () => <div data-testid="image-element-mock">Image Element</div>,
}));

vi.mock("../TableElement", () => ({
	TableElement: () => <div data-testid="table-element-mock">Table Element</div>,
}));

vi.mock("../BlockElement", () => ({
	BlockElement: () => <div data-testid="block-element-mock">Block Element</div>,
}));

vi.mock("../AddressElement", () => ({
	AddressElement: () => (
		<div data-testid="address-element-mock">Address Element</div>
	),
}));

vi.mock("../ControlElements", () => ({
	ControlElements: () => (
		<div data-testid="control-elements-mock">Control Elements</div>
	),
}));

describe("EditableElement component", () => {
	// Common props for all tests
	const mockOnUpdate = vi.fn();
	const mockOnDelete = vi.fn();
	const mockOnCopy = vi.fn();
	const mockOnCut = vi.fn();
	const mockOnSelect = vi.fn();
	const mockSetActiveEditor = vi.fn();

	// Base element for testing
	const baseElement: Element = {
		id: "element-1",
		currentPageId: "page-1",
		type: "text",
		x: 10,
		y: 20,
		width: 100,
		height: 50,
		rotation: 0,
		content: "<p>Test content</p>",
	};

	it("renders TextElement when element type is text", () => {
		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		expect(screen.getByTestId("text-element-mock")).toBeInTheDocument();
		expect(screen.getByTestId("control-elements-mock")).toBeInTheDocument();
	});

	it("renders ShapeElement when element type is shape", () => {
		const shapeElement = { ...baseElement, type: "shape" as const };

		render(
			<EditableElement
				element={shapeElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		expect(screen.getByTestId("shape-element-mock")).toBeInTheDocument();
	});

	it("renders ImageElement when element type is image", () => {
		const imageElement = { ...baseElement, type: "image" as const };

		render(
			<EditableElement
				element={imageElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		expect(screen.getByTestId("image-element-mock")).toBeInTheDocument();
	});

	it("renders TableElement when element type is table", () => {
		const tableElement = {
			...baseElement,
			type: "table" as const,
			tableProperties: {
				rows: 3,
				columns: 3,
				borderWidth: 1,
				borderStyle: "solid",
				cells: [
					[
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
					[
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
					[
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
				],
				columnWidths: [20, 20, 20],
				rowHeights: [10, 10, 10],
			},
		};

		render(
			<EditableElement
				element={tableElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		expect(screen.getByTestId("table-element-mock")).toBeInTheDocument();
	});

	it("renders BlockElement when element type is block", () => {
		const blockElement = {
			...baseElement,
			type: "block" as const,
			childElementIds: ["child-1", "child-2"],
		};

		render(
			<EditableElement
				element={blockElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
				childElements={[]}
			/>,
		);

		expect(screen.getByTestId("block-element-mock")).toBeInTheDocument();
	});

	it("renders AddressElement when element type is address", () => {
		const addressElement = { ...baseElement, type: "address" as const };

		render(
			<EditableElement
				element={addressElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		expect(screen.getByTestId("address-element-mock")).toBeInTheDocument();
	});

	it("applies correct styles based on element properties", () => {
		const styledElement = {
			...baseElement,
			color: "#ff0000",
			rotation: 45,
		};

		render(
			<EditableElement
				element={styledElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const elementContainer = screen.getByRole("application");
		expect(elementContainer.style.color).toBe("rgb(255, 0, 0)");
		expect(elementContainer.style.transform).toContain("rotate(45deg)");
	});

	it("applies correct position styles", () => {
		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const elementContainer = screen.getByRole("application");
		// The browser may convert mm to px, so we'll just check that the style properties exist
		expect(elementContainer.style.left).toBeTruthy();
		expect(elementContainer.style.top).toBeTruthy();
		expect(elementContainer.style.width).toBeTruthy();
		expect(elementContainer.style.height).toBeTruthy();
	});

	it("calls onSelect when clicked", async () => {
		const user = userEvent.setup();

		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const elementContainer = screen.getByRole("application");
		await user.click(elementContainer);

		expect(mockOnSelect).toHaveBeenCalledWith("element-1");
	});

	it("applies locked state correctly", () => {
		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
				isLocked={true}
			/>,
		);

		const elementContainer = screen.getByRole("application");
		expect(elementContainer).toHaveClass("cursor-default");
		expect(elementContainer.style.pointerEvents).toBe("none");
	});

	it("applies selected state correctly", () => {
		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={true}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		// Selected elements have a higher z-index
		const elementContainer = screen.getByRole("application");
		expect(elementContainer.style.zIndex).toBe("6000"); // High z-index for selected elements
	});

	it("applies export mode correctly", () => {
		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
				isExportMode={true}
			/>,
		);

		const elementContainer = screen.getByRole("application");
		expect(elementContainer.style.pointerEvents).toBe("none");
		expect(elementContainer).not.toHaveAttribute("tabIndex");
	});

	it("stores element data attributes", () => {
		render(
			<EditableElement
				element={baseElement}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
				onCopy={mockOnCopy}
				onCut={mockOnCut}
				isSelected={false}
				onSelect={mockOnSelect}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const elementContainer = screen.getByRole("application");
		expect(elementContainer).toHaveAttribute("data-element-id", "element-1");

		// Check that data-element-data contains the expected JSON
		const dataAttr = elementContainer.getAttribute("data-element-data");
		expect(dataAttr).not.toBeNull();

		const parsedData = JSON.parse(dataAttr || "{}");
		expect(parsedData).toEqual({
			id: "element-1",
			currentPageId: "page-1",
			parentId: null,
			x: 10,
			y: 20,
			width: 100,
			height: 50,
		});
	});
});
