import { useCallback, useRef, useState } from "react";
import type { ImageClipboard, ImageWithFolders } from "../types/image";

/**
 * Custom hook for managing image clipboard operations (copy/cut/paste)
 */
export function useImageClipboard() {
	const [clipboard, setClipboard] = useState<ImageClipboard | null>(null);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Clear clipboard after a certain time to prevent stale operations
	const CLIPBOARD_TIMEOUT = 5 * 60 * 1000; // 5 minutes

	const clearClipboard = useCallback(() => {
		setClipboard(null);
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
	}, []);

	const setClipboardWithTimeout = useCallback(
		(newClipboard: ImageClipboard) => {
			// Clear existing timeout
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			setClipboard(newClipboard);

			// Set new timeout
			timeoutRef.current = setTimeout(() => {
				clearClipboard();
			}, CLIPBOARD_TIMEOUT);
		},
		[clearClipboard],
	);

	/**
	 * Copy images to clipboard
	 */
	const copyImages = useCallback(
		(images: ImageWithFolders[]) => {
			if (images.length === 0) return;

			const clipboardData: ImageClipboard = {
				images,
				operation: "copy",
				timestamp: Date.now(),
			};

			setClipboardWithTimeout(clipboardData);
		},
		[setClipboardWithTimeout],
	);

	/**
	 * Cut images to clipboard
	 */
	const cutImages = useCallback(
		(images: ImageWithFolders[]) => {
			if (images.length === 0) return;

			const clipboardData: ImageClipboard = {
				images,
				operation: "cut",
				timestamp: Date.now(),
			};

			setClipboardWithTimeout(clipboardData);
		},
		[setClipboardWithTimeout],
	);

	/**
	 * Check if clipboard has data
	 */
	const hasClipboardData = useCallback(() => {
		return clipboard !== null && clipboard.images.length > 0;
	}, [clipboard]);

	/**
	 * Check if clipboard has copy operation
	 */
	const hasCopyData = useCallback(() => {
		return clipboard?.operation === "copy" && clipboard.images.length > 0;
	}, [clipboard]);

	/**
	 * Check if clipboard has cut operation
	 */
	const hasCutData = useCallback(() => {
		return clipboard?.operation === "cut" && clipboard.images.length > 0;
	}, [clipboard]);

	/**
	 * Get clipboard data for paste operation
	 */
	const getClipboardData = useCallback(() => {
		return clipboard;
	}, [clipboard]);

	/**
	 * Check if an image is in the cut clipboard (for visual styling)
	 */
	const isImageCut = useCallback(
		(imageUrl: string) => {
			if (!clipboard || clipboard.operation !== "cut") return false;
			return clipboard.images.some((img) => img.image === imageUrl);
		},
		[clipboard],
	);

	/**
	 * Get the number of images in clipboard
	 */
	const getClipboardCount = useCallback(() => {
		return clipboard?.images.length || 0;
	}, [clipboard]);

	/**
	 * Check if clipboard data is recent (not stale)
	 */
	const isClipboardFresh = useCallback(() => {
		if (!clipboard) return false;
		const timeDiff = Date.now() - clipboard.timestamp;
		return timeDiff < CLIPBOARD_TIMEOUT;
	}, [clipboard]);

	return {
		// Actions
		copyImages,
		cutImages,
		clearClipboard,

		// Getters
		getClipboardData,
		getClipboardCount,

		// Status checks
		hasClipboardData,
		hasCopyData,
		hasCutData,
		isImageCut,
		isClipboardFresh,

		// Raw clipboard data (for debugging or advanced use)
		clipboard: clipboard && isClipboardFresh() ? clipboard : null,
	};
}
