# Development Guide

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm package manager
- Modern web browser with ES2020+ support

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd editor

# Install dependencies
npm install

# Start development server
npm run dev
```

### Development Scripts
```bash
npm run build        # Build for production
npm run dev-build    # Build for development in watch mode
npm run lint         # Run linting
npm run test         # Run unit tests
npm run test:playwright  # Run e2e tests
```

## Project Structure

### Source Organization
```
src/
├── components/          # React components
│   ├── elements/       # Element-specific components
│   │   ├── EditableElement.tsx
│   │   └── TextElement.tsx
│   ├── sidebar/        # Sidebar components
│   │   ├── Sidebar.tsx
│   │   └── PropertyPanel.tsx
│   └── ui/            # Reusable UI components
├── hooks/              # Custom React hooks
│   ├── useHistory.ts
│   ├── useElementOperations.ts
│   └── usePageOperations.ts
├── types/              # TypeScript definitions
│   ├── element.ts
│   ├── page.ts
│   └── api.ts
├── utils/              # Utility functions
│   ├── htmlExporter.ts
│   ├── unitConversion.ts
│   └── snapGuides.ts
├── services/           # API services
└── styles/             # CSS files
```

### Configuration Files
- `vite.config.ts` - Vite build configuration
- `tsconfig.json` - TypeScript configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `biome.json` - Code formatting and linting
- `playwright.config.ts` - E2E testing configuration

## Development Patterns

### Component Development

#### Creating New Components
1. Create component file in appropriate directory
2. Define TypeScript interfaces for props
3. Implement component with proper typing
4. Add to index file for easy imports

```typescript
// Example component structure
interface MyComponentProps {
  data: SomeType;
  onUpdate: (data: SomeType) => void;
}

export const MyComponent: React.FC<MyComponentProps> = ({
  data,
  onUpdate
}) => {
  // Component implementation
  return <div>{/* JSX */}</div>;
};
```

#### Component Guidelines
- Use functional components with hooks
- Implement proper TypeScript typing
- Follow naming conventions (PascalCase)
- Include JSDoc comments for complex components
- Use memo() for performance-critical components

### State Management

#### Custom Hooks Pattern
Create custom hooks for complex state logic:

```typescript
export function useMyFeature() {
  const [state, setState] = useState(initialState);
  
  const handleOperation = useCallback((data) => {
    // Operation logic
    setState(newState);
  }, []);
  
  return {
    state,
    handleOperation
  };
}
```

#### State Update Guidelines
- Always use immutable updates
- Validate state before updates
- Handle edge cases and errors
- Use batching for multiple updates
- Include proper TypeScript typing

### Element System Development

#### Adding New Element Types
1. Define element type in `types/element.ts`
2. Add rendering logic in `EditableElement.tsx`
3. Create element-specific components if needed
4. Update operation hooks for new type
5. Add tests for new functionality

```typescript
// 1. Add to ElementType union
export type ElementType = 
  | "text" 
  | "shape" 
  | "image" 
  | "newType";

// 2. Extend Element interface if needed
export interface Element {
  // ... existing properties
  newTypeProperty?: string;
}
```

#### Element Component Pattern
```typescript
interface ElementComponentProps {
  element: Element;
  isSelected: boolean;
  onUpdate: (element: Element) => void;
  // ... other props
}

export const ElementComponent: React.FC<ElementComponentProps> = ({
  element,
  isSelected,
  onUpdate
}) => {
  // Element-specific rendering and interaction logic
};
```

### API Integration

#### Service Layer Pattern
Create service functions for API calls:

```typescript
// services/apiService.ts
export async function fetchData(): Promise<DataType[]> {
  const response = await fetch('/api/data');
  if (!response.ok) {
    throw new Error('Failed to fetch data');
  }
  return response.json();
}
```

#### Error Handling
- Use try-catch blocks for async operations
- Provide meaningful error messages
- Implement retry logic where appropriate
- Log errors for debugging

### Testing Strategy

#### Unit Testing
- Test individual functions and components
- Mock external dependencies
- Use React Testing Library for component tests
- Aim for high test coverage

```typescript
// Example test
import { render, screen } from '@testing-library/react';
import { MyComponent } from './MyComponent';

test('renders component correctly', () => {
  render(<MyComponent data={testData} />);
  expect(screen.getByText('Expected Text')).toBeInTheDocument();
});
```

#### E2E Testing
- Test complete user workflows
- Use Playwright for browser automation
- Test across different browsers
- Include accessibility testing

## Code Quality

### TypeScript Guidelines
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Avoid `any` type - use proper typing
- Use union types for constrained values
- Document complex types with JSDoc

### Code Style
- Use Biome for consistent formatting
- Follow React best practices
- Use meaningful variable names
- Keep functions small and focused
- Add comments for complex logic

### Performance Considerations
- Use React.memo for expensive components
- Implement proper dependency arrays in hooks
- Debounce frequent operations
- Lazy load components when possible
- Optimize re-rendering with useMemo/useCallback

## Debugging

### Development Tools
- React Developer Tools browser extension
- Redux DevTools for state inspection
- Browser developer tools
- TypeScript compiler for type checking

### Common Issues
1. **State not updating**: Check immutability of updates
2. **Performance issues**: Profile with React DevTools
3. **Type errors**: Review TypeScript configuration
4. **Build failures**: Check dependency versions

### Debugging Techniques
- Use console.log strategically
- Implement error boundaries
- Add debugging flags for development
- Use browser breakpoints
- Test with different data scenarios

## Deployment

### Build Process
```bash
# Production build
npm run build

# Build outputs to dist/ directory
# Static files ready for deployment
```

### Environment Configuration
- Use environment variables for API endpoints
- Configure different builds for environments
- Set up proper error reporting
- Configure analytics and monitoring

### Performance Optimization
- Enable gzip compression
- Use CDN for static assets
- Implement proper caching headers
- Monitor bundle size
- Use code splitting where beneficial

## Contributing

### Code Review Process
1. Create feature branch from main
2. Implement changes with tests
3. Run linting and tests locally
4. Submit pull request
5. Address review feedback
6. Merge after approval

### Commit Guidelines
- Use conventional commit messages
- Keep commits focused and atomic
- Include tests with new features
- Update documentation as needed

### Documentation
- Update README for new features
- Add JSDoc comments for public APIs
- Update type definitions
- Include examples in documentation