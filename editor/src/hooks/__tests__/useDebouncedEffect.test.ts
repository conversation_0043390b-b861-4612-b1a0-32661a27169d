import { renderHook } from "@testing-library/react";
import { vi } from "vitest";
import useDebouncedEffect from "../useDebouncedEffect";

// Mock setTimeout and clearTimeout
vi.useFakeTimers();

describe("useDebouncedEffect", () => {
	beforeEach(() => {
		vi.clearAllTimers();
	});

	it("should call the callback after the specified delay", () => {
		const callback = vi.fn();
		const delay = 500;

		renderHook(() => useDebouncedEffect(callback, delay, []));

		// Callback should not be called immediately
		expect(callback).not.toHaveBeenCalled();

		// Fast-forward time
		vi.advanceTimersByTime(delay);

		// Callback should be called after the delay
		expect(callback).toHaveBeenCalledTimes(1);
	});

	it("should debounce multiple calls within the delay period", () => {
		const callback = vi.fn();
		const delay = 500;

		const { rerender } = renderHook(
			({ cb, deps }) => useDebouncedEffect(cb, delay, deps),
			{ initialProps: { cb: callback, deps: [1] } },
		);

		// Change the dependency to trigger the effect again
		rerender({ cb: callback, deps: [2] });

		// Fast-forward time but not enough to trigger the first callback
		vi.advanceTimersByTime(300);

		// Callback should not be called yet
		expect(callback).not.toHaveBeenCalled();

		// Change the dependency again
		rerender({ cb: callback, deps: [3] });

		// Fast-forward time to trigger the latest callback
		vi.advanceTimersByTime(delay);

		// Only the latest callback should be called once
		expect(callback).toHaveBeenCalledTimes(1);
	});

	it("should clean up the timeout when unmounting", () => {
		const callback = vi.fn();
		const delay = 500;

		const { unmount } = renderHook(() =>
			useDebouncedEffect(callback, delay, []),
		);

		// Unmount before the delay
		unmount();

		// Fast-forward time
		vi.advanceTimersByTime(delay);

		// Callback should not be called after unmounting
		expect(callback).not.toHaveBeenCalled();
	});

	it("should use the latest callback even if it changes", () => {
		const initialCallback = vi.fn();
		const updatedCallback = vi.fn();
		const delay = 500;

		const { rerender } = renderHook(
			({ cb }) => useDebouncedEffect(cb, delay, []),
			{ initialProps: { cb: initialCallback } },
		);

		// Change the callback but keep the same dependencies
		rerender({ cb: updatedCallback });

		// Fast-forward time
		vi.advanceTimersByTime(delay);

		// The updated callback should be called, not the initial one
		expect(initialCallback).not.toHaveBeenCalled();
		expect(updatedCallback).toHaveBeenCalledTimes(1);
	});

	it("should reset the timer when dependencies change", () => {
		const callback = vi.fn();
		const delay = 500;

		const { rerender } = renderHook(
			({ deps }) => useDebouncedEffect(callback, delay, deps),
			{ initialProps: { deps: [1] } },
		);

		// Fast-forward time but not enough to trigger the callback
		vi.advanceTimersByTime(300);

		// Change the dependency to reset the timer
		rerender({ deps: [2] });

		// Fast-forward time but not enough to trigger the callback after reset
		vi.advanceTimersByTime(300);

		// Callback should not be called yet
		expect(callback).not.toHaveBeenCalled();

		// Fast-forward time to trigger the callback
		vi.advanceTimersByTime(200);

		// Callback should be called once
		expect(callback).toHaveBeenCalledTimes(1);
	});

	it("should reset the timer when delay changes", () => {
		const callback = vi.fn();
		const initialDelay = 500;

		const { rerender } = renderHook(
			({ delay }) => useDebouncedEffect(callback, delay, []),
			{ initialProps: { delay: initialDelay } },
		);

		// Fast-forward time but not enough to trigger the callback
		vi.advanceTimersByTime(300);

		// Change the delay to reset the timer
		const newDelay = 1000;
		rerender({ delay: newDelay });

		// Fast-forward time to what would have triggered the initial delay
		vi.advanceTimersByTime(200);

		// Callback should not be called yet because the delay was reset
		expect(callback).not.toHaveBeenCalled();

		// Fast-forward time to trigger the callback with the new delay
		vi.advanceTimersByTime(800);

		// Callback should be called once
		expect(callback).toHaveBeenCalledTimes(1);
	});
});
