import { useCallback, useEffect, useMemo } from "react";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

interface PageStateProps {
	pages: PageSettings[];
	elements: Element[];
	showPageNumbers: boolean;
	deletedPageNumberIds: Set<string>;
	setEditorState: (
		state: { pages: PageSettings[]; elements: Element[] },
		addToHistory?: boolean,
	) => void;
}

export function usePageState({
	pages,
	elements,
	showPageNumbers,
	deletedPageNumberIds,
	setEditorState,
}: PageStateProps) {
	// Function to toggle page numbers
	const handleTogglePageNumbers = useCallback(
		(
			show: boolean,
			setDeletedPageNumberIds: (ids: Set<string>) => void,
			setShowPageNumbers: (show: boolean) => void,
		) => {
			if (show) {
				// When turning page numbers on globally, reset individual deletions
				setDeletedPageNumberIds(new Set());
			}
			setShowPageNumbers(show);
		},
		[],
	);

	// Memoize page number configuration to avoid unnecessary recalculations
	const pageNumberConfig = useMemo(() => {
		return pages.map((p) => ({
			id: p.id,
			hasCoords:
				typeof p.pagenumber_x === "number" &&
				typeof p.pagenumber_y === "number",
			x: p.pagenumber_x,
			y: p.pagenumber_y,
		}));
	}, [pages]);

	// Track only page number elements to reduce dependency on all elements
	const pageNumberElements = useMemo(() => {
		return elements.filter((el) => el.isPageNumber);
	}, [elements]);

	// Effect to automatically update page numbers when pages change or toggle is active
	const updatePageNumbers = useCallback(() => {
		if (!showPageNumbers) {
			// If page numbers are turned off, ensure all are removed
			const nonPageNumberElements = elements.filter((el) => !el.isPageNumber);
			if (nonPageNumberElements.length !== elements.length) {
				setEditorState({ pages, elements: nonPageNumberElements }, true);
			}
			return;
		}

		// If page numbers are on, update/create them
		let actualPageNumber = 0;
		const workingElements = [...elements]; // Create a mutable copy to work with
		const requiredPageNumberElementIds = new Set<string>();

		pages.forEach((page) => {
			const pageId = page.id;
			const pageNumberElementId = `page-number-${pageId}`;
			requiredPageNumberElementIds.add(pageNumberElementId);

			// Check if this specific page number has been individually deleted
			if (deletedPageNumberIds.has(pageId)) {
				// Ensure it's removed if it exists (e.g., if it was re-added by a different logic path)
				const existingElementIndex = workingElements.findIndex(
					(el) => el.id === pageNumberElementId,
				);
				if (existingElementIndex !== -1) {
					workingElements.splice(existingElementIndex, 1);
				}
				return; // Skip creating/updating this page number
			}

			const hasCoords =
				typeof page.pagenumber_x === "number" &&
				typeof page.pagenumber_y === "number";
			if (!hasCoords) {
				// This page should not have a page number, remove if exists
				const existingElementIndex = workingElements.findIndex(
					(el) => el.id === pageNumberElementId,
				);
				if (existingElementIndex !== -1) {
					workingElements.splice(existingElementIndex, 1);
				}
				return;
			}

			// Calculate the actual page number (1-based, counting only pages with coordinates)
			actualPageNumber++;

			const xPos = (page.pagenumber_x ?? 0) * 10; // Convert cm to mm
			const yPos = (page.pagenumber_y ?? 0) * 10; // Convert cm to mm

			// Define page number element properties
			const elementWidthMm = 20; // Fixed width in mm
			const elementHeightMm = 6; // Fixed height in mm
			const newContent = `<p style="font-family: Arial, sans-serif; font-size: 12px; color: #666; margin: 0; text-align: left;">${actualPageNumber}</p>`;

			// Check if element already exists
			const existingElementIndex = workingElements.findIndex(
				(el) => el.id === pageNumberElementId,
			);

			if (existingElementIndex !== -1) {
				// Update existing element
				const existingElement = workingElements[existingElementIndex];
				const updatedElement: Element = {
					...existingElement,
					content: newContent,
					x: roundToTwoDecimals(xPos),
					y: roundToTwoDecimals(yPos),
					width: elementWidthMm,
					height: elementHeightMm,
				};
				workingElements[existingElementIndex] = updatedElement;
			} else {
				// Create new element
				const newElement: Element = {
					id: pageNumberElementId,
					type: "text",
					content: newContent,
					x: roundToTwoDecimals(xPos),
					y: roundToTwoDecimals(yPos),
					width: elementWidthMm,
					height: elementHeightMm,
					rotation: 0,
					currentPageId: pageId,
					isPageNumber: true,
					zIndex: 1000, // High z-index to ensure page numbers appear on top
				};
				workingElements.push(newElement);
			}
		});

		// Remove any page number elements that are no longer required
		const finalElements = workingElements.filter((el) => {
			if (el.isPageNumber && !requiredPageNumberElementIds.has(el.id)) {
				return false; // Remove this element
			}
			return true; // Keep this element
		});

		// Only update if there are actual changes
		if (
			finalElements.length !== elements.length ||
			finalElements.some((el, index) => el !== elements[index])
		) {
			setEditorState({ pages, elements: finalElements }, true);
		}
	}, [pages, elements, showPageNumbers, deletedPageNumberIds, setEditorState]);

	// Effect to run page number updates
	useEffect(() => {
		updatePageNumbers();
	}, [updatePageNumbers]);

	// Get the readable format name from the format ID
	const getFormatDescription = useCallback(
		(
			formatId: string,
			apiFormats: Array<{ name: string; description: string }>,
		): string => {
			if (apiFormats && apiFormats.length > 0) {
				const format = apiFormats.find((f) => f.name === formatId);
				if (format) {
					return format.description;
				}
			}
			// If format not found in API formats, create a readable name
			return formatId
				.replace("_", " ")
				.replace(/\b\w/g, (c) => c.toUpperCase());
		},
		[],
	);

	// Calculate page dimensions and trim values
	const getPageDimensions = useCallback((page: PageSettings) => {
		const trimLeftCm = page.trim_left ?? 0;
		const trimRightCm = page.trim_right ?? 0;
		const trimTopCm = page.trim_top ?? 0;
		const trimBottomCm = page.trim_bottom ?? 0;

		const outerWidthCm = page.width + trimLeftCm + trimRightCm;
		const outerHeightCm = page.height + trimTopCm + trimBottomCm;

		return {
			trimLeftCm,
			trimRightCm,
			trimTopCm,
			trimBottomCm,
			outerWidthCm,
			outerHeightCm,
		};
	}, []);

	// Check if a page should show page numbers
	const shouldShowPageNumber = useCallback(
		(page: PageSettings) => {
			return (
				showPageNumbers &&
				!deletedPageNumberIds.has(page.id) &&
				typeof page.pagenumber_x === "number" &&
				typeof page.pagenumber_y === "number"
			);
		},
		[showPageNumbers, deletedPageNumberIds],
	);

	// Calculate page number for display
	const getPageNumber = useCallback((pageIndex: number) => {
		return Math.floor(pageIndex / 2) + 1;
	}, []);

	// Determine if page is front or back
	const getPageType = useCallback(
		(pageIndex: number) => {
			const isFrontPage = pageIndex % 2 === 0;
			return {
				isFrontPage,
				isBackPage: !isFrontPage,
				isLastPageOfPhysicalPair:
					!isFrontPage || pageIndex === pages.length - 1,
			};
		},
		[pages.length],
	);

	return {
		handleTogglePageNumbers,
		pageNumberConfig,
		pageNumberElements,
		updatePageNumbers,
		getFormatDescription,
		getPageDimensions,
		shouldShowPageNumber,
		getPageNumber,
		getPageType,
	};
}
