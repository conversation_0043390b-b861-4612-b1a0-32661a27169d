import { useCallback, useEffect, useRef, useState } from "react";
import type { PageSettings } from "../types/page";

export interface PageDragState {
	isDragging: boolean;
	draggedPageIndex: number | null;
	dropTargetIndex: number | null;
	isDropValid: boolean;
	pointerX: number;
	pointerY: number;
}

interface UsePageDnDParams {
	pages: PageSettings[];
	onSwap: (fromIndex: number, toIndex: number) => void;
	isDisabled?: boolean;
}

/**
 * Pointer-events based drag-and-drop for *single* pages in the minimap.
 * The swap only happens when the source & target pages share identical
 * dimensions (width + height).
 */
export function usePageDragAndDrop({
	pages,
	onSwap,
	isDisabled = false,
}: UsePageDnDParams) {
	const [state, setState] = useState<PageDragState>({
		isDragging: false,
		draggedPageIndex: null,
		dropTargetIndex: null,
		isDropValid: false,
		pointerX: 0,
		pointerY: 0,
	});

	// Keep latest state in ref for global listeners
	const stateRef = useRef(state);
	stateRef.current = state;

	const finishDrag = useCallback(() => {
		const { draggedPageIndex, dropTargetIndex, isDropValid } = stateRef.current;
		if (
			draggedPageIndex !== null &&
			dropTargetIndex !== null &&
			isDropValid &&
			draggedPageIndex !== dropTargetIndex
		) {
			onSwap(draggedPageIndex, dropTargetIndex);
		}
		setState({
			isDragging: false,
			draggedPageIndex: null,
			dropTargetIndex: null,
			isDropValid: false,
			pointerX: 0,
			pointerY: 0,
		});
	}, [onSwap]);

	// Global pointerup / move listeners while dragging
	useEffect(() => {
		if (!state.isDragging) return;

		const handlePointerUp = () => finishDrag();
		const handlePointerMove = (e: PointerEvent) => {
			setState((prev) => ({
				...prev,
				pointerX: e.clientX,
				pointerY: e.clientY,
			}));
		};

		window.addEventListener("pointerup", handlePointerUp, { capture: true });
		window.addEventListener("pointermove", handlePointerMove, {
			capture: true,
		});

		return () => {
			window.removeEventListener("pointerup", handlePointerUp, {
				capture: true,
			});
			window.removeEventListener("pointermove", handlePointerMove, {
				capture: true,
			});
		};
	}, [state.isDragging, finishDrag]);

	// ---- API ----
	const pendingRef = useRef<boolean>(false);
	const startPosRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
	const getDragProps = useCallback(
		(pageIndex: number) => {
			return {
				onPointerDown: (e: React.PointerEvent) => {
					if (isDisabled) return;
					if (e.button !== 0) return;
					startPosRef.current = { x: e.clientX, y: e.clientY };
					pendingRef.current = true;
					window.addEventListener("pointermove", detectDrag);
					window.addEventListener("pointerup", cancelPending, { once: true });
					function detectDrag(evt: PointerEvent) {
						if (!pendingRef.current) return;
						const dx = Math.abs(evt.clientX - startPosRef.current.x);
						const dy = Math.abs(evt.clientY - startPosRef.current.y);
						if (dx > 4 || dy > 4) {
							pendingRef.current = false;
							window.removeEventListener("pointermove", detectDrag);
							setState({
								isDragging: true,
								draggedPageIndex: pageIndex,
								dropTargetIndex: null,
								isDropValid: false,
								pointerX: evt.clientX,
								pointerY: evt.clientY,
							});
						}
					}
					function cancelPending() {
						pendingRef.current = false;
						window.removeEventListener("pointermove", detectDrag);
					}
				},
				onPointerEnter: () => {
					if (!stateRef.current.isDragging) return;
					if (stateRef.current.draggedPageIndex === null) return;
					const sourceIdx = stateRef.current.draggedPageIndex;
					if (sourceIdx === pageIndex) {
						// entering itself – invalidate
						setState((prev) => ({
							...prev,
							dropTargetIndex: null,
							isDropValid: false,
						}));
						return;
					}
					const sameSize =
						pages[sourceIdx].width === pages[pageIndex].width &&
						pages[sourceIdx].height === pages[pageIndex].height;
					setState((prev) => ({
						...prev,
						dropTargetIndex: pageIndex,
						isDropValid: sameSize,
					}));
				},
				onPointerLeave: () => {
					if (!stateRef.current.isDragging) return;
					setState((prev) => ({
						...prev,
						dropTargetIndex: null,
						isDropValid: false,
					}));
				},
				tabIndex: 0,
				onKeyDown: (e: React.KeyboardEvent) => {
					if (isDisabled) return;
					if (stateRef.current.isDragging) return;
					if (e.key === " " || e.key === "Enter") {
						e.preventDefault();
						setState({
							isDragging: true,
							draggedPageIndex: pageIndex,
							dropTargetIndex: null,
							isDropValid: false,
							pointerX: 0,
							pointerY: 0,
						});
					}
				},
			} as const;
		},
		[isDisabled, pages],
	);

	// global cursor change
	useEffect(() => {
		if (state.isDragging) {
			document.body.style.cursor = "grabbing";
		} else {
			document.body.style.cursor = "";
		}
		return () => {
			document.body.style.cursor = "";
		};
	}, [state.isDragging]);

	return { dragState: state, getDragProps } as const;
}
