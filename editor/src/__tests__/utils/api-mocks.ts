import { vi } from "vitest";

// Network simulator for testing network conditions
export const NetworkSimulator = {
	delay: 0,
	networkError: false,
	failureRate: 0,

	setDelay(ms: number) {
		this.delay = ms;
	},

	setNetworkError(enabled: boolean) {
		this.networkError = enabled;
	},

	setFailureRate(rate: number) {
		this.failureRate = rate;
	},

	reset() {
		this.delay = 0;
		this.networkError = false;
		this.failureRate = 0;
	},

	async simulate() {
		if (this.networkError) {
			throw new Error("Network error");
		}

		if (Math.random() < this.failureRate) {
			throw new Error("API Error");
		}

		if (this.delay > 0) {
			await new Promise((resolve) => setTimeout(resolve, this.delay));
		}
	},
};

// API mocks with network simulation
export const ApiMocks = {
	async fetchAvailableImages() {
		await NetworkSimulator.simulate();
		return [
			{ id: "img1", url: "test1.jpg", name: "Test Image 1" },
			{ id: "img2", url: "test2.jpg", name: "Test Image 2" },
			{ id: "img3", url: "test3.jpg", name: "Test Image 3" },
		];
	},

	async fetchAvailableFonts() {
		await NetworkSimulator.simulate();
		return [
			{ name: "Arial", family: "sans-serif" },
			{ name: "Times New Roman", family: "serif" },
			{ name: "Courier New", family: "monospace" },
		];
	},

	async fetchAvailableColors() {
		await NetworkSimulator.simulate();
		return [
			{ name: "Red", value: "#FF0000" },
			{ name: "Green", value: "#00FF00" },
			{ name: "Blue", value: "#0000FF" },
		];
	},

	async fetchTemplates() {
		await NetworkSimulator.simulate();
		return [
			{ id: "template1", name: "Business Card", thumbnail: "thumb1.jpg" },
			{ id: "template2", name: "Flyer", thumbnail: "thumb2.jpg" },
		];
	},

	async saveTemplate(_template: unknown) {
		await NetworkSimulator.simulate();
		return { id: "new-template", success: true };
	},

	async fetchSessionStatus() {
		await NetworkSimulator.simulate();
		return { active: true, user: { id: "user1", name: "Test User" } };
	},

	reset() {
		// Reset any internal state if needed
	},
};

// API mock factory for creating custom mocks
export const createApiMocks = () => {
	return {
		fetchAvailableFonts: vi
			.fn()
			.mockImplementation(ApiMocks.fetchAvailableFonts),
		fetchAvailableColors: vi
			.fn()
			.mockImplementation(ApiMocks.fetchAvailableColors),
		fetchTemplates: vi.fn().mockImplementation(ApiMocks.fetchTemplates),
		saveTemplate: vi.fn().mockImplementation(ApiMocks.saveTemplate),
		fetchSessionStatus: vi.fn().mockImplementation(ApiMocks.fetchSessionStatus),

		// Error simulation helpers
		withNetworkError: (_fn: unknown) => {
			return vi.fn().mockRejectedValue(new Error("Network error"));
		},

		withServerError: (_fn: unknown) => {
			return vi.fn().mockRejectedValue({
				status: 500,
				message: "Internal server error",
			});
		},

		withTimeout: (_fn: unknown) => {
			return vi.fn().mockRejectedValue(new Error("Request timeout"));
		},

		withUnauthorized: (_fn: unknown) => {
			return vi.fn().mockRejectedValue({
				status: 401,
				message: "Unauthorized",
			});
		},
	};
};

export type ApiMocksType = ReturnType<typeof createApiMocks>;
