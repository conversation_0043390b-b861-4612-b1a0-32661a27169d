# Component Documentation

## Core Components

### Page Component (`src/components/Page.tsx`)

The main editor component that orchestrates the entire application. It manages:

- Document state (pages and elements)
- UI state (selection, editing mode, zoom)
- Event handling (mouse, keyboard)
- History management (undo/redo)

**Key Features:**
- Multi-page document editing
- Element selection and manipulation
- Copy/paste functionality
- Zoom controls
- Context menus
- Keyboard shortcuts

### EditableElement (`src/components/elements/EditableElement.tsx`)

A universal component that renders all element types with editing capabilities:

- Text elements with rich text editing
- Shape elements (rectangles, circles)
- Image elements with aspect ratio preservation
- Table elements with cell editing
- Block elements (containers for other elements)
- Address elements with special formatting

**Props:**
- `element` - The element data
- `onUpdate` - Callback for element changes
- `onDelete` - Callback for element deletion
- `isSelected` - Whether element is currently selected
- `onSelect` - Callback for element selection

### Sidebar (`src/components/sidebar/Sidebar.tsx`)

Context-sensitive property panel that shows different controls based on the selected element:

- Element positioning (x, y, width, height)
- Text formatting (font, size, color, alignment)
- Shape properties (border, fill, radius)
- Layer management (z-index)
- Element-specific properties

### TopBar (`src/components/TopBar.tsx`)

Document-level controls and information:

- Document title and version
- Save/export actions
- Page navigation
- View options (zoom, borders, guides)
- Template management

## Element Components

### Text Elements
- Rich text editing with TipTap
- Font family, size, and color selection
- Text alignment and formatting
- Variable highlighting for mail merge

### Shape Elements
- Rectangle, circle, and rounded rectangle shapes
- Border and fill color customization
- Corner radius adjustment for rounded rectangles
- Rotation and scaling

### Image Elements
- Image upload and selection
- Aspect ratio preservation
- Cropping and positioning
- Alt text for accessibility

### Table Elements
- Dynamic row/column management
- Cell merging (colspan/rowspan)
- Border styling per cell
- Background colors
- Text formatting within cells

### Block Elements
- Container for grouping multiple elements
- Nested element management
- Block-level operations (copy, move, delete)
- Template creation from blocks

## UI Components

### Dialog Components
- Modal dialogs for confirmations
- Property editing dialogs
- Template selection dialogs
- Export options dialogs

### Form Components
- Input fields with validation
- Color pickers
- Font selectors
- Numeric inputs with units (cm, mm, px)

### Layout Components
- Resizable panels
- Collapsible sections
- Tab interfaces
- Toolbar layouts

## Utility Components

### CropMarks (`src/components/CropMarks.tsx`)
Renders print crop marks around pages for professional printing.

### FoldMarks (`src/components/FoldMarks.tsx`)
Shows fold lines for documents like postcards and brochures.

### PageMinimap (`src/components/PageMinimap.tsx`)
Provides a bird's-eye view of the document with navigation.

### ZoomControls (`src/components/ZoomControls.tsx`)
Zoom in/out controls with preset zoom levels.

### ContextMenu (`src/components/ContextMenu.tsx`)
Right-click context menu with element-specific actions.

## Export Components

### ExportRenderer (`src/components/ExportRenderer.tsx`)
Specialized rendering components for export functionality:

- `ExportDocument` - Renders entire document for export
- `ExportPage` - Renders single page for export
- `ExportElement` - Renders elements without editing controls

These components reuse the same rendering logic as the editor but disable interactive features for clean export output.

## Component Communication

### Props Flow
- Parent components pass data down via props
- Child components communicate up via callback props
- Shared state managed through custom hooks

### Event Handling
- Mouse events handled at the page level
- Keyboard shortcuts managed globally
- Element-specific events handled locally

### State Updates
- All state changes flow through custom hooks
- History tracking automatic for most operations
- Batched updates for performance

## Styling Approach

### Tailwind CSS
- Utility-first CSS framework
- Responsive design patterns
- Dark mode support
- Custom design tokens

### CSS Modules
- Component-specific styles
- Scoped class names
- Import/export of styles

### Inline Styles
- Dynamic styles based on element properties
- Position and dimension calculations
- Color and font applications