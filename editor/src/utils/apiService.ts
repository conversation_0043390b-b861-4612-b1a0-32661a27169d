import type { Element } from "../types/element";
import type {
	FolderUpdateRequest,
	FolderUpdateResponse,
	ImageFolder,
	ImageWithFolders,
} from "../types/image";
import type { PageSettings } from "../types/page";
import { generateCSS, generateSinglePageHTML } from "./htmlExporter";

export interface GetPagesResponse {
	id: number;
	css: string | null;
	version: number;
	pages:
		| {
				hashes: { html: string; json: string }[];
				showPageNumbers?: boolean;
				deletedPageNumberIds?: string[];
		  }
		| { html: string; json: string }[]; // Support both old and new structure
	showPageNumbers?: boolean; // Old structure had this at top level
	deletedPageNumberIds?: string[]; // Old structure support
	role: string;
	allowRelease: number;
}

/**
 * Interface for the page format response from the API
 */
export interface PageFormat {
	name: string;
	description: string;
	height: number;
	width: number;
	trim_top: number;
	trim_right: number;
	trim_bottom: number;
	trim_left: number;
	address_x: number;
	address_y: number;
	address_w?: string;
	address_h?: string;
	sender_y?: number; // Position of sender field relative to address field
	recipient_y?: number; // Position of recipient field relative to address field
	blocker_y?: number; // Bottom position of sender field
	pagenumber_w?: number | null; // Corresponds to X coordinate (mm)
	pagenumber_y?: number | null; // Corresponds to Y coordinate (mm)
	fold_marks?: string | null; // JSON string containing fold mark definitions
}

// Get the API URL and API Key from environment variables
export const API_URL = "/api";

// Cache for object URLs to avoid creating duplicates and memory leaks
const imageUrlCache = new Map<string, string>();

// Helper function to create headers with authentication
const createAuthHeaders = (
	contentType: string = "application/json",
): Headers => {
	const headers = new Headers({
		"Content-Type": contentType,
	});

	return headers;
};

/**
 * Fetch document data from the API
 * @param id The document ID
 * @param version The document version
 * @returns Promise with document data
 */
export async function fetchDocument(
	id: string,
	version: string,
): Promise<GetPagesResponse> {
	const response = await fetch(`${API_URL}/pages/get/${id}/${version}`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch document: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Fetch JSON content by hash
 * @param hash The hash of the JSON file
 * @returns Promise with JSON content
 */
export async function fetchJsonFile(hash: string): Promise<unknown> {
	const response = await fetch(`${API_URL}/json/get/${hash}`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch JSON file: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Store JSON content to the API
 * @param jsonData The JSON data to store
 * @returns Promise with the hash of the stored JSON
 */
export async function storeJsonFile(jsonData: unknown): Promise<string> {
	const jsonBlob = new Blob([JSON.stringify(jsonData)], {
		type: "application/octet-stream",
	});

	const response = await fetch(`${API_URL}/json/store`, {
		method: "PUT",
		headers: createAuthHeaders("application/octet-stream"),
		body: jsonBlob,
	});

	if (!response.ok) {
		throw new Error(
			`Failed to store JSON file: ${response.status} ${response.statusText}`,
		);
	}

	return await response.text();
}

/**
 * Store HTML content to the API
 * @param htmlContent The HTML content to store
 * @returns Promise with the hash of the stored HTML
 */
export async function storeHtmlFile(htmlContent: string): Promise<string> {
	const htmlBlob = new Blob([htmlContent], {
		type: "application/octet-stream",
	});

	const response = await fetch(`${API_URL}/html/store`, {
		method: "PUT",
		headers: createAuthHeaders("application/octet-stream"),
		body: htmlBlob,
	});

	if (!response.ok) {
		throw new Error(
			`Failed to store HTML file: ${response.status} ${response.statusText}`,
		);
	}

	return await response.text();
}

/**
 * Store CSS content to the API
 * @param cssContent The CSS content to store
 * @returns Promise with the hash of the stored CSS
 */
export async function storeCssFile(cssContent: string): Promise<string> {
	const cssBlob = new Blob([cssContent], { type: "application/octet-stream" });

	const response = await fetch(`${API_URL}/css/store`, {
		method: "PUT",
		headers: createAuthHeaders("application/octet-stream"),
		body: cssBlob,
	});

	if (!response.ok) {
		throw new Error(
			`Failed to store CSS file: ${response.status} ${response.statusText}`,
		);
	}

	return await response.text();
}

/**
 * Store document structure to the API
 * @param documentId The document ID (optional, for updates)
 * @param cssHash The hash of the stored CSS file
 * @param pageHashes Array of page hashes (HTML and JSON)
 * @param showPageNumbers Whether to show page numbers
 * @param deletedPageNumberIds Array of page IDs that have their page numbers deleted
 * @returns Promise with the stored document data
 */
export async function storeDocument(
	documentId: string,
	cssHash: string,
	pageHashes: { html: string; json: string }[],
	showPageNumbers: boolean,
	deletedPageNumberIds: string[] = [],
	usedVars: string[] = [],
): Promise<GetPagesResponse> {
	const documentData = {
		id: documentId,
		css: cssHash,
		pages: {
			hashes: pageHashes,
			showPageNumbers: showPageNumbers,
			deletedPageNumberIds: deletedPageNumberIds,
		},
		used_vars: usedVars,
	};

	const response = await fetch(`${API_URL}/pages/store`, {
		method: "POST",
		headers: createAuthHeaders(),
		body: JSON.stringify(documentData),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to store document: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Save document to the API
 * @param pages Array of page settings
 * @param elements Array of page elements
 * @param documentId Optional document ID for updates
 * @param showPageNumbers Whether to show page numbers
 * @param deletedPageNumberIds Set of page IDs that have their page numbers deleted
 * @param apiFormats Optional array of page format definitions
 * @returns Promise with the stored document data
 */
export async function saveDocumentToAPI(
	pages: PageSettings[],
	elements: Element[],
	documentId: string,
	showPageNumbers: boolean,
	deletedPageNumberIds: Set<string> = new Set(),
	apiFormats?: PageFormat[],
): Promise<GetPagesResponse> {
	try {
		// 1. Generate and store CSS
		const cssContent = await generateCSS();
		const cssHash = await storeCssFile(cssContent);

		// 2. Store each page's JSON and HTML
		const pageHashes: { html: string; json: string }[] = [];

		// Collect used variables while iterating over elements
		const variableRegex = /{{\s*([^}\s]+)\s*}}/g;
		const usedVarSet = new Set<string>();

		// Helper to extract vars from HTML string
		const extractVariables = (html: string | undefined) => {
			if (!html) return;
			for (const m of html.matchAll(variableRegex)) {
				const varName = m[1];
				if (varName) usedVarSet.add(varName);
			}
		};

		// Scan through all elements
		for (const el of elements) {
			// Element with direct content
			extractVariables(el.content);

			// Table cells
			if (el.tableProperties?.cells) {
				for (const row of el.tableProperties.cells) {
					for (const cell of row) {
						extractVariables(cell.content);
					}
				}
			}
		}

		// Convert set to sorted array for consistency
		const usedVars = Array.from(usedVarSet).sort();

		// Generate and store each page
		for (let i = 0; i < pages.length; i++) {
			const page = pages[i];
			const pageElements = elements.filter(
				(el) => el.currentPageId === page.id,
			);

			// Create the object to be stored for the page, mapping pagenumber_x to pagenumber_w
			const pageToStore: Record<string, unknown> = { ...page };
			// biome-ignore lint/suspicious/noPrototypeBuiltins: is needed
			if (Object.prototype.hasOwnProperty.call(pageToStore, "pagenumber_x")) {
				pageToStore.pagenumber_w = pageToStore.pagenumber_x;
				delete pageToStore.pagenumber_x;
			}
			// pagenumber_y remains as is, assuming its name is consistent

			// Store page JSON
			const pageData = {
				page: pageToStore, // Use the transformed page object
				elements: pageElements,
			};
			const jsonHash = await storeJsonFile(pageData);

			// Generate and store HTML
			const htmlContent = generateSinglePageHTML(
				page,
				pageElements,
				i + 1,
				"Document",
				false,
				null,
				apiFormats,
			);
			const htmlHash = await storeHtmlFile(htmlContent);

			// Add to page hashes
			pageHashes.push({
				json: jsonHash,
				html: htmlHash,
			});
		}

		// 3. Store the complete document structure
		return await storeDocument(
			documentId,
			cssHash,
			pageHashes,
			showPageNumbers,
			Array.from(deletedPageNumberIds),
			usedVars,
		);
	} catch (error) {
		console.error("Failed to save document to API:", error);
		throw error;
	}
}

/**
 * Store an image file to the API
 * @param blob The image blob to store
 * @param filename The original filename with extension
 * @returns Promise with the hash of the stored image (which will be the URL path)
 */
export async function storeImageFile(
	blob: Blob,
	filename: string,
): Promise<string> {
	const headers = createAuthHeaders("application/octet-stream");
	headers.append("Content-Disposition", `attachment; filename="${filename}"`);

	const response = await fetch(`${API_URL}/image/store`, {
		method: "PUT",
		headers: headers,
		body: blob,
	});

	if (!response.ok) {
		const errorText = await response.text();
		console.error("Server error response:", errorText);
		throw new Error(
			`Failed to store image file: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}

	return await response.text();
}

/**
 * Fetches an image from the API and returns an object URL
 * @param hash The image hash/identifier
 * @returns Promise with the object URL for the image
 */
export async function fetchImageAsBlob(url: string): Promise<Blob> {
	try {
		const response = await fetch(url, {
			method: "GET",
			headers: createAuthHeaders("image/*"),
		});

		if (!response.ok) {
			throw new Error(
				`Failed to fetch image: ${response.status} ${response.statusText}`,
			);
		}

		// Get the image as a blob
		return await response.blob();
	} catch (error) {
		console.error("Error fetching image:", error);
		throw error;
	}
}

/**
 * Fetches an image from the API and returns an object URL
 * @param hash The image hash/identifier
 * @returns Promise with the object URL for the image
 */
export async function fetchImageAsObjectURL(url: string): Promise<string> {
	// Return cached URL if available
	if (imageUrlCache.has(url)) {
		const cachedUrl = imageUrlCache.get(url);
		if (cachedUrl) return cachedUrl;
	}

	try {
		// Get the image as a blob
		const imageBlob = await fetchImageAsBlob(url);

		// Create an object URL from the blob
		const objectUrl = URL.createObjectURL(imageBlob);

		// Cache the URL
		imageUrlCache.set(url, objectUrl);

		return objectUrl;
	} catch (error) {
		console.error("Error fetching image:", error);
		throw error;
	}
}

/**
 * Cleanup function to revoke object URLs when they're no longer needed
 * @param hash The image hash/identifier to cleanup
 */
export function revokeImageObjectURL(hash: string): void {
	if (imageUrlCache.has(hash)) {
		const url = imageUrlCache.get(hash);
		if (url) {
			URL.revokeObjectURL(url);
		}
		imageUrlCache.delete(hash);
	}
}

/**
 * Fetch available page formats from the API
 * @returns Promise with array of page format objects
 */
export async function fetchPageFormats(): Promise<PageFormat[]> {
	const response = await fetch(`${API_URL}/pageformats/get/`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch page formats: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Fetch document variables for a specific job
 * @param jobId The ID of the job
 * @returns Promise with array of variable names
 */
export async function fetchDocumentVariables(jobId: string): Promise<string[]> {
	const response = await fetch(`${API_URL}/vars/get/${jobId}`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch document variables: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

// Define the interface for test data record
export interface TestDataRecord {
	[key: string]: string;
}

/**
 * Fetch test data for a specific job
 * @param jobId The ID of the job
 * @returns Promise with array of test data records
 */
export async function fetchTestData(jobId: string): Promise<TestDataRecord[]> {
	const response = await fetch(`${API_URL}/testdata/get/${jobId}`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch test data: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Interface for a single entry in the version history.
 */
export interface VersionHistoryEntry {
	version: number;
	created_at: string;
	user: string;
}

/**
 * Fetch version history for a specific job.
 * @param jobId The ID of the job.
 * @returns Promise with array of version history entries.
 */
export async function fetchVersionHistory(
	jobId: string,
): Promise<VersionHistoryEntry[]> {
	const response = await fetch(`${API_URL}/pages/get/${jobId}/history`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		// The API returns text, so we try to parse it as JSON after checking response.ok
		// If not ok, the response might not be JSON, so handle appropriately.
		const errorText = await response.text(); // Get error text for debugging
		throw new Error(
			`Failed to fetch version history: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}

	// The API spec says application/text but the example is JSON.
	// We will attempt to parse as JSON.
	try {
		const textData = await response.text();
		return JSON.parse(textData) as VersionHistoryEntry[];
	} catch (e) {
		console.error("Failed to parse version history as JSON:", e);
		throw new Error("Failed to parse version history data.");
	}
}

/**
 * Interface for the image information returned by the API
 */
export interface ImageInfo {
	thumb: string;
	image: string;
	originalName: string;
	folders?: number[];
}

/**
 * Interface for the color information returned by the API
 */
export interface ColorInfo {
	name: string;
	hex: string;
	rgb: string;
	cmyk: string;
}

/**
 * Fetch available images from the API
 * @returns Promise with array of image information
 */
export async function fetchAvailableImages(): Promise<ImageInfo[]> {
	const response = await fetch(`${API_URL}/images/get`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch available images: ${response.status} ${response.statusText}`,
		);
	}

	// Get the images and fix the paths
	const images = await response.json();
	return images;
}

/**
 * Fetch available colors from the API
 * @returns Promise with array of color information
 */
export async function fetchAvailableColors(): Promise<ColorInfo[]> {
	const response = await fetch(`${API_URL}/colors/get`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch available colors: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Interface for the font information returned by the API
 */
export interface FontInfo {
	name: string;
	family: string;
	style: string;
	path?: string;
}

/**
 * Fetch available fonts from the API
 * @returns Promise with array of font information
 */
export async function fetchAvailableFonts(): Promise<FontInfo[]> {
	const response = await fetch(`${API_URL}/fonts/get`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch available fonts: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Fetches the current session status from the backend. Calling this endpoint
 * also refreshes the session timeout on the server side.
 *
 * @returns Promise resolving to an object like { session: boolean }
 */
export async function fetchSessionStatus(): Promise<{ session: boolean }> {
	const response = await fetch(`/session/get/status`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch session status: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Sets the frontify flag for a document.
 * @param jobId The ID of the job.
 * @returns Promise that resolves when the flag is set.
 */
export async function setFrontifyFlag(jobId: string): Promise<string> {
	const response = await fetch(`${API_URL}/pages/set/${jobId}/frontify`, {
		method: "POST",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(
			`Failed to set frontify flag: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}
	return await response.text();
}

/**
 * Deletes the frontify flag for a document.
 * @param jobId The ID of the job.
 * @returns Promise that resolves when the flag is deleted.
 */
export async function deleteFrontifyFlag(jobId: string): Promise<string> {
	const response = await fetch(`${API_URL}/pages/delete/${jobId}/frontify`, {
		method: "POST",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(
			`Failed to delete frontify flag: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}
	return await response.text();
}

/**
 * Gets the frontify flag for a document.
 * @param jobId The ID of the job.
 * @returns Promise with the frontify flag status.
 */
export async function getFrontifyFlag(
	jobId: string,
): Promise<{ frontify: number } | null> {
	const response = await fetch(`${API_URL}/pages/get/${jobId}/frontify`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		if (response.status === 404) {
			// Document or flag not found
			return null;
		}
		const errorText = await response.text();
		throw new Error(
			`Failed to get frontify flag: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}
	// The example response is application/text: example: ['frontify': 1]
	// This is not valid JSON. We need to parse it carefully.
	const textResponse = await response.text();
	try {
		// Attempt to parse it as if it were a JSON-like string in an array/object format
		// {'frontify': 1} or ['frontify': 1]
		// Replace single quotes with double quotes for valid JSON
		const correctedText = textResponse.replace(/'/g, '"');
		// Check if it's an array-like or object-like string
		if (correctedText.startsWith("[") && correctedText.endsWith("]")) {
			// It looks like an array, e.g. "['frontify': 1]"
			// We need to make it a valid JSON array or object. The example is tricky.
			// Given the example ['frontify': 1], it's likely a key-value pair representation.
			// Let's assume it's meant to be an object if it contains 'frontify'.
			const match = correctedText.match(/"frontify":\s*(\d+)/);
			if (match?.[1]) {
				return { frontify: parseInt(match[1], 10) };
			}
		} else if (correctedText.startsWith("{") && correctedText.endsWith("}")) {
			// It looks like an object e.g. "{"frontify": 1}"
			return JSON.parse(correctedText);
		}
		// Fallback for a simple number if direct parsing fails but it's a simple '1' or '0'
		const num = parseInt(textResponse, 10);
		if (!Number.isNaN(num)) {
			return { frontify: num };
		}
		console.warn(
			"Could not parse frontify flag response as JSON or simple number:",
			textResponse,
		);
		return null; // Or handle as an error
	} catch (e) {
		console.error("Error parsing frontify flag response:", textResponse, e);
		throw new Error("Failed to parse frontify flag status from response.");
	}
}

/**
 * Interface for the template information returned by the API
 */
export interface TemplateInfo {
	id: string;
	name: string;
	json: string;
	user: string;
	createdAt: string;
}

/**
 * Fetch available templates from the API
 * @returns Promise with array of template information
 */
export async function fetchTemplates(): Promise<TemplateInfo[]> {
	const response = await fetch(`${API_URL}/templates/get`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch templates: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Save a template to the API
 * @param name The template name
 * @param blockData The block data as JSON
 * @returns Promise with success response
 */
export async function saveTemplate(
	name: string,
	blockData: { blockElement: Element; childElements: Element[] },
): Promise<string> {
	const templateData = {
		id: null,
		name: name,
		json: JSON.stringify(blockData),
	};

	const response = await fetch(`${API_URL}/template/store`, {
		method: "POST",
		headers: createAuthHeaders(),
		body: JSON.stringify(templateData),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to save template: ${response.status} ${response.statusText}`,
		);
	}

	return await response.text();
}

/**
 * Update folder assignments for one or multiple images
 * @param updateRequest The folder update request containing image IDs and their new folder assignments
 * @returns Promise with the update response
 */
export async function updateImageFolders(
	updateRequest: FolderUpdateRequest,
): Promise<FolderUpdateResponse> {
	const response = await fetch(`${API_URL}/images/folders/update`, {
		method: "PUT",
		headers: createAuthHeaders(),
		body: JSON.stringify(updateRequest),
	});

	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(
			`Failed to update image folders: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}

	return await response.json();
}

/**
 * Store an image file to the API with optional folder assignments
 * @param blob The image blob to store
 * @param filename The original filename with extension
 * @param folderIds Optional array of folder IDs to assign the image to
 * @returns Promise with the hash of the stored image (which will be the URL path)
 */
export async function storeImageFileWithFolders(
	blob: Blob,
	filename: string,
	folderIds?: number[],
): Promise<string> {
	const headers = createAuthHeaders("application/octet-stream");
	headers.append("Content-Disposition", `attachment; filename="${filename}"`);

	// Add folder assignments if provided
	if (folderIds && folderIds.length > 0) {
		headers.append("folderIds", folderIds.join(","));
	}

	const response = await fetch(`${API_URL}/image/store`, {
		method: "PUT",
		headers: headers,
		body: blob,
	});

	if (!response.ok) {
		const errorText = await response.text();
		console.error("Server error response:", errorText);
		throw new Error(
			`Failed to store image file: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}

	return await response.text();
}

/**
 * Fetch available image folders from the API
 * @returns Promise with array of image folder information
 */
export async function fetchImageFolders(): Promise<ImageFolder[]> {
	const response = await fetch(`${API_URL}/images/folder/get`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch image folders: ${response.status} ${response.statusText}`,
		);
	}

	return await response.json();
}

/**
 * Fetch available images with folder information from the API
 * @returns Promise with array of image information including folder assignments
 */
export async function fetchAvailableImagesWithFolders(): Promise<
	ImageWithFolders[]
> {
	const response = await fetch(`${API_URL}/images/get`, {
		method: "GET",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch available images: ${response.status} ${response.statusText}`,
		);
	}

	const images = await response.json();

	// Define a minimal interface to represent the expected image structure
	interface RawImage {
		id?: number;
		thumb: string;
		image: string;
		originalName: string;
		folders?: number[];
	}

	// Transform the response to match our ImageWithFolders interface
	return (images as RawImage[]).map((img) => ({
		id: img.id || undefined,
		thumb: img.thumb,
		image: img.image,
		originalName: img.originalName,
		folders: img.folders || [],
	}));
}

/**
 * Move images between folders (copy operation - doesn't remove from source)
 * @param imageIds Array of image IDs to copy
 * @param targetFolderIds Array of target folder IDs
 * @returns Promise with the update response
 */
export async function copyImagesToFolders(
	imageIds: number[],
	targetFolderIds: number[],
): Promise<FolderUpdateResponse> {
	const updateRequest: FolderUpdateRequest = {
		images: imageIds.map((imageId) => ({
			imageId,
			folderIds: targetFolderIds,
		})),
	};

	return updateImageFolders(updateRequest);
}

/**
 * Move images to specific folders (replaces existing folder assignments)
 * @param imageIds Array of image IDs to move
 * @param targetFolderIds Array of target folder IDs
 * @returns Promise with the update response
 */
export async function moveImagesToFolders(
	imageIds: number[],
	targetFolderIds: number[],
): Promise<FolderUpdateResponse> {
	const updateRequest: FolderUpdateRequest = {
		images: imageIds.map((imageId) => ({
			imageId,
			folderIds: targetFolderIds,
		})),
	};

	return updateImageFolders(updateRequest);
}

/**
 * Create a new image folder
 * @param folderName The name of the new folder
 * @param parentId Optional parent folder ID (null for root level)
 * @returns Promise with the created folder information
 */
export async function createImageFolder(
	folderName: string,
	parentId?: number | null,
): Promise<ImageFolder> {
	const requestData = {
		folderName,
		parentId: parentId || null,
	};

	const response = await fetch(`${API_URL}/images/folder/create`, {
		method: "POST",
		headers: createAuthHeaders(),
		body: JSON.stringify(requestData),
	});

	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(
			`Failed to create folder: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}

	return await response.json();
}

/**
 * Delete an image folder
 * @param folderId The ID of the folder to delete
 * @returns Promise with the deletion response
 */
export async function deleteImageFolder(
	folderId: number,
): Promise<{ message: string }> {
	const response = await fetch(`${API_URL}/images/folder/delete/${folderId}`, {
		method: "DELETE",
		headers: createAuthHeaders(),
	});

	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(
			`Failed to delete folder: ${response.status} ${response.statusText}. Server response: ${errorText}`,
		);
	}

	return await response.json();
}
