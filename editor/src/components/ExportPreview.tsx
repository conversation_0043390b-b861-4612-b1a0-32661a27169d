import { FileDown, FileText, FileUp } from "lucide-react";
import { useEffect, useRef } from "react";
import { ExportDocument } from "@/components/ExportRenderer";
import { Button } from "@/components/ui/button";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import type { PageFormat, TestDataRecord } from "@/utils/apiService";

interface ExportPreviewProps {
	pages: PageSettings[];
	elements: Element[];
	isOpen: boolean;
	onClose: () => void;
	onExport: () => void;
	onImport: () => void;
	documentId: string;
	role?: string;
	apiFormats?: PageFormat[];
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
}

export function ExportPreview({
	pages,
	elements,
	isOpen,
	onClose,
	onExport,
	onImport,
	documentId,
	role,
	apiFormats,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
}: ExportPreviewProps) {
	const exportContainerRef = useRef<HTMLDivElement>(null);
	const isAgency = role === "agency";

	useEffect(() => {
		const handleEscape = (event: KeyboardEvent) => {
			if (event.key === "Escape") {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener("keydown", handleEscape);
		} else {
			document.removeEventListener("keydown", handleEscape);
		}

		return () => {
			document.removeEventListener("keydown", handleEscape);
		};
	}, [isOpen, onClose]);

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black/50 z-[100] flex items-center justify-center">
			<div className="bg-white w-full h-full flex flex-col overflow-hidden">
				<div className="p-4 border-b flex justify-between items-center">
					<h2 className="text-xl font-bold">Vorschau</h2>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							onClick={onImport}
							className={`${isAgency ? "flex" : "hidden"} items-center gap-2`}
						>
							<FileUp className="h-4 w-4" />
							Import
						</Button>
						<Button
							variant="outline"
							onClick={onExport}
							className={`${isAgency ? "flex" : "hidden"} items-center gap-2`}
						>
							<FileDown className="h-4 w-4" />
							Export
						</Button>
						<Button
							variant="outline"
							asChild
							className="flex items-center gap-2"
						>
							<a
								href={`/get/editor/testpdf/${documentId}/${selectedTestDataIndex >= 0 ? selectedTestDataIndex : 0}`}
								target="_blank"
								className="flex items-center gap-2"
							>
								<FileText className="h-4 w-4" />
								Test PDF
							</a>
						</Button>
						<Button variant="ghost" onClick={onClose}>
							Schließen
						</Button>
					</div>
				</div>

				<div className="flex-1 overflow-auto p-4">
					<div
						ref={exportContainerRef}
						className="mt-4"
						style={{ position: "relative", zIndex: 1 }}
					>
						<div
							className="absolute inset-0"
							style={{
								width: "100%",
								height: "100%",
							}}
						>
							<ExportDocument
								pages={pages}
								elements={elements}
								apiFormats={apiFormats}
								testData={testData}
								selectedTestDataIndex={selectedTestDataIndex}
								highlightVariables={highlightVariables}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
