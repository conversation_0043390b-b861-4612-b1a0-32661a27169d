import type { LucideIcon } from "lucide-react";
import type React from "react";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import { Label } from "../ui/label";

// Base toolbar container with consistent styling
interface ToolbarContainerProps {
	children: React.ReactNode;
	className?: string;
}

export function ToolbarContainer({
	children,
	className,
}: ToolbarContainerProps) {
	return <div className={cn("space-y-6", className)}>{children}</div>;
}

// Toolbar section with consistent spacing
interface ToolbarSectionProps {
	children: React.ReactNode;
	className?: string;
}

export function ToolbarSection({ children, className }: ToolbarSectionProps) {
	return <div className={cn("space-y-3", className)}>{children}</div>;
}

// Toolbar header with consistent styling
interface ToolbarHeaderProps {
	title: string;
	className?: string;
}

export function ToolbarHeader({ title, className }: ToolbarHeaderProps) {
	return (
		<h3 className={cn("text-lg font-semibold mb-3", className)}>{title}</h3>
	);
}

// Consistent toolbar button with icon
interface ToolbarButtonProps {
	icon: LucideIcon;
	label: string;
	onClick: () => void;
	disabled?: boolean;
	variant?: "default" | "outline" | "secondary" | "ghost" | "destructive";
	className?: string;
	fullWidth?: boolean;
}

export function ToolbarButton({
	icon: Icon,
	label,
	onClick,
	disabled = false,
	variant = "outline",
	className,
	fullWidth = true,
}: ToolbarButtonProps) {
	return (
		<Button
			variant={variant}
			className={cn(
				"flex items-center justify-center gap-2",
				fullWidth && "w-full",
				className,
			)}
			onClick={onClick}
			disabled={disabled}
		>
			<Icon className="w-4 h-4" />
			{label}
		</Button>
	);
}

// Loading state component
interface ToolbarLoadingProps {
	message?: string;
}

export function ToolbarLoading({
	message = "Wird geladen...",
}: ToolbarLoadingProps) {
	return (
		<div className="flex items-center justify-center py-8">
			<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			{message && <span className="ml-2 text-sm text-gray-500">{message}</span>}
		</div>
	);
}

// Error state component
interface ToolbarErrorProps {
	message: string;
	className?: string;
}

export function ToolbarError({ message, className }: ToolbarErrorProps) {
	return (
		<div
			className={cn(
				"p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm",
				className,
			)}
		>
			{message}
		</div>
	);
}

// Empty state component
interface ToolbarEmptyProps {
	message: string;
	className?: string;
}

export function ToolbarEmpty({ message, className }: ToolbarEmptyProps) {
	return <p className={cn("text-sm text-gray-500", className)}>{message}</p>;
}

// Field group with consistent label and spacing
interface ToolbarFieldGroupProps {
	label: string;
	children: React.ReactNode;
	className?: string;
	disabled?: boolean;
}

export function ToolbarFieldGroup({
	label,
	children,
	className,
	disabled = false,
}: ToolbarFieldGroupProps) {
	return (
		<div className={cn("space-y-2", className)}>
			<Label className={cn("text-sm font-medium", disabled && "text-gray-400")}>
				{label}
			</Label>
			{children}
		</div>
	);
}

// Utility function to get consistent disabled styling
export function getDisabledClassName(disabled: boolean): string {
	return disabled ? "opacity-50 cursor-not-allowed" : "";
}

// Utility function to get consistent disabled label styling
export function getDisabledLabelClassName(disabled: boolean): string {
	return disabled ? "text-gray-400" : "";
}
