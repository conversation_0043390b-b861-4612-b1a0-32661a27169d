import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import { useElementOperations } from "../useElementOperations";

// Mock uuid to return predictable values
vi.mock("uuid", () => ({
	v4: vi.fn().mockReturnValue("test-uuid"),
}));

type TableElementOperation =
	| import("./../../types/element").Element
	| import("./../../types/element").BatchDeleteOperation;

describe("useElementOperations", () => {
	// Setup common test variables
	const mockPages = [PageFactory.createPage({ id: "page-1" })];
	const mockElements = [
		ElementFactory.createTextElement({
			id: "text-1",
			currentPageId: "page-1",
			zIndex: 1,
		}),
		ElementFactory.createShapeElement({
			id: "shape-1",
			currentPageId: "page-1",
			zIndex: 2,
		}),
	];

	const mockSetEditorState = vi.fn();
	const mockCommitBatch = vi.fn();
	const mockBeginBatch = vi.fn();
	const mockUpdateDraft = vi.fn();
	const mockIsBatching = vi.fn().mockReturnValue(false);
	const mockSetSelectedElementId = vi.fn();
	const mockSetEditingBlockId = vi.fn();
	const mockSetDeletedPageNumberIds = vi.fn();
	const mockSetClipboard = vi.fn();

	const defaultProps = {
		pages: mockPages,
		elements: mockElements,
		setEditorState: mockSetEditorState,
		commitBatch: mockCommitBatch,
		beginBatch: mockBeginBatch,
		updateDraft: mockUpdateDraft,
		isBatching: mockIsBatching,
		currentPage: "page-1",
		editingBlockId: null,
		selectedElementId: null,
		setSelectedElementId: mockSetSelectedElementId,
		setEditingBlockId: mockSetEditingBlockId,
		setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
		isEditorLocked: false,
		clipboard: null,
		setClipboard: mockSetClipboard,
		isEditing: false,
	};

	beforeEach(() => {
		vi.clearAllMocks();
	});

	// Test element creation
	describe("handleAddElement", () => {
		it("should add a new element and select it", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const newElement = ElementFactory.createTextElement({
				id: "new-text",
				currentPageId: "page-1",
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [...mockElements, newElement],
			});
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("new-text");
		});

		it("should assign z-index to new element if not provided", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const newElement = ElementFactory.createTextElement({
				id: "new-text",
				currentPageId: "page-1",
				zIndex: undefined,
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			// The highest z-index in mockElements is 2, so the new element should get 3
			expect(mockSetEditorState).toHaveBeenCalled();
			const calledElements = mockSetEditorState.mock.calls[0][0].elements;
			const addedElement = calledElements.find(
				(el: import("./../../types/element").Element) => el.id === "new-text",
			);
			expect(addedElement?.zIndex).toBe(3);
		});

		it("should not add element if editor is locked", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					isEditorLocked: true,
				}),
			);

			const newElement = ElementFactory.createTextElement({
				id: "new-text",
				currentPageId: "page-1",
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should add element to block when in block editing mode", () => {
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				x: 50,
				y: 50,
				childElementIds: [],
			});

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					elements: [...mockElements, blockElement],
					editingBlockId: "block-1",
				}),
			);

			const newElement = ElementFactory.createTextElement({
				id: "new-text",
				currentPageId: "page-1",
				x: 100,
				y: 100,
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const calledElements = mockSetEditorState.mock.calls[0][0].elements;

			// Check that the block was updated with the new child ID
			const updatedBlock = calledElements.find(
				(el: import("./../../types/element").Element) => el.id === "block-1",
			);
			expect(updatedBlock?.childElementIds).toContain("new-text");

			// Check that the element was added with correct parent and position
			const addedElement = calledElements.find(
				(el: import("./../../types/element").Element) => el.id === "new-text",
			);
			expect(addedElement?.parentId).toBe("block-1");
			expect(addedElement?.x).toBe(50); // 100 - 50 (block x)
			expect(addedElement?.y).toBe(50); // 100 - 50 (block y)
		});

		it("should handle batch delete operation", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const batchDeleteOp = {
				_batchDelete: true,
				elementIds: ["text-1", "shape-1"],
			};

			act(() => {
				result.current.handleAddElement(
					batchDeleteOp as unknown as TableElementOperation,
				);
			});

			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [],
			});
		});

		it("should handle single element delete operation", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const deleteOp = {
				id: "text-1",
				_delete: true,
			};

			act(() => {
				result.current.handleAddElement(
					deleteOp as unknown as TableElementOperation,
				);
			});

			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: mockElements.filter((el) => el.id !== "text-1"),
			});
		});
	});

	// Test adding multiple elements
	describe("handleAddElements", () => {
		it("should add multiple elements in a single operation", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const newElements = [
				ElementFactory.createTextElement({
					id: "new-text-1",
					currentPageId: "page-1",
				}),
				ElementFactory.createShapeElement({
					id: "new-shape-1",
					currentPageId: "page-1",
				}),
			];

			act(() => {
				result.current.handleAddElements(newElements);
			});

			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [...mockElements, ...newElements],
			});

			// Should select the last added element
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("new-shape-1");
		});

		it("should assign sequential z-indices to new elements", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const newElements = [
				ElementFactory.createTextElement({
					id: "new-text-1",
					currentPageId: "page-1",
					zIndex: undefined,
				}),
				ElementFactory.createShapeElement({
					id: "new-shape-1",
					currentPageId: "page-1",
					zIndex: undefined,
				}),
			];

			act(() => {
				result.current.handleAddElements(newElements);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const calledElements = mockSetEditorState.mock.calls[0][0].elements;

			// The highest z-index in mockElements is 2, so the new elements should get 3 and 4
			const addedText = calledElements.find(
				(el: import("./../../types/element").Element) => el.id === "new-text-1",
			);
			const addedShape = calledElements.find(
				(el: import("./../../types/element").Element) =>
					el.id === "new-shape-1",
			);
			expect(addedText?.zIndex).toBe(3);
			expect(addedShape?.zIndex).toBe(4);
		});
	});

	// Test element updates
	describe("handleUpdateElement", () => {
		it("should update an existing element", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const updatedElement = {
				...mockElements[0],
				content: "Updated content",
			};

			act(() => {
				result.current.handleUpdateElement(updatedElement, true);
			});

			expect(mockCommitBatch).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [updatedElement, mockElements[1]],
			});
		});

		it("should use updateDraft for non-final updates", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const updatedElement = {
				...mockElements[0],
				x: 100,
				y: 150,
			};

			act(() => {
				result.current.handleUpdateElement(updatedElement, false);
			});

			expect(mockBeginBatch).toHaveBeenCalled();
			expect(mockUpdateDraft).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [updatedElement, mockElements[1]],
			});
			expect(mockCommitBatch).not.toHaveBeenCalled();
		});

		it("should handle moveToFront action", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const elementWithAction: import("../../types/element").Element = {
				...mockElements[0],
				_action: "moveToFront",
			};

			act(() => {
				result.current.handleUpdateElement(elementWithAction, true);
			});

			expect(mockCommitBatch).toHaveBeenCalled();
			const updatedElements = mockCommitBatch.mock.calls[0][0].elements;

			// First element should now have highest z-index
			const updatedElement = updatedElements.find(
				(el: import("./../../types/element").Element) => el.id === "text-1",
			);
			expect(updatedElement?.zIndex).toBe(1);

			// Second element should have lower z-index
			const otherElement = updatedElements.find(
				(el: import("./../../types/element").Element) => el.id === "shape-1",
			);
			expect(otherElement?.zIndex).toBe(0);
		});

		it("should handle moveToBack action", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const elementWithAction: import("../../types/element").Element = {
				...mockElements[1], // shape-1 with zIndex 2
				_action: "moveToBack",
			};

			act(() => {
				result.current.handleUpdateElement(elementWithAction, true);
			});

			expect(mockCommitBatch).toHaveBeenCalled();
			const updatedElements = mockCommitBatch.mock.calls[0][0].elements;

			// Element with action should now have lowest z-index
			const updatedElement = updatedElements.find(
				(el: import("./../../types/element").Element) => el.id === "shape-1",
			);
			expect(updatedElement?.zIndex).toBe(0);

			// Other element should have higher z-index
			const otherElement = updatedElements.find(
				(el: import("./../../types/element").Element) => el.id === "text-1",
			);
			expect(otherElement?.zIndex).toBe(1);
		});
	});

	// Test element deletion
	describe("handleDeleteElement", () => {
		it("should delete an element", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			act(() => {
				result.current.handleDeleteElement("text-1");
			});

			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [mockElements[1]], // Only shape-1 remains
			});
		});

		it("should not delete address field elements", () => {
			const addressElement = ElementFactory.createAddressElement({
				id: "address-1",
				currentPageId: "page-1",
				isAddressField: true,
			});

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					elements: [...mockElements, addressElement],
				}),
			);

			act(() => {
				result.current.handleDeleteElement("address-1");
			});

			// Should not call setEditorState to delete the element
			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should delete a block and its children", () => {
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				childElementIds: ["child-1", "child-2"],
			});

			const childElements = [
				ElementFactory.createTextElement({
					id: "child-1",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
				ElementFactory.createShapeElement({
					id: "child-2",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
			];

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					elements: [...mockElements, blockElement, ...childElements],
				}),
			);

			act(() => {
				result.current.handleDeleteElement("block-1");
			});

			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: mockElements, // Only the original elements remain
			});
		});

		it("should remove child from parent block when deleting a child element", () => {
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				childElementIds: ["child-1", "child-2"],
			});

			const childElements = [
				ElementFactory.createTextElement({
					id: "child-1",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
				ElementFactory.createShapeElement({
					id: "child-2",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
			];

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					elements: [...mockElements, blockElement, ...childElements],
				}),
			);

			act(() => {
				result.current.handleDeleteElement("child-1");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const updatedElements = mockSetEditorState.mock.calls[0][0].elements;

			// Check that child-1 is removed
			expect(
				updatedElements.find(
					(el: import("./../../types/element").Element) => el.id === "child-1",
				),
			).toBeUndefined();

			// Check that block's childElementIds is updated
			const updatedBlock = updatedElements.find(
				(el: import("./../../types/element").Element) => el.id === "block-1",
			);
			expect(updatedBlock?.childElementIds).toEqual(["child-2"]);
		});

		it("should exit block edit mode if deleting the currently edited block", () => {
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				childElementIds: [],
			});

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					elements: [...mockElements, blockElement],
					editingBlockId: "block-1",
				}),
			);

			act(() => {
				result.current.handleDeleteElement("block-1");
			});

			expect(mockSetEditingBlockId).toHaveBeenCalledWith(null);
		});
	});

	// Test clipboard operations
	describe("clipboard operations", () => {
		it("should copy an element to clipboard", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			act(() => {
				result.current.handleCopyElement("text-1");
			});

			expect(mockSetClipboard).toHaveBeenCalledWith(mockElements[0]);
		});

		it("should copy a block and its children to clipboard", () => {
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				childElementIds: ["child-1", "child-2"],
			});

			const childElements = [
				ElementFactory.createTextElement({
					id: "child-1",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
				ElementFactory.createShapeElement({
					id: "child-2",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
			];

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					elements: [...mockElements, blockElement, ...childElements],
				}),
			);

			act(() => {
				result.current.handleCopyElement("block-1");
			});

			expect(mockSetClipboard).toHaveBeenCalledWith({
				block: blockElement,
				children: childElements,
			});
		});

		it("should cut an element (copy then delete)", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			act(() => {
				result.current.handleCutElement("text-1");
			});

			// Should copy the element
			expect(mockSetClipboard).toHaveBeenCalledWith(mockElements[0]);

			// Should delete the element
			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [mockElements[1]], // Only shape-1 remains
			});
		});

		it("should paste a copied element", () => {
			const copiedElement = mockElements[0];

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					clipboard: copiedElement,
				}),
			);

			act(() => {
				result.current.handlePasteAction("page-1", 200, 300);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const pastedElements = mockSetEditorState.mock.calls[0][0].elements;

			// Should have original elements plus the pasted one
			expect(pastedElements.length).toBe(3);

			// Check the pasted element properties
			const pastedElement = pastedElements.find(
				(el: import("./../../types/element").Element) => el.id === "test-uuid",
			);
			expect(pastedElement).toBeDefined();
			expect(pastedElement?.x).toBe(200 - copiedElement.width / 2);
			expect(pastedElement?.y).toBe(300);
			expect(pastedElement?.currentPageId).toBe("page-1");

			// Should select the pasted element
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("test-uuid");
		});

		it("should paste a block with its children", () => {
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				childElementIds: ["child-1", "child-2"],
				width: 100,
				height: 100,
			});

			const childElements = [
				ElementFactory.createTextElement({
					id: "child-1",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
				ElementFactory.createShapeElement({
					id: "child-2",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
			];

			const blockClipboard = {
				block: blockElement,
				children: childElements,
			};

			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					clipboard: blockClipboard,
				}),
			);

			act(() => {
				result.current.handlePasteAction("page-1", 200, 300);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const pastedElements = mockSetEditorState.mock.calls[0][0].elements;

			// Should have original elements plus the block and its children
			expect(pastedElements.length).toBe(5);

			// Check the pasted block
			const pastedBlock = pastedElements.find(
				(el: import("./../../types/element").Element) => el.id === "test-uuid",
			);
			expect(pastedBlock).toBeDefined();
			expect(pastedBlock?.x).toBe(200 - blockElement.width / 2);
			expect(pastedBlock?.y).toBe(300);
			expect(pastedBlock?.currentPageId).toBe("page-1");
			expect(pastedBlock?.childElementIds?.length).toBe(2);

			// Should select the pasted block
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("test-uuid");
		});
	});

	// Test batch operations
	describe("batch operations", () => {
		it("should use batch operations for non-final element updates", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					isBatching: vi.fn().mockReturnValue(false),
				}),
			);

			const updatedElement = {
				...mockElements[0],
				x: 100,
				y: 150,
			};

			act(() => {
				result.current.handleUpdateElement(updatedElement, false);
			});

			expect(mockBeginBatch).toHaveBeenCalled();
			expect(mockUpdateDraft).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [updatedElement, mockElements[1]],
			});
		});

		it("should not begin batch if already batching", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					...defaultProps,
					isBatching: vi.fn().mockReturnValue(true),
				}),
			);

			const updatedElement = {
				...mockElements[0],
				x: 100,
				y: 150,
			};

			act(() => {
				result.current.handleUpdateElement(updatedElement, false);
			});

			expect(mockBeginBatch).not.toHaveBeenCalled();
			expect(mockUpdateDraft).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [updatedElement, mockElements[1]],
			});
		});

		it("should commit batch for final element updates", () => {
			const { result } = renderHook(() => useElementOperations(defaultProps));

			const updatedElement = {
				...mockElements[0],
				x: 100,
				y: 150,
			};

			act(() => {
				result.current.handleUpdateElement(updatedElement, true);
			});

			expect(mockCommitBatch).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [updatedElement, mockElements[1]],
			});
			expect(mockBeginBatch).not.toHaveBeenCalled();
			expect(mockUpdateDraft).not.toHaveBeenCalled();
		});
	});
});
