import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";
import type { Editor } from "@/components/RichTextEditor";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import { Sidebar } from "../Sidebar";

// Mock the child toolbar components
vi.mock("../PageToolbar", () => ({
	PageToolbar: () => <div data-testid="page-toolbar-mock">Page Toolbar</div>,
}));

vi.mock("../TextFormatToolbar", () => ({
	TextFormatToolbar: () => (
		<div data-testid="text-format-toolbar-mock">Text Format Toolbar</div>
	),
}));

vi.mock("../ElementToolbar", () => ({
	ElementToolbar: () => (
		<div data-testid="element-toolbar-mock">Element Toolbar</div>
	),
}));

vi.mock("../TableFormatToolbar", () => ({
	TableFormatToolbar: () => (
		<div data-testid="table-format-toolbar-mock">Table Format Toolbar</div>
	),
}));

vi.mock("../BlockToolbar", () => ({
	BlockToolbar: () => <div data-testid="block-toolbar-mock">Block Toolbar</div>,
}));

vi.mock("../TemplateToolbar", () => ({
	TemplateToolbar: () => (
		<div data-testid="template-toolbar-mock">Template Toolbar</div>
	),
}));

vi.mock("../VersionToolbar", () => ({
	VersionToolbar: () => (
		<div data-testid="version-toolbar-mock">Version Toolbar</div>
	),
}));

describe("Sidebar component", () => {
	// Common props for all tests
	const mockPages: PageSettings[] = [
		{
			id: "page-1",
			format: "A4",
			orientation: "portrait",
			width: 210,
			height: 297,
		},
	];

	const mockElements: Element[] = [];

	const mockOnAddPage = vi.fn();
	const mockOnAddElement = vi.fn();
	const mockOnAddElements = vi.fn();
	const mockOnUpdateElement = vi.fn();
	const mockOnTogglePageNumbers = vi.fn();
	const mockSetShowElementBorders = vi.fn();
	const mockSetShowFoldMarks = vi.fn();
	const mockOnVersionChange = vi.fn();
	const mockOnRefreshHistory = vi.fn();
	const mockOnInsertTemplate = vi.fn();
	const mockOnRefreshTemplates = vi.fn();
	const mockDetermineWeight = vi.fn();

	const defaultProps = {
		pages: mockPages,
		elements: mockElements,
		onAddPage: mockOnAddPage,
		onAddElement: mockOnAddElement,
		onAddElements: mockOnAddElements,
		onUpdateElement: mockOnUpdateElement,
		editor: null,
		selectedElement: null,
		apiFonts: [],
		isLoadingFonts: false,
		loadedFonts: new Set<string>(),
		determineWeight: mockDetermineWeight,
		showPageNumbers: false,
		onTogglePageNumbers: mockOnTogglePageNumbers,
		showElementBorders: true,
		setShowElementBorders: mockSetShowElementBorders,
		showFoldMarks: false,
		setShowFoldMarks: mockSetShowFoldMarks,
		versionHistory: [],
		currentVersion: "latest",
		onVersionChange: mockOnVersionChange,
		onRefreshHistory: mockOnRefreshHistory,
		apiColors: [],
		isLoadingColors: false,
		colorError: null,
		templates: [],
		isLoadingTemplates: false,
		templatesError: null,
		onInsertTemplate: mockOnInsertTemplate,
		onRefreshTemplates: mockOnRefreshTemplates,
	};

	it("renders with default tab (page)", () => {
		render(<Sidebar {...defaultProps} />);

		// Check that the sidebar container is rendered
		expect(screen.getByText("Seite")).toBeInTheDocument();
		expect(screen.getByText("Text")).toBeInTheDocument();
		expect(screen.getByText("Form")).toBeInTheDocument();
		expect(screen.getByText("Bild")).toBeInTheDocument();
		expect(screen.getByText("Tabelle")).toBeInTheDocument();
		expect(screen.getByText("Baustein")).toBeInTheDocument();
		expect(screen.getByText("Vorlagen")).toBeInTheDocument();
		expect(screen.getByText("Historie")).toBeInTheDocument();

		// Check that the page toolbar is rendered by default
		expect(screen.getByTestId("page-toolbar-mock")).toBeInTheDocument();
	});

	it("switches to text tab when editor is provided", () => {
		const editor = { commands: {} } as unknown as Editor;
		render(<Sidebar {...defaultProps} editor={editor} />);

		// Text toolbar should be rendered when editor is provided
		expect(screen.getByTestId("text-format-toolbar-mock")).toBeInTheDocument();
	});

	it("switches to appropriate tab based on selected element type", async () => {
		const user = userEvent.setup();
		const { rerender } = render(<Sidebar {...defaultProps} />);

		// Initially page toolbar is shown
		expect(screen.getByTestId("page-toolbar-mock")).toBeInTheDocument();

		// Select a text element
		rerender(
			<Sidebar
				{...defaultProps}
				selectedElement={{
					id: "text-1",
					currentPageId: "page-1",
					type: "text",
					x: 10,
					y: 20,
					width: 100,
					height: 50,
					rotation: 0,
				}}
			/>,
		);

		// Text toolbar should be shown
		expect(screen.getByTestId("text-format-toolbar-mock")).toBeInTheDocument();

		// Select a shape element
		rerender(
			<Sidebar
				{...defaultProps}
				selectedElement={{
					id: "shape-1",
					currentPageId: "page-1",
					type: "shape",
					x: 10,
					y: 20,
					width: 100,
					height: 50,
					rotation: 0,
				}}
			/>,
		);

		// Element toolbar should be shown
		expect(screen.getByTestId("element-toolbar-mock")).toBeInTheDocument();

		// Click on the table tab
		await user.click(screen.getByText("Tabelle"));
		expect(screen.getByTestId("table-format-toolbar-mock")).toBeInTheDocument();
	});

	it("hides certain tabs when block editing is active", () => {
		render(<Sidebar {...defaultProps} isBlockEditingActive={true} />);

		// Page, Block, Templates, and Version tabs should be hidden
		expect(screen.queryByText("Seite")).not.toBeInTheDocument();
		expect(screen.queryByText("Baustein")).not.toBeInTheDocument();
		expect(screen.queryByText("Vorlagen")).not.toBeInTheDocument();
		expect(screen.queryByText("Historie")).not.toBeInTheDocument();

		// Other tabs should still be visible
		expect(screen.getByText("Text")).toBeInTheDocument();
		expect(screen.getByText("Form")).toBeInTheDocument();
		expect(screen.getByText("Bild")).toBeInTheDocument();
		expect(screen.getByText("Tabelle")).toBeInTheDocument();
	});

	it("calls refresh functions when clicking on tabs", async () => {
		const user = userEvent.setup();
		render(<Sidebar {...defaultProps} />);

		// Click on the templates tab
		await user.click(screen.getByText("Vorlagen"));
		expect(mockOnRefreshTemplates).toHaveBeenCalledTimes(1);

		// Click on the version tab
		await user.click(screen.getByText("Historie"));
		expect(mockOnRefreshHistory).toHaveBeenCalledTimes(1);
	});
});
