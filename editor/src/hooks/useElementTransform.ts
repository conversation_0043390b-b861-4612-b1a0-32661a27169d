import { useCallback, useEffect, useRef, useState } from "react";
import { pxToMm, roundToTwoDecimals } from "@/utils/unitConversion";
import type { Element } from "../types/element";

// Define possible handle types
export type ResizeHandleType =
	| "top"
	| "bottom"
	| "left"
	| "right"
	| "top-left"
	| "top-right"
	| "bottom-left"
	| "bottom-right";

// Snapping configuration
const SNAP_THRESHOLD_MM = 1;

// Define SnapGuideInfo interface
interface SnapGuideInfo {
	type: "horizontal" | "vertical";
	position: number;
	start: number;
	end: number;
}

// --- Resizing Helper Functions ---

// Helper function to get local coordinates based on point type
const getLocalPointCoords = (
	width: number,
	height: number,
	pointType: string,
): { lx: number; ly: number } => {
	switch (pointType) {
		case "tl":
			return { lx: 0, ly: 0 };
		case "tc":
			return { lx: width / 2, ly: 0 };
		case "tr":
			return { lx: width, ly: 0 };
		case "ml":
			return { lx: 0, ly: height / 2 };
		case "mc":
			return { lx: width / 2, ly: height / 2 }; // Center
		case "mr":
			return { lx: width, ly: height / 2 };
		case "bl":
			return { lx: 0, ly: height };
		case "bc":
			return { lx: width / 2, ly: height };
		case "br":
			return { lx: width, ly: height };
		default:
			console.warn(`Unknown pointType: ${pointType}`);
			return { lx: 0, ly: 0 };
	}
};

// Function to calculate the screen position of a specific point on the element
const calculatePointPosition = (
	startX: number,
	startY: number,
	startWidth: number,
	startHeight: number,
	rotationRad: number,
	pointType: string,
): { x: number; y: number } => {
	const { lx, ly } = getLocalPointCoords(startWidth, startHeight, pointType);
	const cosTheta = Math.cos(rotationRad);
	const sinTheta = Math.sin(rotationRad);

	// Center-relative local coordinates
	const clx = lx - startWidth / 2;
	const cly = ly - startHeight / 2;

	// Rotated center-relative coordinates
	const rotated_clx = clx * cosTheta - cly * sinTheta;
	const rotated_cly = clx * sinTheta + cly * cosTheta;

	// Initial center screen coordinates
	const initialCenterX = startX + startWidth / 2;
	const initialCenterY = startY + startHeight / 2;

	// Final screen coordinates
	const screenX = initialCenterX + rotated_clx;
	const screenY = initialCenterY + rotated_cly;

	return { x: screenX, y: screenY };
};

// Function to calculate the new top-left position based on a fixed point
const calculateTopLeftFromFixedPoint = (
	fixedPointScreenX: number,
	fixedPointScreenY: number,
	newWidth: number,
	newHeight: number,
	rotationRad: number,
	fixedPointType: string,
): { x: number; y: number } => {
	const { lx, ly } = getLocalPointCoords(newWidth, newHeight, fixedPointType);
	const cosTheta = Math.cos(rotationRad);
	const sinTheta = Math.sin(rotationRad);

	// Center-relative local coordinates of the fixed point type for NEW dimensions
	const clx = lx - newWidth / 2;
	const cly = ly - newHeight / 2;

	// Rotated center-relative coordinates
	const rotated_clx = clx * cosTheta - cly * sinTheta;
	const rotated_cly = clx * sinTheta + cly * cosTheta;

	// Calculate the new center based on the fixed point
	const newCenterX = fixedPointScreenX - rotated_clx;
	const newCenterY = fixedPointScreenY - rotated_cly;

	// Calculate the new top-left from the new center
	const newX = newCenterX - newWidth / 2;
	const newY = newCenterY - newHeight / 2;

	return { x: roundToTwoDecimals(newX), y: roundToTwoDecimals(newY) };
};

// Mapping from handle type to the opposite fixed point type
const handleToFixedPointMap: Record<ResizeHandleType, string> = {
	top: "bc",
	bottom: "tc",
	left: "mr",
	right: "ml",
	"top-left": "br",
	"top-right": "bl",
	"bottom-left": "tr",
	"bottom-right": "tl",
};

// --- End Resizing Helper Functions ---

interface UseElementTransformProps {
	element: Element;
	onUpdate: (updatedElement: Element, isFinal: boolean) => void;
	updateSnapGuides?: (
		visible: boolean,
		guides: SnapGuideInfo[],
		relativeToBlockId?: string | null,
		blockInfo?: { x: number; y: number; width: number; height: number } | null,
	) => void;
	editingBlockId?: string | null;
	calculateSnapGuides?: (
		elementId: string,
		newX: number,
		newY: number,
		elementWidth: number,
		elementHeight: number,
		parentBlockId?: string | null,
		movingHandle?: ResizeHandleType,
	) => { snapX: number | null; snapY: number | null; guides: SnapGuideInfo[] };
}

export function useElementTransform({
	element,
	onUpdate,
	updateSnapGuides,
	editingBlockId,
	calculateSnapGuides,
}: UseElementTransformProps) {
	const [isDragging, setIsDragging] = useState(false);
	const [isResizing, setIsResizing] = useState(false);
	const [activeHandle, setActiveHandle] = useState<ResizeHandleType | null>(
		null,
	);

	const startPosRef = useRef({
		x: 0,
		y: 0,
		width: 0,
		height: 0,
		mouseX: 0,
		mouseY: 0,
	});

	const throttleRef = useRef<number | null>(null);
	const otherElementsRef = useRef<Element[]>([]);

	// Helper function to get all other elements on the current page for snapping
	const updateOtherElements = () => {
		const allPageElements = document.querySelectorAll(`[data-element-id]`);
		const relevantElements: Element[] = [];

		allPageElements.forEach((elDom) => {
			const id = elDom.getAttribute("data-element-id");
			if (id && id !== element.id) {
				// Exclude self
				const elementData = elDom.getAttribute("data-element-data");
				if (elementData) {
					try {
						const parsedElement = JSON.parse(elementData) as Element;
						relevantElements.push(parsedElement);
					} catch (e) {
						console.error("Failed to parse element data", e);
					}
				}
			}
		});
		otherElementsRef.current = relevantElements;
	};

	const handleDragStart = (e: React.MouseEvent) => {
		setIsDragging(true);
		onUpdate(element, true);

		startPosRef.current = {
			x: element.x,
			y: element.y,
			width: element.width,
			height: element.height,
			mouseX: e.clientX,
			mouseY: e.clientY,
		};

		// Update other elements for snapping
		updateOtherElements();
	};

	const handleResizeStart = (
		e: React.MouseEvent,
		handleType: ResizeHandleType,
	) => {
		e.stopPropagation();
		setIsResizing(true);
		setActiveHandle(handleType);
		onUpdate(element, true);

		startPosRef.current = {
			x: element.x,
			y: element.y,
			width: element.width,
			height: element.height,
			mouseX: e.clientX,
			mouseY: e.clientY,
		};
	};

	const handleDrag = useCallback(
		(e: MouseEvent) => {
			if (!isDragging) return;

			const dx = e.clientX - startPosRef.current.mouseX;
			const dy = e.clientY - startPosRef.current.mouseY;
			const dxMm = pxToMm(dx);
			const dyMm = pxToMm(dy);

			let newX = roundToTwoDecimals(startPosRef.current.x + dxMm);
			let newY = roundToTwoDecimals(startPosRef.current.y + dyMm);

			let snapX: number | null = null;
			let snapY: number | null = null;
			let guides: SnapGuideInfo[] = [];

			if (calculateSnapGuides) {
				const snapResult = calculateSnapGuides(
					element.id,
					newX,
					newY,
					element.width,
					element.height,
					element.parentId || null,
				);

				snapX = snapResult.snapX;
				snapY = snapResult.snapY;
				guides = snapResult.guides;
			}

			// Apply snapping if needed
			if (snapX !== null) {
				if (Math.abs(newX - snapX) <= SNAP_THRESHOLD_MM) newX = snapX;
				else if (
					Math.abs(newX + element.width / 2 - snapX) <= SNAP_THRESHOLD_MM
				)
					newX = snapX - element.width / 2;
				else if (Math.abs(newX + element.width - snapX) <= SNAP_THRESHOLD_MM)
					newX = snapX - element.width;
			}
			if (snapY !== null) {
				if (Math.abs(newY - snapY) <= SNAP_THRESHOLD_MM) newY = snapY;
				else if (
					Math.abs(newY + element.height / 2 - snapY) <= SNAP_THRESHOLD_MM
				)
					newY = snapY - element.height / 2;
				else if (Math.abs(newY + element.height - snapY) <= SNAP_THRESHOLD_MM)
					newY = snapY - element.height;
			}

			newX = roundToTwoDecimals(newX);
			newY = roundToTwoDecimals(newY);

			if (updateSnapGuides) {
				if (editingBlockId && element.parentId === editingBlockId) {
					updateSnapGuides(guides.length > 0, guides, element.parentId);
				} else {
					updateSnapGuides(guides.length > 0, guides, null, null);
				}
			}

			onUpdate({ ...element, x: newX, y: newY }, false);
		},
		[
			isDragging,
			element,
			onUpdate,
			calculateSnapGuides,
			updateSnapGuides,
			editingBlockId,
		],
	);

	const handleResize = useCallback(
		(e: MouseEvent) => {
			if (!isResizing || !activeHandle) return;
			e.preventDefault();

			const startState = startPosRef.current;
			const rotationRad = (element.rotation * Math.PI) / 180;
			const cosTheta = Math.cos(rotationRad);
			const sinTheta = Math.sin(rotationRad);

			const dx_px = e.clientX - startState.mouseX;
			const dy_px = e.clientY - startState.mouseY;
			const dx_local_px = dx_px * cosTheta + dy_px * sinTheta;
			const dy_local_px = -dx_px * sinTheta + dy_px * cosTheta;
			const dxMm_local = pxToMm(dx_local_px);
			const dyMm_local = pxToMm(dy_local_px);

			let newWidth = startState.width;
			let newHeight = startState.height;

			switch (activeHandle) {
				case "top":
					newHeight = startState.height - dyMm_local;
					break;
				case "bottom":
					newHeight = startState.height + dyMm_local;
					break;
				case "left":
					newWidth = startState.width - dxMm_local;
					break;
				case "right":
					newWidth = startState.width + dxMm_local;
					break;
				case "top-left":
					newWidth = startState.width - dxMm_local;
					newHeight = startState.height - dyMm_local;
					break;
				case "top-right":
					newWidth = startState.width + dxMm_local;
					newHeight = startState.height - dyMm_local;
					break;
				case "bottom-left":
					newWidth = startState.width - dxMm_local;
					newHeight = startState.height + dyMm_local;
					break;
				case "bottom-right":
					newWidth = startState.width + dxMm_local;
					newHeight = startState.height + dyMm_local;
					break;
			}

			const minSize = 5; // Minimum element size in mm
			newWidth = Math.max(minSize, newWidth);
			newHeight = Math.max(minSize, newHeight);

			// Step 2: Store fixed point
			const fixedPointType = handleToFixedPointMap[activeHandle];
			const fixedPointScreenPos = calculatePointPosition(
				startState.x,
				startState.y,
				startState.width,
				startState.height,
				rotationRad,
				fixedPointType,
			);

			// Step 3: Aspect Ratio Pass 1
			const shouldPreserveAspectRatio =
				(element.type === "image" && element.aspectRatio) ||
				element.type === "table";
			if (shouldPreserveAspectRatio) {
				const startWidth = startState.width;
				const startHeight = startState.height;
				const aspectRatio =
					element.aspectRatio ||
					(startWidth > 0 && startHeight > 0 ? startWidth / startHeight : 1);
				let primaryAxisForAspectRatioPass1: "width" | "height" | "corner" =
					"corner";

				if (activeHandle.includes("left") || activeHandle.includes("right")) {
					primaryAxisForAspectRatioPass1 = "width";
				} else if (
					activeHandle.includes("top") ||
					activeHandle.includes("bottom")
				) {
					primaryAxisForAspectRatioPass1 = "height";
				}

				if (aspectRatio > 0) {
					if (primaryAxisForAspectRatioPass1 === "width") {
						// Width changed primarily
						newHeight = newWidth / aspectRatio;
					} else if (primaryAxisForAspectRatioPass1 === "height") {
						// Height changed primarily
						newWidth = newHeight * aspectRatio;
					} else {
						// Corner handles: adjust based on which dimension changed more proportionally
						const widthChangeRatio =
							startWidth > 0
								? Math.abs((newWidth - startWidth) / startWidth)
								: Infinity;
						const heightChangeRatio =
							startHeight > 0
								? Math.abs((newHeight - startHeight) / startHeight)
								: Infinity;
						if (widthChangeRatio >= heightChangeRatio) {
							// Width change is dominant or equal
							newHeight = newWidth / aspectRatio;
							primaryAxisForAspectRatioPass1 = "width"; // Record dominant axis for secondary adjustment
						} else {
							// Height change is dominant
							newWidth = newHeight * aspectRatio;
							primaryAxisForAspectRatioPass1 = "height"; // Record dominant axis
						}
					}
					newWidth = Math.max(minSize, newWidth);
					newHeight = Math.max(minSize, newHeight);

					// Second pass to ensure consistency after one dimension is set based on primary change
					if (primaryAxisForAspectRatioPass1 === "width") {
						newHeight = Math.max(minSize, newWidth / aspectRatio);
					} else {
						// Covers 'height' and 'corner' where height might have been dominant or default
						newWidth = Math.max(minSize, newHeight * aspectRatio);
					}
				}
			}
			newWidth = Math.max(minSize, newWidth);
			newHeight = Math.max(minSize, newHeight);

			// Step 4: Calculate Position based on Fixed Point (using dimensions from Step 3)
			let { x: effectiveX, y: effectiveY } = calculateTopLeftFromFixedPoint(
				fixedPointScreenPos.x,
				fixedPointScreenPos.y,
				newWidth,
				newHeight,
				rotationRad,
				fixedPointType,
			);
			let effectiveWidth = newWidth;
			let effectiveHeight = newHeight;

			// Step 5: Get Snap Guides
			let snapX: number | null = null;
			let snapY: number | null = null;
			let guides: SnapGuideInfo[] = [];

			if (calculateSnapGuides) {
				const snapResult = calculateSnapGuides(
					element.id,
					effectiveX,
					effectiveY,
					effectiveWidth,
					effectiveHeight,
					element.parentId || null,
					activeHandle || undefined,
				);

				snapX = snapResult.snapX;
				snapY = snapResult.snapY;
				guides = snapResult.guides;
			}

			// Step 6: Apply Snapping
			let snappedX = false;
			let snappedY = false;

			if (snapX !== null) {
				const currentElemRight = effectiveX + effectiveWidth;
				const currentElemCenterX = effectiveX + effectiveWidth / 2;

				// Horizontal snapping for left/right and corner handles
				if (activeHandle.includes("left")) {
					// top-left, left, bottom-left
					// Try to snap the left edge
					if (Math.abs(effectiveX - snapX) <= SNAP_THRESHOLD_MM) {
						const delta = snapX - effectiveX;
						effectiveX = snapX;
						effectiveWidth -= delta;
						snappedX = true;
					}
					// If not, try to snap center (only if it's a pure 'left' handle, less likely for corners to jump to center)
					else if (
						activeHandle === "left" &&
						Math.abs(currentElemCenterX - snapX) <= SNAP_THRESHOLD_MM
					) {
						effectiveX = snapX - effectiveWidth / 2;
						snappedX = true;
					}
				} else if (activeHandle.includes("right")) {
					// top-right, right, bottom-right
					// Try to snap the right edge
					if (Math.abs(currentElemRight - snapX) <= SNAP_THRESHOLD_MM) {
						effectiveWidth = snapX - effectiveX;
						snappedX = true;
					}
					// If not, try to snap center (only if it's a pure 'right' handle)
					else if (
						activeHandle === "right" &&
						Math.abs(currentElemCenterX - snapX) <= SNAP_THRESHOLD_MM
					) {
						effectiveX = snapX - effectiveWidth / 2;
						snappedX = true;
					}
				}
			}

			if (snapY !== null) {
				const currentElemBottom = effectiveY + effectiveHeight;
				const currentElemCenterY = effectiveY + effectiveHeight / 2;

				// Vertical snapping for top/bottom and corner handles
				if (activeHandle.includes("top")) {
					// top-left, top, top-right
					// Try to snap the top edge
					if (Math.abs(effectiveY - snapY) <= SNAP_THRESHOLD_MM) {
						const delta = snapY - effectiveY;
						effectiveY = snapY;
						effectiveHeight -= delta;
						snappedY = true;
					}
					// If not, try to snap center (only if it's a pure 'top' handle)
					else if (
						activeHandle === "top" &&
						Math.abs(currentElemCenterY - snapY) <= SNAP_THRESHOLD_MM
					) {
						effectiveY = snapY - effectiveHeight / 2;
						snappedY = true;
					}
				} else if (activeHandle.includes("bottom")) {
					// bottom-left, bottom, bottom-right
					// Try to snap the bottom edge
					if (Math.abs(currentElemBottom - snapY) <= SNAP_THRESHOLD_MM) {
						effectiveHeight = snapY - effectiveY;
						snappedY = true;
					}
					// If not, try to snap center (only if it's a pure 'bottom' handle)
					else if (
						activeHandle === "bottom" &&
						Math.abs(currentElemCenterY - snapY) <= SNAP_THRESHOLD_MM
					) {
						effectiveY = snapY - effectiveHeight / 2;
						snappedY = true;
					}
				}
			}

			// Fallback for corner handles if specific edges didn't snap: try center-to-center if close
			if (
				(activeHandle.includes("left") || activeHandle.includes("right")) &&
				(activeHandle.includes("top") || activeHandle.includes("bottom"))
			) {
				// It's a corner handle
				if (snapX !== null && !snappedX) {
					// If X hasn't been snapped by edge logic
					const currentElemCenterX = effectiveX + effectiveWidth / 2;
					if (Math.abs(currentElemCenterX - snapX) <= SNAP_THRESHOLD_MM * 0.5) {
						// More conservative threshold for center snap
						effectiveX = snapX - effectiveWidth / 2;
						snappedX = true;
					}
				}
				if (snapY !== null && !snappedY) {
					// If Y hasn't been snapped by edge logic
					const currentElemCenterY = effectiveY + effectiveHeight / 2;
					if (Math.abs(currentElemCenterY - snapY) <= SNAP_THRESHOLD_MM * 0.5) {
						// More conservative threshold
						effectiveY = snapY - effectiveHeight / 2;
						snappedY = true;
					}
				}
			}

			effectiveWidth = Math.max(minSize, effectiveWidth);
			effectiveHeight = Math.max(minSize, effectiveHeight);

			// Step 7: Aspect Ratio Pass 2 (applied after snapping)
			if (shouldPreserveAspectRatio) {
				const originalSnappedWidth = effectiveWidth;
				const originalSnappedHeight = effectiveHeight;
				const aspectRatio =
					element.aspectRatio ||
					(startState.width > 0 && startState.height > 0
						? startState.width / startState.height
						: 1);
				let primaryAxisForAspectRatioPass2: "width" | "height" | "corner" =
					"corner";

				if (activeHandle.includes("left") || activeHandle.includes("right")) {
					primaryAxisForAspectRatioPass2 = "width";
				} else if (
					activeHandle.includes("top") ||
					activeHandle.includes("bottom")
				) {
					primaryAxisForAspectRatioPass2 = "height";
				}

				if (aspectRatio > 0) {
					if (primaryAxisForAspectRatioPass2 === "width") {
						effectiveHeight = effectiveWidth / aspectRatio;
					} else if (primaryAxisForAspectRatioPass2 === "height") {
						effectiveWidth = effectiveHeight * aspectRatio;
					} else {
						// Corner case
						// Heuristic: if a dimension was significantly changed by snapping, prioritize that.
						const widthActuallyChangedFromMouseAndSnap =
							Math.abs(originalSnappedWidth - effectiveWidth) >
							SNAP_THRESHOLD_MM / 2;
						const heightActuallyChangedFromMouseAndSnap =
							Math.abs(originalSnappedHeight - effectiveHeight) >
							SNAP_THRESHOLD_MM / 2;

						if (
							widthActuallyChangedFromMouseAndSnap &&
							!heightActuallyChangedFromMouseAndSnap
						) {
							effectiveHeight = effectiveWidth / aspectRatio;
							primaryAxisForAspectRatioPass2 = "width";
						} else if (
							heightActuallyChangedFromMouseAndSnap &&
							!widthActuallyChangedFromMouseAndSnap
						) {
							effectiveWidth = effectiveHeight * aspectRatio;
							primaryAxisForAspectRatioPass2 = "height";
						} else {
							// Both or neither changed significantly, use dominant mouse-driven change from startState
							const overallWidthChangeRatio =
								startState.width > 0
									? Math.abs(
											(originalSnappedWidth - startState.width) /
												startState.width,
										)
									: Infinity;
							const overallHeightChangeRatio =
								startState.height > 0
									? Math.abs(
											(originalSnappedHeight - startState.height) /
												startState.height,
										)
									: Infinity;
							if (overallWidthChangeRatio >= overallHeightChangeRatio) {
								effectiveHeight = effectiveWidth / aspectRatio;
								primaryAxisForAspectRatioPass2 = "width";
							} else {
								effectiveWidth = effectiveHeight * aspectRatio;
								primaryAxisForAspectRatioPass2 = "height";
							}
						}
					}
					effectiveWidth = Math.max(minSize, effectiveWidth);
					effectiveHeight = Math.max(minSize, effectiveHeight);

					// Second pass for consistency
					if (primaryAxisForAspectRatioPass2 === "width") {
						effectiveHeight = Math.max(minSize, effectiveWidth / aspectRatio);
					} else {
						// Covers height and corner cases after primary decision
						effectiveWidth = Math.max(minSize, effectiveHeight * aspectRatio);
					}
				}
			}
			effectiveWidth = Math.max(minSize, effectiveWidth);
			effectiveHeight = Math.max(minSize, effectiveHeight);

			// Step 8: Final Position Recalculation
			const { x: finalX, y: finalY } = calculateTopLeftFromFixedPoint(
				fixedPointScreenPos.x,
				fixedPointScreenPos.y,
				effectiveWidth,
				effectiveHeight,
				rotationRad,
				fixedPointType,
			);

			const finalNewWidth = roundToTwoDecimals(effectiveWidth);
			const finalNewHeight = roundToTwoDecimals(effectiveHeight);
			const finalNewX = roundToTwoDecimals(finalX);
			const finalNewY = roundToTwoDecimals(finalY);

			if (updateSnapGuides) {
				if (editingBlockId && element.parentId === editingBlockId) {
					updateSnapGuides(guides.length > 0, guides, element.parentId);
				} else {
					updateSnapGuides(guides.length > 0, guides, null, null);
				}
			}

			onUpdate(
				{
					...element,
					x: finalNewX,
					y: finalNewY,
					width: finalNewWidth,
					height: finalNewHeight,
				},
				false,
			);
		},
		[
			isResizing,
			activeHandle,
			element,
			onUpdate,
			calculateSnapGuides,
			updateSnapGuides,
			editingBlockId,
		],
	);

	// Mouse event listeners
	useEffect(() => {
		const handleMouseMove = (e: MouseEvent) => {
			if (isDragging) handleDrag(e);
			if (isResizing) handleResize(e);
		};

		const handleMouseUp = () => {
			if (isDragging || isResizing) {
				onUpdate(element, true); // Commit final state
			}
			setIsDragging(false);
			setIsResizing(false);
			setActiveHandle(null);
			if (updateSnapGuides) {
				updateSnapGuides(false, [], null, null); // Clear guides with null block context
			}
			if (throttleRef.current !== null) {
				cancelAnimationFrame(throttleRef.current);
				throttleRef.current = null;
			}
		};

		if (isDragging || isResizing) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);
		}

		return () => {
			document.removeEventListener("mousemove", handleMouseMove);
			document.removeEventListener("mouseup", handleMouseUp);
			if (throttleRef.current !== null) {
				cancelAnimationFrame(throttleRef.current);
				throttleRef.current = null;
			}
		};
	}, [
		isDragging,
		isResizing,
		element,
		onUpdate,
		handleDrag,
		handleResize,
		updateSnapGuides,
	]);

	return {
		isDragging,
		isResizing,
		activeHandle,
		handleDragStart,
		handleResizeStart,
	};
}
