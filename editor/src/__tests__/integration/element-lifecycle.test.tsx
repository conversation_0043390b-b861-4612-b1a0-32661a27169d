import { act, renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useElementOperations } from "../../hooks/useElementOperations";
import type { Element } from "../../types/element";
import type { PageSettings } from "../../types/page";
import { ElementFactory, PageFactory } from "../utils/mock-factories";

// Mock uuid to have predictable IDs in tests
vi.mock("uuid", () => ({
	v4: vi.fn(() => `test-uuid-${Math.random().toString(36).substring(2, 9)}`),
}));

describe("Element Lifecycle Integration Tests", () => {
	let mockPages: PageSettings[];
	let mockElements: Element[];
	let mockSetEditorState: ReturnType<typeof vi.fn>;
	let mockCommitBatch: ReturnType<typeof vi.fn>;
	let mockBeginBatch: ReturnType<typeof vi.fn>;
	let mockUpdateDraft: ReturnType<typeof vi.fn>;
	let mockIsBatching: ReturnType<typeof vi.fn>;
	let mockSetSelectedElementId: ReturnType<typeof vi.fn>;
	let mockSetEditingBlockId: ReturnType<typeof vi.fn>;
	let mockSetDeletedPageNumberIds: ReturnType<typeof vi.fn>;
	let mockSetClipboard: ReturnType<typeof vi.fn>;

	beforeEach(() => {
		// Create initial test state
		mockPages = [PageFactory.createPage({ id: "page-1" })];
		mockElements = [
			ElementFactory.createTextElement({
				id: "text-1",
				currentPageId: "page-1",
			}),
			ElementFactory.createShapeElement({
				id: "shape-1",
				currentPageId: "page-1",
			}),
		];

		// Create mock functions
		mockSetEditorState = vi.fn();
		mockCommitBatch = vi.fn();
		mockBeginBatch = vi.fn();
		mockUpdateDraft = vi.fn();
		mockIsBatching = vi.fn().mockReturnValue(false);
		mockSetSelectedElementId = vi.fn();
		mockSetEditingBlockId = vi.fn();
		mockSetDeletedPageNumberIds = vi.fn();
		mockSetClipboard = vi.fn();
	});

	describe("Complete Element Creation → Modification → Deletion Workflow", () => {
		it("should handle complete element lifecycle from creation to deletion", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			// Step 1: Create a new element
			const newElement = ElementFactory.createTextElement({
				id: "new-text",
				currentPageId: "page-1",
				content: "New element content",
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			// Verify element was added
			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [...mockElements, newElement],
			});
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("new-text");

			// Update the elements array to include the new element for the next step
			const elementsAfterAdd = [...mockElements, newElement];

			// Create a new hook instance with updated elements
			const { result: resultAfterAdd } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: elementsAfterAdd,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			// Step 2: Modify the element
			const modifiedElement = {
				...newElement,
				content: "Modified content",
				x: 100,
				y: 150,
			};

			act(() => {
				resultAfterAdd.current.handleUpdateElement(modifiedElement, true);
			});

			// Verify element was updated
			expect(mockCommitBatch).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [mockElements[0], mockElements[1], modifiedElement],
			});

			// Step 3: Delete the element
			// Create another hook instance with the modified elements for deletion
			const elementsAfterModify = [
				mockElements[0],
				mockElements[1],
				modifiedElement,
			];
			const { result: resultAfterModify } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: elementsAfterModify,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			act(() => {
				resultAfterModify.current.handleDeleteElement("new-text");
			});

			// Verify element was deleted (should call setEditorState with original elements)
			expect(mockSetEditorState).toHaveBeenLastCalledWith({
				pages: mockPages,
				elements: mockElements,
			});
		});

		it("should handle element creation with proper z-index assignment", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			// Create element without z-index
			const newElement = ElementFactory.createTextElement({
				id: "new-element",
				currentPageId: "page-1",
				zIndex: undefined,
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			// Should assign z-index higher than existing elements
			const expectedZIndex =
				Math.max(...mockElements.map((el) => el.zIndex || 0)) + 1;
			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [...mockElements, { ...newElement, zIndex: expectedZIndex }],
			});
		});

		it("should handle element modification with z-index reordering", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			// Move element to front
			const elementToMove = {
				...mockElements[0],
				_action: "moveToFront" as const,
			};

			act(() => {
				result.current.handleUpdateElement(elementToMove, true);
			});

			// Should reorder z-indices
			expect(mockCommitBatch).toHaveBeenCalled();
			const callArgs = mockCommitBatch.mock.calls[0][0];
			const reorderedElements = callArgs.elements;

			// Find the moved element and verify it has the highest z-index
			const movedElement = reorderedElements.find(
				(el: Element) => el.id === mockElements[0].id,
			);
			const otherElements = reorderedElements.filter(
				(el: Element) => el.id !== mockElements[0].id,
			);
			const maxOtherZIndex = Math.max(
				...otherElements.map((el: Element) => el.zIndex || 0),
			);

			expect(movedElement.zIndex).toBeGreaterThan(maxOtherZIndex);
		});

		it("should handle element deletion with cleanup", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: "text-1",
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			act(() => {
				result.current.handleDeleteElement("text-1");
			});

			// Should remove element and clear selection if it was selected
			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [mockElements[1]], // Only shape-1 should remain
			});
		});
	});

	describe("Element Copy/Paste and Duplication Workflows", () => {
		it("should handle complete copy and paste workflow", () => {
			let clipboard:
				| Element
				| { block: Element; children: Element[] }
				| { childElement: Element; originalParentId: string }
				| null = null;
			const mockSetClipboardLocal = vi.fn((value) => {
				clipboard = value;
			});

			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			// Step 1: Copy an element
			act(() => {
				result.current.handleCopyElement("text-1");
			});

			expect(mockSetClipboardLocal).toHaveBeenCalledWith(mockElements[0]);
			clipboard = mockElements[0]; // Simulate clipboard update

			// Step 2: Paste the element
			const { result: resultWithClipboard } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			act(() => {
				resultWithClipboard.current.handlePasteAction("page-1", 200, 200);
			});

			// Should create a new element with new ID and position
			expect(mockSetEditorState).toHaveBeenLastCalledWith(
				expect.objectContaining({
					pages: mockPages,
					elements: expect.arrayContaining([
						...mockElements,
						expect.objectContaining({
							id: expect.not.stringMatching(mockElements[0].id),
							x: expect.any(Number),
							y: 200,
							currentPageId: "page-1",
						}),
					]),
				}),
			);
		});

		it("should handle cut and paste workflow", () => {
			let clipboard:
				| Element
				| { block: Element; children: Element[] }
				| { childElement: Element; originalParentId: string }
				| null = null;
			const mockSetClipboardLocal = vi.fn((value) => {
				clipboard = value;
			});

			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			// Step 1: Cut an element (should copy and delete)
			act(() => {
				result.current.handleCutElement("text-1");
			});

			// Should copy to clipboard
			expect(mockSetClipboardLocal).toHaveBeenCalledWith(mockElements[0]);

			// Should delete the element
			expect(mockSetEditorState).toHaveBeenCalledWith({
				pages: mockPages,
				elements: [mockElements[1]], // Only shape-1 should remain
			});

			clipboard = mockElements[0]; // Simulate clipboard update

			// Step 2: Paste the element
			const { result: resultWithClipboard } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: [mockElements[1]], // Only shape-1 remains after cut
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			act(() => {
				resultWithClipboard.current.handlePasteAction("page-1", 300, 300);
			});

			// Should restore the element with new ID and position
			expect(mockSetEditorState).toHaveBeenLastCalledWith(
				expect.objectContaining({
					pages: mockPages,
					elements: expect.arrayContaining([
						mockElements[1], // Original shape element
						expect.objectContaining({
							id: expect.not.stringMatching(mockElements[0].id),
							x: expect.any(Number),
							y: 300,
							currentPageId: "page-1",
							content: mockElements[0].content, // Should preserve original content
						}),
					]),
				}),
			);
		});

		it("should handle block element copy and paste workflow", () => {
			// Create a block element with children
			const blockElement = ElementFactory.createBlockElement({
				id: "block-1",
				currentPageId: "page-1",
				childElementIds: ["child-1", "child-2"],
			});

			const childElements = [
				ElementFactory.createTextElement({
					id: "child-1",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
				ElementFactory.createShapeElement({
					id: "child-2",
					currentPageId: "page-1",
					parentId: "block-1",
				}),
			];

			const elementsWithBlock = [
				...mockElements,
				blockElement,
				...childElements,
			];
			let clipboard:
				| Element
				| { block: Element; children: Element[] }
				| { childElement: Element; originalParentId: string }
				| null = null;
			const mockSetClipboardLocal = vi.fn((value) => {
				clipboard = value;
			});

			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: elementsWithBlock,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			// Copy the block element
			act(() => {
				result.current.handleCopyElement("block-1");
			});

			// Should copy block with children
			expect(mockSetClipboardLocal).toHaveBeenCalledWith({
				block: blockElement,
				children: childElements,
			});

			clipboard = { block: blockElement, children: childElements };

			// Paste the block
			const { result: resultWithClipboard } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: elementsWithBlock,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			act(() => {
				resultWithClipboard.current.handlePasteAction("page-1", 400, 400);
			});

			// Should create new block with new children
			const lastCall =
				mockSetEditorState.mock.calls[
					mockSetEditorState.mock.calls.length - 1
				][0];
			const newElements = lastCall.elements;

			// Should have original elements plus new block and children
			expect(newElements.length).toBe(elementsWithBlock.length + 3); // +1 block, +2 children

			// Find the new block (should have different ID but same type)
			const newBlocks = newElements.filter(
				(el: Element) => el.type === "block" && el.id !== "block-1",
			);
			expect(newBlocks).toHaveLength(1);

			const newBlock = newBlocks[0];
			expect(newBlock.y).toBe(400);
			expect(newBlock.childElementIds).toHaveLength(2);
		});

		it("should handle cross-page element copy and paste workflow", () => {
			// Create a second page
			const secondPage = PageFactory.createPage({ id: "page-2" });
			const pagesWithSecond = [...mockPages, secondPage];

			let clipboard:
				| Element
				| { block: Element; children: Element[] }
				| { childElement: Element; originalParentId: string }
				| null = null;
			const mockSetClipboardLocal = vi.fn((value) => {
				clipboard = value;
			});

			const { result } = renderHook(() =>
				useElementOperations({
					pages: pagesWithSecond,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			// Copy an element from page 1
			act(() => {
				result.current.handleCopyElement("text-1");
			});

			expect(mockSetClipboardLocal).toHaveBeenCalledWith(mockElements[0]);
			clipboard = mockElements[0]; // Simulate clipboard update

			// Paste to page 2
			const { result: resultWithClipboard } = renderHook(() =>
				useElementOperations({
					pages: pagesWithSecond,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-2", // Now on page 2
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			act(() => {
				resultWithClipboard.current.handlePasteAction("page-2", 200, 200);
			});

			// Should create a new element on page 2
			expect(mockSetEditorState).toHaveBeenLastCalledWith(
				expect.objectContaining({
					pages: pagesWithSecond,
					elements: expect.arrayContaining([
						...mockElements,
						expect.objectContaining({
							id: expect.not.stringMatching(mockElements[0].id),
							currentPageId: "page-2",
							content: mockElements[0].content,
						}),
					]),
				}),
			);
		});

		it("should prevent copying address field elements", () => {
			const addressElement = ElementFactory.createAddressElement({
				id: "address-1",
				currentPageId: "page-1",
				isAddressField: true,
			});

			const elementsWithAddress = [...mockElements, addressElement];
			const mockSetClipboardLocal = vi.fn();

			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: elementsWithAddress,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			// Try to copy address field element
			act(() => {
				result.current.handleCopyElement("address-1");
			});

			// Should not copy address field elements
			expect(mockSetClipboardLocal).not.toHaveBeenCalled();
		});
	});

	describe("Element Lifecycle Edge Cases", () => {
		it("should handle locked editor state", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: true, // Editor is locked
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			const newElement = ElementFactory.createTextElement({
				id: "new-element",
				currentPageId: "page-1",
			});

			// Try to add element when locked
			act(() => {
				result.current.handleAddElement(newElement);
			});

			// Should not add element
			expect(mockSetEditorState).not.toHaveBeenCalled();

			// Try to delete element when locked
			act(() => {
				result.current.handleDeleteElement("text-1");
			});

			// Should not delete element
			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should handle element creation without current page", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: null, // No current page
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			const newElement = ElementFactory.createTextElement({
				id: "new-element",
				currentPageId: "", // No page ID
			});

			act(() => {
				result.current.handleAddElement(newElement);
			});

			// Should not add element without page ID
			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should handle deletion of non-existent element", () => {
			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			act(() => {
				result.current.handleDeleteElement("non-existent-id");
			});

			// Should not crash or modify state
			expect(mockSetEditorState).not.toHaveBeenCalled();
		});
	});

	describe("Complex Element Workflows", () => {
		it("should handle batch element operations", () => {
			// Create multiple elements
			const newElements = [
				ElementFactory.createTextElement({
					id: "batch-text-1",
					currentPageId: "page-1",
				}),
				ElementFactory.createShapeElement({
					id: "batch-shape-1",
					currentPageId: "page-1",
				}),
				ElementFactory.createImageElement({
					id: "batch-image-1",
					currentPageId: "page-1",
				}),
			];

			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			// Add multiple elements at once
			act(() => {
				result.current.handleAddElements(newElements);
			});

			// Should add all elements in a single operation
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					elements: expect.arrayContaining([...mockElements, ...newElements]),
				}),
			);

			// Should select the last added element
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("batch-image-1");
		});

		it("should handle element duplication with proper positioning", () => {
			// Create an element to duplicate
			const elementToDuplicate = ElementFactory.createTextElement({
				id: "original-text",
				currentPageId: "page-1",
				x: 100,
				y: 100,
				content: "Original content",
			});

			const elementsWithOriginal = [...mockElements, elementToDuplicate];
			const clipboard = elementToDuplicate; // Start with element already in clipboard
			const mockSetClipboardLocal = vi.fn();

			// Reset the mock to clear previous calls
			mockSetEditorState.mockClear();

			const { result } = renderHook(() =>
				useElementOperations({
					pages: mockPages,
					elements: elementsWithOriginal,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: "page-1",
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: clipboard,
					setClipboard: mockSetClipboardLocal,
					isEditing: false,
				}),
			);

			// Paste at a new position (simulating duplication)
			act(() => {
				result.current.handlePasteAction("page-1", 150, 150);
			});

			// Should create a duplicate with new position
			expect(mockSetEditorState).toHaveBeenCalled();

			// Get the elements from the last call
			const lastCallElements = mockSetEditorState.mock.calls[0][0].elements;

			// Verify the new element was added
			expect(lastCallElements.length).toBeGreaterThan(
				elementsWithOriginal.length,
			);

			// Find the new element (should have different ID but same content)
			const newElements = lastCallElements.filter(
				(el) => el.id !== "original-text" && el.content === "Original content",
			);

			expect(newElements.length).toBe(1);
			expect(newElements[0].y).toBe(150);
		});
	});
});
