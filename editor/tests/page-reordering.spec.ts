import { expect, type Locator, type Page, test } from "@playwright/test";

const host = process.env.EDITOR_TEST_HOST;
if (!host) {
	throw new Error("EDITOR_TEST_HOST is not set");
}

test.describe("Page Reordering", () => {
	test.beforeEach(async ({ page }) => {
		await page.goto(`${host}/editor?id=105&version=latest`);
		// Wait for the page to fully load - use specific selector for main editor pages
		await page.waitForSelector('[data-page-id][role="button"]', {
			timeout: 10000,
		});
	});

	test("should show page minimap", async ({ page }) => {
		// Wait for minimap to be visible - use more specific selector
		await expect(
			page
				.locator('.fixed.right-4 .text-xs.font-semibold:has-text("Seiten")')
				.first(),
		).toBeVisible();

		// Check that the minimap contains page pairs
		const pagePairs = page.locator(
			'[role="application"][aria-label^="Seitenpaar"]',
		);
		const pairCount = await pagePairs.count();
		expect(pairCount).toBeGreaterThanOrEqual(1);
	});

	test("should allow page selection via minimap", async ({ page }) => {
		// Click on the second page pair in the minimap if it exists
		const secondPageButton = page.locator('button[title*="Seite 3"]').first();
		if (await secondPageButton.isVisible()) {
			await secondPageButton.click();

			// Wait for scroll animation to complete
			await page.waitForTimeout(1500);

			// Verify the page was selected (check for blue ring indicating current page)
			await expect(secondPageButton).toHaveClass(/ring-2/);
		}
	});

	test("should enable drag functionality when editor is not locked", async ({
		page,
	}) => {
		// Check that drag handles are visible and draggable
		const dragHandle = page.locator('[data-testid="drag-handle"]').first();
		await expect(dragHandle).toBeVisible();
	});

	test("should show drag instruction when dragging starts", async ({
		page,
	}) => {
		// Get the first drag handle
		const dragHandle = page.locator('[data-testid="drag-handle"]').first();
		await expect(dragHandle).toBeVisible();

		// Start drag via pointer events and verify instruction appears
		const dragHandleBox = await dragHandle.boundingBox();
		if (dragHandleBox) {
			// Start drag with mouse down
			await page.mouse.move(
				dragHandleBox.x + dragHandleBox.width / 2,
				dragHandleBox.y + dragHandleBox.height / 2,
			);
			await page.mouse.down();

			// Move slightly to ensure the application recognises the start of a drag
			await page.mouse.move(
				dragHandleBox.x + dragHandleBox.width / 2,
				dragHandleBox.y + dragHandleBox.height / 2 + 10,
			);

			// Should show drag instruction
			await expect(
				page.locator(
					"text=In den hervorgehobenen Bereichen ablegen zum Neuordnen",
				),
			).toBeVisible();

			// End drag
			await page.mouse.up();
		}
	});

	test("should reorder pages via drag and drop", async ({ page }) => {
		// Capture the initial DOM order of pages to verify reordering later
		const initialPageIds = await page
			.locator('[data-page-id][role="button"]')
			.evaluateAll((elements) =>
				elements.map((el) => el.getAttribute("data-page-id")),
			);

		const initialFirstId = (initialPageIds[0] || "") as string;

		// Determine drag handles and page pair information dynamically
		const dragHandles = page.locator('[data-testid="drag-handle"]');
		const _firstDragHandle = dragHandles.nth(0);
		const secondDragHandle = dragHandles.nth(1);

		// Count page pairs to identify the last pair index for a guaranteed valid drop zone
		const pagePairLocator = page.locator(
			'[role="application"][aria-label^="Seitenpaar"]',
		);
		const pairCount = await pagePairLocator.count();

		if ((await secondDragHandle.isVisible()) && pairCount > 1) {
			// Move the SECOND pair before the first pair (swap order)
			await performMouseDragAndDrop(page, secondDragHandle, 0, "before");

			// Wait until the first page id differs, indicating the order has changed
			await page.waitForFunction(
				(args) => {
					const [selector, firstId] = args;
					const el = document.querySelector(selector);
					return !!el && el.getAttribute("data-page-id") !== firstId;
				},
				['[data-page-id][role="button"]', initialFirstId],
				{ timeout: 5000 },
			);

			// Verify that the page order has changed by checking DOM order
			const finalPageIds = await page
				.locator('[data-page-id][role="button"]')
				.evaluateAll((elements) =>
					elements.map((el) => el.getAttribute("data-page-id")),
				);

			// The order should have changed
			expect(finalPageIds).not.toEqual(initialPageIds);

			// But all the same page IDs should still be present
			expect(finalPageIds.sort()).toEqual(initialPageIds.sort());

			// Should have same number of pages as before
			expect(finalPageIds).toHaveLength(initialPageIds.length);
		}
	});

	test("should highlight drop zones during drag", async ({ page }) => {
		const dragHandle = page.locator('[data-testid="drag-handle"]').first();
		await expect(dragHandle).toBeVisible();

		// Use mouse/pointer events to start drag state
		await performMouseDragStart(page, dragHandle);

		// Check for highlighted drop zones
		const dropZones = page.locator(".border-blue-300, .border-blue-500");
		const dropZoneCount = await dropZones.count();
		expect(dropZoneCount).toBeGreaterThanOrEqual(1);

		// No need to assert highlight here; drag visual feedback tested elsewhere.

		// End drag
		await page.mouse.up();
	});

	test("should not allow reordering when editor is locked", async ({
		page,
	}) => {
		// Lock the editor (this would need to be implemented based on your app's locking mechanism)
		// For now, just verify that drag handles become disabled when locked

		// Note: This test would need to be expanded based on how editor locking is implemented
		const dragHandle = page.locator('[data-testid="drag-handle"]').first();
		await expect(dragHandle).toBeVisible();
	});
});

// Helper function for mouse-based drag and drop (more reliable for Chrome/Safari)
async function performMouseDragAndDrop(
	page: Page,
	source: Locator,
	targetPairIndex: number,
	dropType: "before" | "after" = "before",
) {
	const sourceBox = await source.boundingBox();
	if (!sourceBox) return;

	// Start drag on source handle
	await page.mouse.move(
		sourceBox.x + sourceBox.width / 2,
		sourceBox.y + sourceBox.height / 2,
	);
	await page.mouse.down();

	// Move slightly to ensure the application recognises the start of a drag
	await page.mouse.move(
		sourceBox.x + sourceBox.width / 2,
		sourceBox.y + sourceBox.height / 2 + 10,
	);

	// Wait for the corresponding drop zone (prefer "before" zone as it is always rendered
	// except for the pair right before the dragged one). Fallback to the "after" zone for
	// the last pair. This makes the helper more robust regardless of the total number of
	// page pairs in the document.
	const dropZone = page.locator(
		`[data-testid="drop-zone-${dropType}-${targetPairIndex}"]`,
	);
	await dropZone.waitFor({ state: "visible" });

	const targetBox = await dropZone.boundingBox();
	if (targetBox) {
		// Move into drop zone
		await page.mouse.move(
			targetBox.x + targetBox.width / 2,
			targetBox.y + targetBox.height / 2,
			{ steps: 15 },
		);

		// Wait until the drop zone shows the active highlight state
		await expect(dropZone).toHaveClass(/border-blue-500/);

		// Give the application a short moment to register the drag over the drop zone
		await page.waitForTimeout(100);
	}

	// Release mouse to drop
	await page.mouse.up();
}

// Helper function to start mouse drag
async function performMouseDragStart(page: Page, source: Locator) {
	const sourceBox = await source.boundingBox();

	if (sourceBox) {
		await page.mouse.move(
			sourceBox.x + sourceBox.width / 2,
			sourceBox.y + sourceBox.height / 2,
		);
		await page.mouse.down();

		// Move slightly to trigger drag state
		await page.mouse.move(
			sourceBox.x + sourceBox.width / 2,
			sourceBox.y + sourceBox.height / 2 + 10,
		);
	}
}
