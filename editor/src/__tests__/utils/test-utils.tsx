import {
	type RenderOptions,
	type RenderResult,
	render,
} from "@testing-library/react";
import React from "react";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";

// Define the editor state interface based on the hooks analysis
export interface EditorState {
	pages: PageSettings[];
	elements: Element[];
}

// Define render options interface
export interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
	initialState?: EditorState;
	apiMocks?: Record<string, unknown>;
	wrapperProps?: Record<string, unknown>;
}

// Mock context provider for testing
interface MockEditorContextValue {
	pages: PageSettings[];
	elements: Element[];
	setEditorState: (state: EditorState, addToHistory?: boolean) => void;
	getEditorState: () => EditorState;
	currentPage: PageSettings | null;
	setCurrentPage: (pageId: string) => void;
	selectedElementId: string | null;
	setSelectedElementId: (id: string | null) => void;
	editingBlockId: string | null;
	setEditingBlockId: (id: string | null) => void;
	isEditorLocked: boolean;
	setIsEditorLocked: (locked: boolean) => void;
	showPageNumbers: boolean;
	setShowPageNumbers: (show: boolean) => void;
	deletedPageNumberIds: Set<string>;
	setDeletedPageNumberIds: (ids: Set<string>) => void;
}

const MockEditorContext = React.createContext<MockEditorContextValue | null>(
	null,
);

// Mock provider component
interface MockEditorProviderProps {
	children: React.ReactNode;
	initialState?: EditorState;
	value?: Partial<MockEditorContextValue>;
}

function MockEditorProvider({
	children,
	initialState = { pages: [], elements: [] },
	value = {},
}: MockEditorProviderProps) {
	const [pages, setPages] = React.useState<PageSettings[]>(initialState.pages);
	const [elements, setElements] = React.useState<Element[]>(
		initialState.elements,
	);
	const [currentPage, setCurrentPageState] =
		React.useState<PageSettings | null>(initialState.pages[0] || null);
	const [selectedElementId, setSelectedElementId] = React.useState<
		string | null
	>(null);
	const [editingBlockId, setEditingBlockId] = React.useState<string | null>(
		null,
	);
	const [isEditorLocked, setIsEditorLocked] = React.useState(false);
	const [showPageNumbers, setShowPageNumbers] = React.useState(false);
	const [deletedPageNumberIds, setDeletedPageNumberIds] = React.useState<
		Set<string>
	>(new Set());

	const setEditorState = React.useCallback(
		(newState: EditorState, _addToHistory?: boolean) => {
			setPages(newState.pages);
			setElements(newState.elements);
		},
		[],
	);

	const getEditorState = React.useCallback((): EditorState => {
		return { pages, elements };
	}, [pages, elements]);

	const setCurrentPage = React.useCallback(
		(pageId: string) => {
			const page = pages.find((p) => p.id === pageId);
			if (page) {
				setCurrentPageState(page);
			}
		},
		[pages],
	);

	const contextValue: MockEditorContextValue = {
		pages,
		elements,
		setEditorState,
		getEditorState,
		currentPage,
		setCurrentPage,
		selectedElementId,
		setSelectedElementId,
		editingBlockId,
		setEditingBlockId,
		isEditorLocked,
		setIsEditorLocked,
		showPageNumbers,
		setShowPageNumbers,
		deletedPageNumberIds,
		setDeletedPageNumberIds,
		...value,
	};

	return (
		<MockEditorContext.Provider value={contextValue}>
			{children}
		</MockEditorContext.Provider>
	);
}

// Hook to use the mock context in tests
export function useMockEditorContext() {
	const context = React.useContext(MockEditorContext);
	if (!context) {
		throw new Error(
			"useMockEditorContext must be used within a MockEditorProvider",
		);
	}
	return context;
}

// Custom render function with provider wrappers
export function renderWithProviders(
	ui: React.ReactElement,
	options: CustomRenderOptions = {},
): RenderResult & { mockContext: MockEditorContextValue } {
	const {
		initialState = { pages: [], elements: [] },
		wrapperProps = {},
		...renderOptions
	} = options;

	// Store the mock context value for test access
	let mockContextValue: MockEditorContextValue;

	function Wrapper({ children }: { children: React.ReactNode }) {
		return (
			<MockEditorProvider initialState={initialState} value={wrapperProps}>
				<MockContextCapture>{children}</MockContextCapture>
			</MockEditorProvider>
		);
	}

	// Component to capture the context value for test access
	function MockContextCapture({ children }: { children: React.ReactNode }) {
		mockContextValue = useMockEditorContext();
		return <>{children}</>;
	}

	const result = render(ui, { wrapper: Wrapper, ...renderOptions });

	return {
		...result,
		// biome-ignore lint/style/noNonNullAssertion: lol
		mockContext: mockContextValue!,
	};
}

// Re-export everything from React Testing Library
export * from "@testing-library/react";
export { renderWithProviders as render };
