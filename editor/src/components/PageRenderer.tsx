import type React from "react";
import { CropMarks } from "@/components/CropMarks";
import { EditableElement } from "@/components/elements/EditableElement";
import { FoldMarks } from "@/components/FoldMarks";
import type { Editor } from "@/components/RichTextEditor";
import type { ResizeHandleType } from "@/hooks/useElementTransform";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import type { TestDataRecord } from "@/utils/apiService";

interface SnapGuideInfo {
	type: "horizontal" | "vertical";
	position: number;
	start: number;
	end: number;
}

interface PageRendererProps {
	page: PageSettings;
	elements: Element[];
	selectedElementId: string | null;
	editingBlockId: string | null;
	zoomLevel: number;
	showElementBorders: boolean;
	showFoldMarks: boolean;
	showPageNumbers: boolean;
	deletedPageNumberIds: Set<string>;
	testData: TestDataRecord[];
	selectedTestDataIndex: number;
	highlightVariables: boolean;
	isEditorLocked: boolean;

	// Event handlers
	onUpdateElement: (element: Element, isFinal: boolean) => void;
	onDeleteElement: (elementId: string) => void;
	onCopyElement: (elementId: string) => void;
	onCutElement: (elementId: string) => void;
	onSaveAsTemplate: (blockElement: Element, childElements: Element[]) => void;
	onSelectElement: (elementId: string | null) => void;
	onEditingChange: (isEditing: boolean) => void;
	onPageClick: (e: React.MouseEvent<HTMLDivElement>) => void;
	onPageContextMenu: (
		e: React.MouseEvent<HTMLDivElement>,
		pageDetails: PageSettings,
	) => void;

	// Refs and utilities
	pageRef: React.RefObject<HTMLDivElement | null>;
	setActiveEditor: (editor: Editor | null) => void;
	updateSnapGuides: (
		visible: boolean,
		guides: SnapGuideInfo[],
		relativeToBlockId?: string | null,
		blockInfo?: { x: number; y: number; width: number; height: number } | null,
	) => void;
	calculateSnapGuides: (
		elementId: string,
		newX: number,
		newY: number,
		elementWidth: number,
		elementHeight: number,
		parentBlockId?: string | null,
		movingHandle?: ResizeHandleType,
	) => { snapX: number | null; snapY: number | null; guides: SnapGuideInfo[] };
	setIsTextEditorFocused: (focused: boolean) => void;
	setEditingBlockId: (id: string | null) => void;
}

export function PageRenderer({
	page,
	elements,
	selectedElementId,
	editingBlockId,
	zoomLevel,
	showElementBorders,
	showFoldMarks,
	showPageNumbers,
	deletedPageNumberIds,
	testData,
	selectedTestDataIndex,
	highlightVariables,
	isEditorLocked,
	onUpdateElement,
	onDeleteElement,
	onCopyElement,
	onCutElement,
	onSaveAsTemplate,
	onSelectElement,
	onEditingChange,
	onPageClick,
	onPageContextMenu,
	pageRef,
	setActiveEditor,
	updateSnapGuides,
	calculateSnapGuides,
	setIsTextEditorFocused,
	setEditingBlockId,
}: PageRendererProps) {
	// Render elements for this page
	const renderElements = () => {
		// Get top-level elements for the given page (elements that are not children of a block)
		const topLevelElementsOnPage = elements.filter(
			(element) => element.currentPageId === page.id && !element.parentId,
		);

		// Sort elements by z-index and preserve original order for elements with same z-index
		const indexedElements = topLevelElementsOnPage.map((element, index) => ({
			element,
			originalIndex: index,
		}));

		indexedElements.sort((a, b) => {
			const zIndexA = a.element.zIndex ?? 0;
			const zIndexB = b.element.zIndex ?? 0;
			if (zIndexA !== zIndexB) {
				return zIndexA - zIndexB;
			}
			return a.originalIndex - b.originalIndex;
		});

		return indexedElements.map(({ element }) => {
			// If the element is a block, it will be responsible for rendering its children.
			// We'll pass all elements to EditableElement and let it handle child rendering if it's a block.
			const childElements =
				element.type === "block" && element.childElementIds
					? elements.filter((el) => element.childElementIds?.includes(el.id))
					: [];

			return (
				<EditableElement
					key={element.id}
					element={element}
					onUpdate={onUpdateElement}
					onDelete={onDeleteElement}
					onCopy={onCopyElement}
					onCut={onCutElement}
					onSaveAsTemplate={onSaveAsTemplate}
					isSelected={element.id === selectedElementId}
					onSelect={(id) => {
						onSelectElement(id);
						onEditingChange(false);
					}}
					setActiveEditor={setActiveEditor}
					onEditingChange={onEditingChange}
					testData={testData}
					selectedTestDataIndex={selectedTestDataIndex}
					highlightVariables={highlightVariables}
					showElementBorders={showElementBorders}
					updateSnapGuides={updateSnapGuides}
					isLocked={isEditorLocked}
					childElements={childElements}
					editingBlockId={editingBlockId}
					onEnterBlockEditMode={setEditingBlockId}
					globalSelectedElementId={selectedElementId}
					calculateSnapGuides={calculateSnapGuides}
					setIsTextEditorFocused={setIsTextEditorFocused}
				/>
			);
		});
	};

	// Calculate page dimensions in pixels
	const pageWidthPx = page.width * 10 * 3.7795275591; // Convert cm to px
	const pageHeightPx = page.height * 10 * 3.7795275591;

	// Calculate trim in pixels
	const trimTopPx = (page.trim_top ?? 0) * 10 * 3.7795275591;
	const trimRightPx = (page.trim_right ?? 0) * 10 * 3.7795275591;
	const trimBottomPx = (page.trim_bottom ?? 0) * 10 * 3.7795275591;
	const trimLeftPx = (page.trim_left ?? 0) * 10 * 3.7795275591;

	// Calculate content area dimensions
	const contentWidthPx = pageWidthPx - trimLeftPx - trimRightPx;
	const contentHeightPx = pageHeightPx - trimTopPx - trimBottomPx;

	// Check if page numbers should be shown for this page
	const shouldShowPageNumber =
		showPageNumbers &&
		!deletedPageNumberIds.has(page.id) &&
		typeof page.pagenumber_x === "number" &&
		typeof page.pagenumber_y === "number";

	return (
		<div
			role="application"
			key={page.id}
			className="page-container"
			style={{
				width: `${pageWidthPx}px`,
				height: `${pageHeightPx}px`,
				transform: `scale(${zoomLevel})`,
				transformOrigin: "top left",
				marginBottom: "20px",
				position: "relative",
				backgroundColor: "white",
				boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
				border: "1px solid #ddd",
			}}
			ref={pageRef}
			onClick={onPageClick}
			onKeyDown={(e) => {
				if (e.key === "Enter" || e.key === " ") {
					e.preventDefault();
					// Simulate a click by creating a synthetic mouse event
					const rect = e.currentTarget.getBoundingClientRect();
					const syntheticEvent = new MouseEvent("click", {
						bubbles: true,
						cancelable: true,
						clientX: rect.left + rect.width / 2,
						clientY: rect.top + rect.height / 2,
					});
					e.currentTarget.dispatchEvent(syntheticEvent);
				}
			}}
			onContextMenu={(e) => onPageContextMenu(e, page)}
		>
			{/* Crop marks */}
			<CropMarks
				pageWidth={pageWidthPx}
				pageHeight={pageHeightPx}
				trimTop={trimTopPx}
				trimRight={trimRightPx}
				trimBottom={trimBottomPx}
				trimLeft={trimLeftPx}
			/>

			{/* Fold marks */}
			{showFoldMarks && (
				<FoldMarks
					pageWidth={page.width}
					pageHeight={page.height}
					trimLeft={page.trim_left ?? 0}
					trimTop={page.trim_top ?? 0}
					outerWidth={
						page.width + (page.trim_left ?? 0) + (page.trim_right ?? 0)
					}
					outerHeight={
						page.height + (page.trim_top ?? 0) + (page.trim_bottom ?? 0)
					}
					pageFormat={undefined} // This would need to be passed as a prop
					showFullLines={showFoldMarks}
				/>
			)}

			{/* Content area */}
			<div
				className="page-content"
				style={{
					position: "absolute",
					left: `${trimLeftPx}px`,
					top: `${trimTopPx}px`,
					width: `${contentWidthPx}px`,
					height: `${contentHeightPx}px`,
					overflow: "visible",
				}}
			>
				{/* Page number */}
				{shouldShowPageNumber && (
					<div
						className="page-number"
						style={{
							position: "absolute",
							left: `${(page.pagenumber_x ?? 0) * 10 * 3.7795275591}px`,
							top: `${(page.pagenumber_y ?? 0) * 10 * 3.7795275591}px`,
							fontSize: "12px",
							color: "#666",
							pointerEvents: "none",
							userSelect: "none",
						}}
					>
						{Math.floor(elements.findIndex((p) => p.id === page.id) / 2) + 1}
					</div>
				)}

				{/* Render elements */}
				{renderElements()}
			</div>
		</div>
	);
}
