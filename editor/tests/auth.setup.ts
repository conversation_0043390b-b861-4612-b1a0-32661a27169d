import path from "node:path";
import { fileURLToPath } from "node:url";
import { test as setup } from "@playwright/test";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const authFile = path.join(__dirname, "../playwright/.auth/user.json");

const email = process.env.EDITOR_TEST_EMAIL;
if (!email) {
	throw new Error("EDITOR_TEST_EMAIL is not set");
}

const password = process.env.EDITOR_TEST_PASS;
if (!password) {
	throw new Error("EDITOR_TEST_PASSWORD is not set");
}

const host = process.env.EDITOR_TEST_HOST;
if (!host) {
	throw new Error("EDITOR_TEST_HOST is not set");
}

setup("authenticate", async ({ page }) => {
	// Perform authentication steps. Replace these actions with your own.
	await page.goto(`${host}/login`);
	await page.getByPlaceholder("Geben Sie Ihre E-Mail Adresse ein").fill(email);
	await page.getByPlaceholder("Geb<PERSON>e Ihr Passwort ein").fill(password);
	await page.getByRole("button", { name: "Anmelden" }).click();
	// Wait until the page receives the cookies.
	//
	// Sometimes login flow sets cookies in the process of several redirects.
	// Wait for the final URL to ensure that the cookies are actually set.
	await page.waitForURL(`${host}/dashboard`);

	await page.context().storageState({ path: authFile });
});
