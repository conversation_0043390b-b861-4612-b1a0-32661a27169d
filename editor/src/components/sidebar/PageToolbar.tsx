import {
	BookOpenText,
	FoldHorizontal,
	MapPin,
	Plus,
	Square,
} from "lucide-react";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Switch } from "@/components/ui/switch";
import type { BatchDeleteOperation, Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import { fetchPageFormats, type PageFormat } from "@/utils/apiService";
import { Button } from "../ui/button";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";

interface PageToolbarProps {
	onAddPage: (format: string) => void;
	onAddElement: (
		element: Element | import("../../types/element").BatchDeleteOperation,
	) => void;
	onAddElements: (elements: Element[]) => void;
	elements: Element[];
	currentPageId?: string;
	currentPageFormat?: string;
	apiFormats?: PageFormat[];
	showPageNumbers: boolean;
	onTogglePageNumbers: (show: boolean) => void;
	showElementBorders: boolean;
	setShowElementBorders: (show: boolean) => void;
	showFoldMarks: boolean;
	setShowFoldMarks: (show: boolean) => void;
	pages: PageSettings[];
	isReleased?: boolean;
}

export function PageToolbar({
	onAddPage,
	onAddElement,
	onAddElements,
	elements,
	currentPageId,
	currentPageFormat,
	apiFormats = [],
	showPageNumbers,
	onTogglePageNumbers,
	showElementBorders,
	setShowElementBorders,
	showFoldMarks,
	setShowFoldMarks,
	pages,
	isReleased = false,
}: PageToolbarProps) {
	const [format, setFormat] = useState("");
	const [addressEnabled, setAddressEnabled] = useState(false);
	const [pageFormats, setPageFormats] = useState<PageFormat[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isTogglingAddress, setIsTogglingAddress] = useState(false);

	// Fetch page formats from API
	useEffect(() => {
		const getPageFormats = async () => {
			try {
				setIsLoading(true);
				const formats = await fetchPageFormats();
				setPageFormats(formats);
				// Set the first format as default if available
				if (formats.length > 0) {
					setFormat(formats[0].name);
				}
				setError(null);
			} catch (err) {
				console.error("Failed to fetch page formats:", err);
				setError("Failed to load page formats");
				// Do not use hard-coded fallback formats – rely solely on API data
				setPageFormats([]);
				setFormat("");
			} finally {
				setIsLoading(false);
			}
		};

		getPageFormats();
	}, []);

	// Use API formats if passed as prop
	useEffect(() => {
		if (apiFormats && apiFormats.length > 0) {
			setPageFormats(apiFormats);
			// Set the first API format as default if format is still empty
			if (!format && apiFormats.length > 0) {
				setFormat(apiFormats[0].name);
			}
			setIsLoading(false);
		}
	}, [apiFormats, format]);

	// Check if an address element already exists on the current page
	useEffect(() => {
		console.log("[PageToolbar] useEffect checking for address elements");
		console.log("[PageToolbar] currentPageId:", currentPageId);
		console.log("[PageToolbar] elements.length:", elements.length);
		console.log("[PageToolbar] isTogglingAddress:", isTogglingAddress);

		// Skip automatic state sync if we're in the middle of a manual toggle operation
		if (isTogglingAddress) {
			console.log(
				"[PageToolbar] Skipping automatic address state sync during manual toggle",
			);
			return;
		}

		if (currentPageId && elements.length > 0) {
			const addressElements = elements.filter(
				(element) =>
					element.type === "address" && element.currentPageId === currentPageId,
			);

			const hasAddressElement = addressElements.length > 0;

			// Only update state if it's different to avoid unnecessary re-renders
			if (hasAddressElement !== addressEnabled) {
				setAddressEnabled(hasAddressElement);
			}
		} else {
			if (addressEnabled) {
				setAddressEnabled(false);
			}
		}
	}, [currentPageId, elements, addressEnabled, isTogglingAddress]);

	const handleAddressToggle = (checked: boolean) => {
		setAddressEnabled(checked);
		setIsTogglingAddress(true);

		// Only add/remove address if we have the necessary props
		if (onAddElement && currentPageId) {
			if (checked) {
				// Check if an address element already exists on this page
				const existingAddressElement = elements.find(
					(element) =>
						element.type === "address" &&
						element.currentPageId === currentPageId,
				);

				// Only add a new address element if one doesn't already exist
				if (!existingAddressElement) {
					// Find the current page format to get positioning data
					const formatData =
						currentPageFormat &&
						[...pageFormats, ...apiFormats].find(
							(f) => f.name === currentPageFormat,
						);

					// Abort if we cannot retrieve the required positioning information
					if (!formatData) {
						console.error(
							"[PageToolbar] Address toggle aborted: no format data found for",
							currentPageFormat,
						);
						setIsTogglingAddress(false);
						return;
					}

					const addressX = formatData.address_x;
					const addressY = formatData.address_y;
					const addressWidth = Number(formatData.address_w);
					const addressHeight = Number(formatData.address_h);
					const senderY = formatData.sender_y as number;
					const recipientY = formatData.recipient_y ?? 0;
					const blockerY = formatData.blocker_y as number;

					// Validate that all mandatory values are present
					if (
						[
							addressX,
							addressY,
							addressWidth,
							addressHeight,
							senderY,
							blockerY,
						].some(
							(v) => v === undefined || v === null || Number.isNaN(Number(v)),
						)
					) {
						console.error(
							"[PageToolbar] Address toggle aborted: incomplete address positioning data",
							formatData,
						);
						setIsTogglingAddress(false);
						return;
					}

					console.log("[PageToolbar] Using format positions:", {
						addressX,
						addressY,
						addressWidth,
						addressHeight,
						senderY,
						recipientY,
						blockerY,
					});

					// Create address container element
					const addressContainerId = uuidv4();
					const addressContainer: Element = {
						id: addressContainerId,
						type: "address",
						content: "",
						x: addressX,
						y: addressY,
						width: addressWidth,
						height: addressHeight,
						rotation: 0,
						currentPageId: currentPageId,
						isLocked: true, // Make it not movable or resizable
						isAddressField: true, // Special flag to identify as address field
						addressLayout: {
							sender_y: senderY,
							recipient_y: recipientY,
							blocker_y: blockerY,
						},
					};

					// Create sender text element
					const senderHeight =
						blockerY && blockerY > senderY ? blockerY - senderY : 20; // Ensure minimum height
					const senderElement: Element = {
						id: uuidv4(),
						type: "text",
						content:
							'<p><span style="font-family: NeoSansforeprimo-Regular; font-size: 9.5pt; color: #071E7F; line-height: 1.2;">eprimo GmbH • Postfach 12 61 • 66559 Ottweiler</span></p>',
						x: addressX,
						y: addressY + senderY, // Position relative to address container
						width: addressWidth,
						height: Math.max(senderHeight, 5), // Ensure minimum height
						rotation: 0,
						currentPageId: currentPageId,
						isLocked: true, // Make it not movable or resizable, but still editable
						isAddressField: true, // Mark as address-related for special handling
						// Remove parentId - this should be an independent element
					};

					// Create array of all elements to add in batch
					let elementsToAdd = [addressContainer, senderElement];

					if (recipientY > 0) {
						// Create recipient text element
						const recipientHeight = addressHeight - recipientY;
						const recipientElement: Element = {
							id: uuidv4(),
							type: "text",
							content:
								'<p><span style="font-family: NeoSansforeprimo-Regular; font-size: 9.5pt; color: #071E7F"; line-height: 1.2;>Hier sind die Adressvariablen zu befüllen.</span></p>',
							x: addressX,
							y: addressY + recipientY, // Position relative to address container
							width: addressWidth,
							height: recipientHeight,
							rotation: 0,
							currentPageId: currentPageId,
							isLocked: true, // Make it not movable or resizable, but still editable
							isAddressField: true, // Mark as address-related for special handling
							// Remove parentId - this should be an independent element
						};

						elementsToAdd = [...elementsToAdd, recipientElement];
					}

					// Add all elements as a batch to avoid race conditions
					if (onAddElements) {
						onAddElements(elementsToAdd);
						setIsTogglingAddress(false);
					}
				} else {
					setIsTogglingAddress(false);
				}
			} else {
				// Find all address-related elements for this page (container, sender, recipient)
				const addressRelatedElements = elements.filter(
					(element) =>
						element.currentPageId === currentPageId && element.isAddressField,
				);

				if (addressRelatedElements.length > 0) {
					// Create a batch deletion object that removes all elements at once
					const batchDeleteOperation: BatchDeleteOperation = {
						_batchDelete: true,
						elementIds: addressRelatedElements.map((el) => el.id),
						currentPageId: currentPageId,
					};

					onAddElement(batchDeleteOperation);
					setIsTogglingAddress(false);
				} else {
					setIsTogglingAddress(false);
				}
			}
		} else {
			setIsTogglingAddress(false);
		}
	};

	return (
		<div className="space-y-4">
			<div className="space-y-2">
				<label htmlFor="format-select" className="text-sm font-medium">
					Format
				</label>
				<Select value={format} onValueChange={setFormat} disabled={isLoading}>
					<SelectTrigger>
						<SelectValue placeholder={isLoading ? "Loading..." : "Format"} />
					</SelectTrigger>
					<SelectContent>
						{pageFormats.map((pageFormat) => (
							<SelectItem key={pageFormat.name} value={pageFormat.name}>
								{pageFormat.description}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				{error && <p className="text-xs text-red-500">{error}</p>}
			</div>

			<Button
				className="w-full"
				onClick={() => onAddPage(format)}
				disabled={isLoading || !format}
			>
				<Plus className="w-4 h-4 mr-2" />
				Seite hinzufügen
			</Button>

			{currentPageId && (
				<div className="pt-4 border-t mt-4 space-y-3">
					<div className="flex items-center space-x-2">
						<Switch
							id="address-mode"
							checked={addressEnabled}
							onCheckedChange={handleAddressToggle}
						/>
						<Label htmlFor="address-mode" className="flex items-center gap-2">
							<MapPin className="w-4 h-4" />
							Adresse anzeigen
						</Label>
					</div>
					<div className="flex items-center space-x-2">
						<Switch
							id="page-number-mode"
							checked={showPageNumbers}
							onCheckedChange={onTogglePageNumbers}
							disabled={!pages || pages.length === 0}
						/>
						<Label
							htmlFor="page-number-mode"
							className="flex items-center gap-2"
						>
							<BookOpenText className="w-4 h-4" />
							Seitenzahlen
						</Label>
					</div>
					<div className="flex items-center space-x-2">
						<Switch
							id="element-borders-mode"
							checked={showElementBorders}
							onCheckedChange={setShowElementBorders}
							disabled={isReleased}
						/>
						<Label
							htmlFor="element-borders-mode"
							className="flex items-center gap-2"
						>
							<Square className="w-4 h-4" />
							Rahmen anzeigen
						</Label>
					</div>
					<div className="flex items-center space-x-2">
						<Switch
							id="fold-marks-mode"
							checked={showFoldMarks}
							onCheckedChange={setShowFoldMarks}
							disabled={isReleased}
						/>
						<Label
							htmlFor="fold-marks-mode"
							className="flex items-center gap-2"
						>
							<FoldHorizontal className="w-4 h-4" />
							Falzmarkierungen
						</Label>
					</div>
				</div>
			)}
		</div>
	);
}
