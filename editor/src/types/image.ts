/**
 * Image folder interface representing a folder/tag
 */
export interface ImageFolder {
	id: number;
	folderName: string;
	parentId: number | null;
}

/**
 * Extended image info with folder assignments
 */
export interface ImageWithFolders {
	id?: number;
	thumb: string;
	image: string;
	originalName: string;
	folders: number[];
}

/**
 * Image clipboard state for copy/cut operations
 */
export interface ImageClipboard {
	images: ImageWithFolders[];
	operation: "copy" | "cut";
	timestamp: number;
}

/**
 * Request payload for updating image folder assignments
 */
export interface FolderUpdateRequest {
	images: Array<{
		imageId: number;
		folderIds: number[];
	}>;
}

/**
 * Response from folder update API
 */
export interface FolderUpdateResponse {
	message: string;
	updatedImages: Array<{
		imageId: number;
		folderIds: number[];
	}>;
}

/**
 * Image selection state for multi-select operations
 */
export interface ImageSelection {
	selectedImages: Set<string>;
	lastSelected: string | null;
}

/**
 * Upload progress state
 */
export interface UploadProgress {
	fileId: string;
	fileName: string;
	progress: number;
	status: "pending" | "uploading" | "success" | "error";
	error?: string;
}

/**
 * Context menu item interface
 */
export interface ContextMenuItem {
	id: string;
	label?: string;
	icon?: string;
	disabled?: boolean;
	separator?: boolean;
	submenu?: ContextMenuItem[];
	onClick?: () => void;
}

/**
 * Image browser view modes
 */
export type ImageViewMode = "grid" | "list";

/**
 * Image browser filter options
 */
export interface ImageFilters {
	folderId: number | null;
	searchTerm: string;
	showOnlySelected: boolean;
}
