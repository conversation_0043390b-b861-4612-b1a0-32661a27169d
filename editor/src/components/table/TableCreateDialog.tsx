import { useState } from "react";
import type { TableProperties } from "@/types/table";

interface TableEditorProps {
	onCreateTable: (properties: TableProperties) => void;
	onClose?: () => void;
}

const MAX_ROWS = 10;
const MAX_COLS = 10;

export function TableCreateDialog({
	onCreateTable,
	onClose,
}: TableEditorProps) {
	const [hoveredCell, setHoveredCell] = useState({ row: 0, col: 0 });

	const handleCellHover = (row: number, col: number) => {
		setHoveredCell({ row: row + 1, col: col + 1 });
	};

	const handleCellClick = (row: number, col: number) => {
		const selectedRows = row + 1;
		const selectedCols = col + 1;

		// Define default cell dimensions in mm
		const defaultCellWidthMm = 15; // Example default width in mm
		const defaultCellHeightMm = 10; // Example default height in mm

		onCreateTable({
			rows: selectedRows,
			columns: selectedCols,
			borderWidth: 1, // px
			borderStyle: "solid",
			cells: Array(selectedRows)
				.fill(null)
				.map(() =>
					Array(selectedCols)
						.fill(null)
						.map(() => ({
							content: "",
							colspan: 1,
							rowspan: 1,
							backgroundColor: null,
							verticalAlign: "top" as const,
							borderWidths: {
								top: 1, // px
								right: 1, // px
								bottom: 1, // px
								left: 1, // px
							},
						})),
				),
			// Initialize column widths and row heights in mm
			columnWidths: Array(selectedCols).fill(defaultCellWidthMm),
			rowHeights: Array(selectedRows).fill(defaultCellHeightMm),
		});

		onClose?.();
	};

	return (
		<div className="p-2 bg-white rounded-lg shadow-lg">
			<div className="mb-2 text-xs text-gray-600 text-center">
				{hoveredCell.row} x {hoveredCell.col}
			</div>
			<div
				className="grid gap-0.5"
				style={{ gridTemplateColumns: `repeat(${MAX_COLS}, 1fr)` }}
			>
				{Array.from({ length: MAX_ROWS }, (_, rowIndex) =>
					Array.from({ length: MAX_COLS }, (_, colIndex) => (
						<button
							type="button"
							key={`table-cell-${rowIndex * MAX_COLS + colIndex}`}
							className={`w-4 h-4 border transition-colors duration-100 ${
								rowIndex <= hoveredCell.row - 1 &&
								colIndex <= hoveredCell.col - 1
									? "bg-blue-500 border-blue-600"
									: "bg-gray-100 border-gray-300"
							} hover:bg-blue-500 hover:border-blue-600 cursor-pointer`}
							onMouseEnter={() => handleCellHover(rowIndex, colIndex)}
							onClick={() => handleCellClick(rowIndex, colIndex)}
						/>
					)),
				)}
			</div>
		</div>
	);
}
