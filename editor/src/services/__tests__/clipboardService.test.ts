import { beforeEach, describe, expect, it, vi } from "vitest";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import type { Element } from "../../types/element";
import type { PageSettings } from "../../types/page";
import * as clipboardService from "../clipboardService";
import * as elementService from "../elementService";

// Mock elementService functions
vi.mock("../elementService", () => ({
	cloneElement: vi.fn(),
	cloneBlockWithChildren: vi.fn(),
	validateElementBounds: vi.fn((element) => element),
	getElementsAtPosition: vi.fn(() => []),
}));

describe("clipboardService", () => {
	// Reset clipboard before each test
	beforeEach(() => {
		clipboardService.clearClipboard();
		vi.clearAllMocks();
	});

	describe("copyElement", () => {
		it("should copy an element to clipboard", () => {
			// Arrange
			const element = ElementFactory.createTextElement();

			// Act
			clipboardService.copyElement(element);

			// Assert
			const clipboard = clipboardService.getClipboard();
			expect(clipboard).not.toBeNull();
			expect(clipboard?.type).toBe("element");
			expect(clipboard?.element).toEqual(element);
		});

		it("should create a copy of the element, not a reference", () => {
			// Arrange
			const element = ElementFactory.createTextElement();

			// Act
			clipboardService.copyElement(element);

			// Assert
			const clipboard = clipboardService.getClipboard();
			expect(clipboard?.element).not.toBe(element); // Different reference
			expect(clipboard?.element).toEqual(element); // But equal content
		});
	});

	describe("copyBlock", () => {
		it("should copy a block and its children to clipboard", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
				ElementFactory.createShapeElement({ parentId: blockElement.id }),
			];

			// Act
			clipboardService.copyBlock(blockElement, childElements);

			// Assert
			const clipboard = clipboardService.getClipboard();
			expect(clipboard).not.toBeNull();
			expect(clipboard?.type).toBe("block");
			expect(clipboard?.block).toEqual(blockElement);
			expect(clipboard?.children).toEqual(childElements);
		});

		it("should create copies of the block and children, not references", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
			];

			// Act
			clipboardService.copyBlock(blockElement, childElements);

			// Assert
			const clipboard = clipboardService.getClipboard();
			expect(clipboard?.block).not.toBe(blockElement); // Different reference
			expect(clipboard?.children?.[0]).not.toBe(childElements[0]); // Different reference
			expect(clipboard?.block).toEqual(blockElement); // But equal content
			expect(clipboard?.children).toEqual(childElements); // But equal content
		});
	});

	describe("cutElement", () => {
		it("should copy element and mark it as cut", () => {
			// Arrange
			const element = ElementFactory.createTextElement();

			// Act
			clipboardService.cutElement(element);

			// Assert
			const clipboard = clipboardService.getClipboard();
			expect(clipboard?.element?._isCut).toBe(true);
		});
	});

	describe("cutBlock", () => {
		it("should copy block and mark it as cut", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
			];

			// Act
			clipboardService.cutBlock(blockElement, childElements);

			// Assert
			const clipboard = clipboardService.getClipboard();
			expect(clipboard?.block?._isCut).toBe(true);
		});
	});

	describe("getClipboard", () => {
		it("should return null when clipboard is empty", () => {
			// Act & Assert
			expect(clipboardService.getClipboard()).toBeNull();
		});

		it("should return clipboard data when available", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			// Act & Assert
			expect(clipboardService.getClipboard()).not.toBeNull();
		});
	});

	describe("hasClipboard", () => {
		it("should return false when clipboard is empty", () => {
			// Act & Assert
			expect(clipboardService.hasClipboard()).toBe(false);
		});

		it("should return true when clipboard has data", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			// Act & Assert
			expect(clipboardService.hasClipboard()).toBe(true);
		});
	});

	describe("clearClipboard", () => {
		it("should clear clipboard data", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			// Act
			clipboardService.clearClipboard();

			// Assert
			expect(clipboardService.hasClipboard()).toBe(false);
			expect(clipboardService.getClipboard()).toBeNull();
		});
	});

	describe("pasteElement", () => {
		it("should return null when clipboard is empty", () => {
			// Arrange
			const targetPageId = "page-1";
			const elements: Element[] = [];
			const pages: PageSettings[] = [
				PageFactory.createPage({ id: targetPageId }),
			];

			// Act
			const result = clipboardService.pasteElement(
				targetPageId,
				100,
				200,
				elements,
				pages,
			);

			// Assert
			expect(result).toBeNull();
		});

		it("should return null when target page does not exist", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			const targetPageId = "non-existent-page";
			const elements: Element[] = [];
			const pages: PageSettings[] = [PageFactory.createPage({ id: "page-1" })];

			// Act
			const result = clipboardService.pasteElement(
				targetPageId,
				100,
				200,
				elements,
				pages,
			);

			// Assert
			expect(result).toBeNull();
		});

		it("should paste a single element at specified position", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			const targetPageId = "page-1";
			const elements: Element[] = [];
			const pages: PageSettings[] = [
				PageFactory.createPage({ id: targetPageId }),
			];

			const clonedElement = { ...element, id: "cloned-id" };
			(elementService.cloneElement as jest.Mock).mockReturnValue(clonedElement);

			// Act
			const result = clipboardService.pasteElement(
				targetPageId,
				100,
				200,
				elements,
				pages,
			);

			// Assert
			expect(elementService.cloneElement).toHaveBeenCalledWith(
				expect.anything(),
				100,
				200,
				targetPageId,
				elements,
			);
			expect(elementService.validateElementBounds).toHaveBeenCalledWith(
				clonedElement,
				pages[0],
			);
			expect(result).not.toBeNull();
			expect(result?.newElements).toContain(clonedElement);
			expect(result?.pastedElementId).toBe("cloned-id");
		});

		it("should paste a block with children at specified position", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
			];
			clipboardService.copyBlock(blockElement, childElements);

			const targetPageId = "page-1";
			const elements: Element[] = [];
			const pages: PageSettings[] = [
				PageFactory.createPage({ id: targetPageId }),
			];

			const newBlock = { ...blockElement, id: "new-block-id" };
			const newChildren = [{ ...childElements[0], id: "new-child-id" }];
			(elementService.cloneBlockWithChildren as jest.Mock).mockReturnValue({
				block: newBlock,
				children: newChildren,
			});

			// Act
			const result = clipboardService.pasteElement(
				targetPageId,
				100,
				200,
				elements,
				pages,
			);

			// Assert
			expect(elementService.cloneBlockWithChildren).toHaveBeenCalledWith(
				expect.anything(),
				expect.anything(),
				100,
				200,
				targetPageId,
				elements,
			);
			expect(elementService.validateElementBounds).toHaveBeenCalledWith(
				newBlock,
				pages[0],
			);
			expect(result).not.toBeNull();
			expect(result?.newElements).toContain(newBlock);
			expect(result?.newElements).toEqual(expect.arrayContaining(newChildren));
			expect(result?.pastedElementId).toBe("new-block-id");
		});
	});

	describe("calculateSmartPastePosition", () => {
		it("should return original position when no overlapping elements", () => {
			// Arrange
			(elementService.getElementsAtPosition as jest.Mock).mockReturnValue([]);

			// Act
			const result = clipboardService.calculateSmartPastePosition(
				100,
				200,
				[],
				"page-1",
				50,
				30,
			);

			// Assert
			expect(result).toEqual({ x: 100, y: 200 });
		});

		it("should adjust position when overlapping elements exist", () => {
			// Arrange
			const overlappingElement = ElementFactory.createTextElement();

			// Mock implementation to simulate overlap detection
			(elementService.getElementsAtPosition as jest.Mock).mockImplementation(
				() => {
					return [overlappingElement];
				},
			);

			// We need to mock the isOverlapping function which is private
			// For this test, we'll just verify that the function attempts to find elements
			// at different positions until it gives up

			// Act
			clipboardService.calculateSmartPastePosition(
				100,
				200,
				[],
				"page-1",
				50,
				30,
			);

			// Assert
			// Just verify that getElementsAtPosition was called at least once
			expect(elementService.getElementsAtPosition).toHaveBeenCalled();
			// The actual result will be { x: 100, y: 200 } because our mock always returns overlapping elements
		});
	});

	describe("pasteElementSmart", () => {
		it("should use smart positioning when pasting", () => {
			// Arrange
			const element = ElementFactory.createTextElement({
				width: 50,
				height: 30,
			});
			clipboardService.copyElement(element);

			const targetPageId = "page-1";
			const elements: Element[] = [];
			const pages: PageSettings[] = [
				PageFactory.createPage({ id: targetPageId }),
			];

			// Since we can't easily mock internal functions, we'll test the integration
			// by verifying that the function returns a result when clipboard has data

			// Mock elementService functions needed by pasteElementSmart
			const clonedElement = { ...element, id: "cloned-id" };
			(elementService.cloneElement as jest.Mock).mockReturnValue(clonedElement);
			(elementService.validateElementBounds as jest.Mock).mockReturnValue(
				clonedElement,
			);

			// Act
			const result = clipboardService.pasteElementSmart(
				targetPageId,
				100,
				200,
				elements,
				pages,
			);

			// Assert
			// Just verify that we get a result with the expected structure
			expect(result).not.toBeNull();
			expect(result?.newElements).toContain(clonedElement);
			expect(result?.pastedElementId).toBe("cloned-id");
		});
	});

	describe("createTemplateFromClipboard", () => {
		it("should return null when clipboard is empty", () => {
			// Act & Assert
			expect(clipboardService.createTemplateFromClipboard()).toBeNull();
		});

		it("should return null when clipboard contains a single element", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			// Act & Assert
			expect(clipboardService.createTemplateFromClipboard()).toBeNull();
		});

		it("should create template data from block in clipboard", () => {
			// Arrange
			const blockId = "block-1";
			const blockElement = {
				...ElementFactory.createBlockElement(),
				id: blockId,
			};
			const childElements = [
				{
					...ElementFactory.createTextElement(),
					parentId: blockId,
					x: 10,
					y: 20,
				},
			];
			clipboardService.copyBlock(blockElement, childElements);

			// Act
			const result = clipboardService.createTemplateFromClipboard();

			// Assert
			expect(result).not.toBeNull();
			expect(result?.block?.currentPageId).toBe("");
			expect(result?.block?.x).toBe(0);
			expect(result?.block?.y).toBe(0);
			expect(result?.children?.[0].currentPageId).toBe("");
			expect(result?.children?.[0].parentId).toBe(blockId);
			expect(result?.children?.[0].x).toBe(10); // Preserve relative position
			expect(result?.children?.[0].y).toBe(20); // Preserve relative position
		});
	});

	describe("getClipboardSummary", () => {
		it("should return null when clipboard is empty", () => {
			// Act & Assert
			expect(clipboardService.getClipboardSummary()).toBeNull();
		});

		it("should return summary for copied element", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			// Act
			const summary = clipboardService.getClipboardSummary();

			// Assert
			expect(summary).toBe("Copied: text element");
		});

		it("should return summary for cut element", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.cutElement(element);

			// Act
			const summary = clipboardService.getClipboardSummary();

			// Assert
			expect(summary).toBe("Cut: text element");
		});

		it("should return summary for copied block with children", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
			];
			clipboardService.copyBlock(blockElement, childElements);

			// Act
			const summary = clipboardService.getClipboardSummary();

			// Assert
			expect(summary).toBe("Copied: Block with 1 child element");
		});

		it("should use plural form for multiple children", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
				ElementFactory.createShapeElement({ parentId: blockElement.id }),
			];
			clipboardService.copyBlock(blockElement, childElements);

			// Act
			const summary = clipboardService.getClipboardSummary();

			// Assert
			expect(summary).toBe("Copied: Block with 2 child elements");
		});
	});

	describe("isClipboardCut", () => {
		it("should return false when clipboard is empty", () => {
			// Act & Assert
			expect(clipboardService.isClipboardCut()).toBe(false);
		});

		it("should return false for copied element", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.copyElement(element);

			// Act & Assert
			expect(clipboardService.isClipboardCut()).toBe(false);
		});

		it("should return true for cut element", () => {
			// Arrange
			const element = ElementFactory.createTextElement();
			clipboardService.cutElement(element);

			// Act & Assert
			expect(clipboardService.isClipboardCut()).toBe(true);
		});

		it("should return true for cut block", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement({ parentId: blockElement.id }),
			];
			clipboardService.cutBlock(blockElement, childElements);

			// Act & Assert
			expect(clipboardService.isClipboardCut()).toBe(true);
		});
	});
});
