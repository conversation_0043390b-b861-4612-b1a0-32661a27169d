import type { Editor } from "@tiptap/react";
import type { ResizeHandleType } from "@/hooks/useElementTransform";
import type { TestDataRecord } from "@/utils/apiService";
import type { Element } from "../../types/element";
import { EditableElement } from "./EditableElement";

// Define SnapGuideInfo if not already globally available
interface SnapGuideInfo {
	type: "horizontal" | "vertical";
	position: number;
	start: number;
	end: number;
}

interface BlockElementProps {
	element: Element;
	childElements?: Element[];
	editingBlockId?: string | null;
	globalSelectedElementId?: string | null;
	onEnterBlockEditMode?: (blockId: string | null) => void;
	onUpdate: (updatedElement: Element, isFinal: boolean) => void;
	onDelete: (elementId: string) => void;
	onCopy: (elementId: string) => void;
	onCut: (elementId: string) => void;
	onSelect: (elementId: string) => void;
	setActiveEditor: (editor: Editor | null) => void;
	onEditingChange?: (isEditing: boolean) => void;
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
	showElementBorders?: boolean;
	updateSnapGuides?: (
		visible: boolean,
		guides: SnapGuideInfo[],
		relativeToBlockId?: string | null,
		blockInfo?: { x: number; y: number; width: number; height: number } | null,
	) => void;
	calculateSnapGuides?: (
		elementId: string,
		newX: number,
		newY: number,
		elementWidth: number,
		elementHeight: number,
		parentBlockId?: string | null,
		movingHandle?: ResizeHandleType,
	) => { snapX: number | null; snapY: number | null; guides: SnapGuideInfo[] };
	// Add parent keyboard handler to allow arrow key movement
	onParentKeyDown?: (e: React.KeyboardEvent<HTMLDivElement>) => void;
}

export function BlockElement({
	element,
	childElements,
	editingBlockId,
	globalSelectedElementId,
	onEnterBlockEditMode,
	onUpdate,
	onDelete,
	onCopy,
	onCut,
	onSelect,
	setActiveEditor,
	onEditingChange,
	testData,
	selectedTestDataIndex,
	highlightVariables,
	showElementBorders,
	updateSnapGuides,
	calculateSnapGuides,
	onParentKeyDown,
}: BlockElementProps) {
	return (
		<button
			type="button"
			className={`box-border w-full h-full overflow-hidden relative border-none bg-transparent p-0 cursor-pointer`}
			tabIndex={0}
			// Add a double-click handler to enter block edit mode for this block
			onDoubleClick={() => onEnterBlockEditMode?.(element.id)}
			onKeyDown={(e) => {
				if (e.key === "Enter") {
					e.preventDefault();
					onEnterBlockEditMode?.(element.id);
				} else {
					// Pass through other keyboard events (like arrow keys) to the parent EditableElement
					// Cast the event to match the expected type
					onParentKeyDown?.(
						e as unknown as React.KeyboardEvent<HTMLDivElement>,
					);
				}
			}}
		>
			{childElements?.map((child) => (
				<EditableElement
					key={child.id}
					element={child}
					// Pass down the main handlers from Page.tsx, received by this (block) element
					onUpdate={onUpdate}
					onDelete={onDelete}
					onCopy={onCopy}
					onCut={onCut}
					onSelect={onSelect}
					isSelected={globalSelectedElementId === child.id}
					setActiveEditor={setActiveEditor}
					onEditingChange={onEditingChange} // This might need care if child editing affects parent block editing state
					testData={testData}
					selectedTestDataIndex={selectedTestDataIndex}
					highlightVariables={highlightVariables}
					showElementBorders={showElementBorders}
					updateSnapGuides={updateSnapGuides}
					// Child is locked if its own isLocked is true, OR if the globally edited block is not its direct parent.
					isLocked={child.isLocked || editingBlockId !== child.parentId}
					// Pass block-related props down, but childElements is not for them
					editingBlockId={editingBlockId}
					onEnterBlockEditMode={onEnterBlockEditMode} // Children shouldn't trigger this for the parent block
					globalSelectedElementId={globalSelectedElementId}
					calculateSnapGuides={calculateSnapGuides} // Add the new prop
				/>
			))}
			{/* You can add a label or an indicator if the block is being edited */}
			{editingBlockId === element.id && (
				<div className="absolute top-0 left-0 bg-blue-500 text-white text-xs p-1 opacity-75">
					Baustein bearbeiten
				</div>
			)}
		</button>
	);
}
