import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Save } from "lucide-react";
import { describe, expect, it, vi } from "vitest";

import {
	getDisabledClassName,
	getDisabledLabelClassName,
	ToolbarButton,
	ToolbarContainer,
	ToolbarEmpty,
	ToolbarError,
	ToolbarFieldGroup,
	ToolbarHeader,
	ToolbarLoading,
	ToolbarSection,
} from "../ToolbarBase";

describe("ToolbarBase components", () => {
	it("renders ToolbarContainer with children", () => {
		render(
			<ToolbarContainer>
				<div data-testid="test-child">Test Content</div>
			</ToolbarContainer>,
		);

		expect(screen.getByTestId("test-child")).toBeInTheDocument();
		expect(screen.getByText("Test Content")).toBeInTheDocument();
	});

	it("renders ToolbarSection with children", () => {
		render(
			<ToolbarSection>
				<div data-testid="test-child">Section Content</div>
			</ToolbarSection>,
		);

		expect(screen.getByTestId("test-child")).toBeInTheDocument();
		expect(screen.getByText("Section Content")).toBeInTheDocument();
	});

	it("renders ToolbarHeader with title", () => {
		render(<ToolbarHeader title="Test Header" />);

		expect(screen.getByText("Test Header")).toBeInTheDocument();
		expect(screen.getByText("Test Header").tagName).toBe("H3");
	});

	it("renders ToolbarButton with icon and label", async () => {
		const handleClick = vi.fn();
		const user = userEvent.setup();

		render(
			<ToolbarButton icon={Save} label="Save Document" onClick={handleClick} />,
		);

		const button = screen.getByRole("button", { name: /save document/i });
		expect(button).toBeInTheDocument();

		await user.click(button);
		expect(handleClick).toHaveBeenCalledTimes(1);
	});

	it("renders disabled ToolbarButton", () => {
		render(
			<ToolbarButton
				icon={Save}
				label="Save Document"
				onClick={vi.fn()}
				disabled={true}
			/>,
		);

		const button = screen.getByRole("button", { name: /save document/i });
		expect(button).toBeDisabled();
	});

	it("renders ToolbarButton with different variants", () => {
		const { rerender } = render(
			<ToolbarButton
				icon={Save}
				label="Default Button"
				onClick={vi.fn()}
				variant="default"
			/>,
		);

		expect(screen.getByRole("button")).toHaveClass("bg-primary");

		rerender(
			<ToolbarButton
				icon={Save}
				label="Outline Button"
				onClick={vi.fn()}
				variant="outline"
			/>,
		);

		expect(screen.getByRole("button")).toHaveClass("border-input");
	});

	it("renders ToolbarLoading with custom message", () => {
		render(<ToolbarLoading message="Custom loading message" />);

		expect(screen.getByText("Custom loading message")).toBeInTheDocument();
		expect(document.querySelector(".animate-spin")).toBeInTheDocument();
	});

	it("renders ToolbarError with message", () => {
		render(<ToolbarError message="Error occurred" />);

		expect(screen.getByText("Error occurred")).toBeInTheDocument();
		// Check that the error container has the error message
		const errorContainer = screen.getByText("Error occurred");
		expect(errorContainer).toBeInTheDocument();
	});

	it("renders ToolbarEmpty with message", () => {
		render(<ToolbarEmpty message="No items found" />);

		expect(screen.getByText("No items found")).toBeInTheDocument();
		expect(screen.getByText("No items found")).toHaveClass("text-gray-500");
	});

	it("renders ToolbarFieldGroup with label and children", () => {
		render(
			<ToolbarFieldGroup label="Field Label">
				<input data-testid="test-input" />
			</ToolbarFieldGroup>,
		);

		expect(screen.getByText("Field Label")).toBeInTheDocument();
		expect(screen.getByTestId("test-input")).toBeInTheDocument();
	});

	it("renders disabled ToolbarFieldGroup", () => {
		render(
			<ToolbarFieldGroup label="Disabled Field" disabled={true}>
				<input data-testid="test-input" />
			</ToolbarFieldGroup>,
		);

		expect(screen.getByText("Disabled Field")).toHaveClass("text-gray-400");
	});

	it("returns correct disabled class names", () => {
		expect(getDisabledClassName(true)).toBe("opacity-50 cursor-not-allowed");
		expect(getDisabledClassName(false)).toBe("");

		expect(getDisabledLabelClassName(true)).toBe("text-gray-400");
		expect(getDisabledLabelClassName(false)).toBe("");
	});
});
