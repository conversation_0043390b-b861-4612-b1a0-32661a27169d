import { expect, test } from "@playwright/test";

const host = process.env.EDITOR_TEST_HOST;
if (!host) {
	throw new Error("EDITOR_TEST_HOST is not set");
}

test.describe("Position Size Settings", () => {
	test.beforeEach(async ({ page }) => {
		await page.goto(`${host}/editor?id=105&version=latest`);
		// Wait for any element to render inside the editor canvas
		await page.waitForSelector("[data-element-id]", { timeout: 10000 });
	});

	test("should allow changing selected element position to 0,0", async ({
		page,
	}) => {
		// Select the first element on the page
		const firstElement = page.locator("[data-element-id]").first();
		await firstElement.click();

		// Ensure the sidebar position inputs are visible
		const xInput = page.locator("#position-x");
		await expect(xInput).toBeVisible();

		// Change X position to 0
		await xInput.fill("0");

		const yInput = page.locator("#position-y");
		await expect(yInput).toBeVisible();

		// Change Y position to 0
		await yInput.fill("0");

		// Give the debounce some time to process
		await page.waitForTimeout(200);

		// Verify that the input values stick and remain at 0
		await expect(xInput).toHaveValue("0");
		await expect(yInput).toHaveValue("0");
	});
});
