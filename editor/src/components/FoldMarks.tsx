import type React from "react";
import type { PageFormat } from "@/utils/apiService";

interface FoldMarksProps {
	/** Page width in cm */
	pageWidth: number;
	/** Page height in cm */
	pageHeight: number;
	/** Trim margins in cm */
	trimLeft: number;
	trimTop: number;
	/** Outer page dimensions in cm */
	outerWidth: number;
	outerHeight: number;
	/** Page format containing fold marks data */
	pageFormat: PageFormat | undefined;
	/** Whether to show full lines across the page */
	showFullLines?: boolean;
}

interface FoldMark {
	direction: "horizontal" | "vertical";
	position: number; // in mm
}

export const FoldMarks: React.FC<FoldMarksProps> = ({
	pageWidth,
	pageHeight,
	trimLeft,
	trimTop,
	outerWidth,
	outerHeight,
	pageFormat,
	showFullLines = false,
}) => {
	// Parse fold marks from page format
	const parseFoldMarks = (): FoldMark[] => {
		if (!pageFormat || !pageFormat.fold_marks) {
			return [];
		}

		try {
			// The fold_marks field is a JSON string that needs to be parsed
			const foldMarks = JSON.parse(pageFormat.fold_marks);
			if (Array.isArray(foldMarks)) {
				return foldMarks.filter(
					(mark) =>
						mark &&
						typeof mark.direction === "string" &&
						["horizontal", "vertical"].includes(mark.direction) &&
						typeof mark.position === "number",
				);
			}
		} catch (error) {
			console.error("Failed to parse fold marks:", error);
		}

		return [];
	};

	const foldMarks = parseFoldMarks();

	if (foldMarks.length === 0) {
		return null;
	}

	// Convert cm to mm for calculations
	const pageWidthMm = pageWidth * 10;
	const pageHeightMm = pageHeight * 10;
	const trimLeftMm = trimLeft * 10;
	const trimTopMm = trimTop * 10;
	const outerWidthMm = outerWidth * 10;
	const outerHeightMm = outerHeight * 10;

	// Calculate content area bounds
	const contentLeftMm = trimLeftMm;
	const contentRightMm = trimLeftMm + pageWidthMm;
	const contentTopMm = trimTopMm;
	const contentBottomMm = trimTopMm + pageHeightMm;

	// Mark properties for hints
	const hintLength = 8; // mm - length of hint marks
	const hintOffset = 2; // mm - distance from content edge
	const markThickness = 0.5; // px

	const baseMarkStyle = {
		position: "absolute" as const,
		borderColor: "#dc2626", // Red color for fold marks
		borderStyle: "solid",
		borderWidth: 0,
	};

	return (
		<div
			className="fold-marks absolute inset-0 pointer-events-none"
			style={{ zIndex: 9998 }}
		>
			{foldMarks.map((foldMark) => {
				const isHorizontal = foldMark.direction === "horizontal";
				const positionMm = foldMark.position;
				// Add trim offset so positions are relative to the outer (trimmed) container
				const adjustedPositionMm =
					positionMm + (isHorizontal ? trimTopMm : trimLeftMm);

				// Create unique key based on direction and position
				const uniqueKey = `${foldMark.direction}-${foldMark.position}`;

				if (showFullLines) {
					// Full line mode - spans entire outer container
					if (isHorizontal) {
						return (
							<div
								key={`fold-mark-full-${uniqueKey}`}
								className={`fold-mark-full horizontal`}
								style={{
									...baseMarkStyle,
									left: "0",
									top: `${adjustedPositionMm}mm`,
									width: `${outerWidthMm}mm`,
									height: 0,
									borderTopWidth: `${markThickness}px`,
									transform: "translateY(-50%)",
								}}
							/>
						);
					} else {
						return (
							<div
								key={`fold-mark-full-${uniqueKey}`}
								className={`fold-mark-full vertical`}
								style={{
									...baseMarkStyle,
									left: `${adjustedPositionMm}mm`,
									top: "0",
									width: 0,
									height: `${outerHeightMm}mm`,
									borderLeftWidth: `${markThickness}px`,
									transform: "translateX(-50%)",
								}}
							/>
						);
					}
				} else {
					// Hint mode - small marks outside content area
					const marks: React.ReactNode[] = [];

					if (isHorizontal) {
						// Horizontal fold mark - show hints on left and right sides
						// Only show if the fold mark intersects with the content area vertically
						if (
							adjustedPositionMm >= contentTopMm &&
							adjustedPositionMm <= contentBottomMm
						) {
							// Left hint
							marks.push(
								<div
									key={`fold-hint-left-${uniqueKey}`}
									className="fold-hint horizontal"
									style={{
										...baseMarkStyle,
										left: `${contentLeftMm - hintOffset - hintLength}mm`,
										top: `${adjustedPositionMm}mm`,
										width: `${hintLength}mm`,
										height: 0,
										borderTopWidth: `${markThickness}px`,
										transform: "translateY(-50%)",
									}}
								/>,
							);
							// Right hint
							marks.push(
								<div
									key={`fold-hint-right-${uniqueKey}`}
									className="fold-hint horizontal"
									style={{
										...baseMarkStyle,
										left: `${contentRightMm + hintOffset}mm`,
										top: `${adjustedPositionMm}mm`,
										width: `${hintLength}mm`,
										height: 0,
										borderTopWidth: `${markThickness}px`,
										transform: "translateY(-50%)",
									}}
								/>,
							);
						}
					} else {
						// Vertical fold mark - show hints on top and bottom sides
						// Only show if the fold mark intersects with the content area horizontally
						if (
							adjustedPositionMm >= contentLeftMm &&
							adjustedPositionMm <= contentRightMm
						) {
							// Top hint
							marks.push(
								<div
									key={`fold-hint-top-${uniqueKey}`}
									className="fold-hint vertical"
									style={{
										...baseMarkStyle,
										left: `${adjustedPositionMm}mm`,
										top: `${contentTopMm - hintOffset - hintLength}mm`,
										width: 0,
										height: `${hintLength}mm`,
										borderLeftWidth: `${markThickness}px`,
										transform: "translateX(-50%)",
									}}
								/>,
							);
							// Bottom hint
							marks.push(
								<div
									key={`fold-hint-bottom-${uniqueKey}`}
									className="fold-hint vertical"
									style={{
										...baseMarkStyle,
										left: `${adjustedPositionMm}mm`,
										top: `${contentBottomMm + hintOffset}mm`,
										width: 0,
										height: `${hintLength}mm`,
										borderLeftWidth: `${markThickness}px`,
										transform: "translateX(-50%)",
									}}
								/>,
							);
						}
					}

					return marks;
				}
			})}
		</div>
	);
};
