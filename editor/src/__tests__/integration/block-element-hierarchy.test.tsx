import { act, renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useElementOperations } from "../../hooks/useElementOperations";
import type { Element } from "../../types/element";
import type { PageSettings } from "../../types/page";
import { ElementFactory, PageFactory } from "../utils/mock-factories";

// Mock uuid to have predictable IDs in tests
vi.mock("uuid", () => ({
	v4: vi.fn(() => `test-uuid-${Math.random().toString(36).substring(2, 9)}`),
}));

function isBlockClipboard(
	obj: unknown,
): obj is { block: Element; children: Element[] } {
	return !!(
		obj &&
		typeof obj === "object" &&
		"block" in obj &&
		"children" in obj
	);
}

describe("Block Element and Hierarchy Management", () => {
	let mockPages: PageSettings[];
	let mockElements: Element[];
	let mockSetEditorState: ReturnType<typeof vi.fn>;
	let mockSetSelectedElementId: ReturnType<typeof vi.fn>;
	let mockSetEditingBlockId: ReturnType<typeof vi.fn>;
	let mockSetDeletedPageNumberIds: ReturnType<typeof vi.fn>;
	let mockSetClipboard: ReturnType<typeof vi.fn>;
	let mockCommitBatch: ReturnType<typeof vi.fn>;
	let mockBeginBatch: ReturnType<typeof vi.fn>;
	let mockUpdateDraft: ReturnType<typeof vi.fn>;
	let mockIsBatching: ReturnType<typeof vi.fn>;

	beforeEach(() => {
		// Create initial test state
		mockPages = [
			PageFactory.createPage({ id: "page-1" }),
			PageFactory.createPage({ id: "page-2" }),
		];
		mockElements = [
			ElementFactory.createTextElement({
				id: "text-1",
				currentPageId: "page-1",
				x: 50,
				y: 50,
			}),
			ElementFactory.createShapeElement({
				id: "shape-1",
				currentPageId: "page-1",
				x: 150,
				y: 50,
			}),
			ElementFactory.createTextElement({
				id: "text-2",
				currentPageId: "page-2",
				x: 50,
				y: 100,
			}),
		];

		// Create mock functions
		mockSetEditorState = vi.fn();
		mockSetSelectedElementId = vi.fn();
		mockSetEditingBlockId = vi.fn();
		mockSetDeletedPageNumberIds = vi.fn();
		mockSetClipboard = vi.fn();
		mockCommitBatch = vi.fn();
		mockBeginBatch = vi.fn();
		mockUpdateDraft = vi.fn();
		mockIsBatching = vi.fn().mockReturnValue(false);
	});

	it("should handle block creation with child elements", () => {
		// Create a block element
		const blockElement = ElementFactory.createBlockElement({
			id: "block-1",
			currentPageId: "page-1",
			childElementIds: [], // Start with empty children
		});

		// Create child elements
		const childElement1 = ElementFactory.createTextElement({
			id: "child-1",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 10, // Relative to block
			y: 10,
		});

		const { result } = renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: mockElements,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: null,
				selectedElementId: null,
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: null,
				setClipboard: mockSetClipboard,
				isEditing: false,
			}),
		);

		// Step 1: Add the block
		act(() => {
			result.current.handleAddElement(blockElement);
		});

		// Verify block was added
		expect(mockSetEditorState).toHaveBeenCalled();
		const firstCallArgs = mockSetEditorState.mock.calls[0][0];
		expect(firstCallArgs.elements).toContainEqual(blockElement);

		// Update elements array to include the block
		const elementsWithBlock = [...mockElements, blockElement];
		mockSetEditorState.mockClear();

		// Step 2: Add child elements to the block
		const { result: resultWithBlock } = renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: elementsWithBlock,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: "block-1", // Now editing the block
				selectedElementId: null,
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: null,
				setClipboard: mockSetClipboard,
				isEditing: false,
			}),
		);

		// Add first child
		act(() => {
			resultWithBlock.current.handleAddElement(childElement1);
		});

		// Verify child was added and block was updated
		expect(mockSetEditorState).toHaveBeenCalled();
		const secondCallArgs = mockSetEditorState.mock.calls[0][0];

		// Find the updated block
		const updatedBlock = secondCallArgs.elements.find(
			(el: Element) => el.id === "block-1",
		);
		expect(updatedBlock).toBeDefined();
		expect(updatedBlock.childElementIds).toContain("child-1");

		// Find the child element
		const addedChild = secondCallArgs.elements.find(
			(el: Element) => el.id === "child-1",
		);
		expect(addedChild).toBeDefined();
		expect(addedChild.parentId).toBe("block-1");
	});

	it("should handle block editing mode and child element manipulation", () => {
		// Create a block with children
		const blockElement = ElementFactory.createBlockElement({
			id: "block-1",
			currentPageId: "page-1",
			childElementIds: ["child-1", "child-2"],
			x: 100,
			y: 100,
			width: 200,
			height: 150,
		});

		const childElement1 = ElementFactory.createTextElement({
			id: "child-1",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 10, // Relative to block
			y: 10,
		});

		const childElement2 = ElementFactory.createShapeElement({
			id: "child-2",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 50, // Relative to block
			y: 10,
		});

		const elementsWithBlock = [
			...mockElements,
			blockElement,
			childElement1,
			childElement2,
		];

		// Step 1: Enter block editing mode
		renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: elementsWithBlock,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: null,
				selectedElementId: "block-1",
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: null,
				setClipboard: mockSetClipboard,
				isEditing: false,
			}),
		);

		// Simulate entering block edit mode
		act(() => {
			mockSetEditingBlockId("block-1");
		});

		// Step 2: Modify a child element
		const { result: resultInEditMode } = renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: elementsWithBlock,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: "block-1", // Now in block edit mode
				selectedElementId: "child-1",
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: null,
				setClipboard: mockSetClipboard,
				isEditing: false,
			}),
		);

		// Update child element position
		const updatedChild = {
			...childElement1,
			x: 30, // New position relative to block
			y: 20,
		};

		act(() => {
			resultInEditMode.current.handleUpdateElement(updatedChild, true);
		});

		// Verify child was updated
		expect(mockCommitBatch).toHaveBeenCalled();
		const commitBatchArgs = mockCommitBatch.mock.calls[0][0];
		const updatedChildElement = commitBatchArgs.elements.find(
			(el: Element) => el.id === "child-1",
		);
		expect(updatedChildElement).toBeDefined();
		expect(updatedChildElement.x).toBe(30);
		expect(updatedChildElement.y).toBe(20);

		// Step 3: Delete a child element
		mockCommitBatch.mockClear();
		mockSetEditorState.mockClear();

		act(() => {
			resultInEditMode.current.handleDeleteElement("child-2");
		});

		// Verify child was deleted and block was updated
		expect(mockSetEditorState).toHaveBeenCalled();
		const deleteCallArgs = mockSetEditorState.mock.calls[0][0];

		// Find the updated block
		const updatedBlock = deleteCallArgs.elements.find(
			(el: Element) => el.id === "block-1",
		);
		expect(updatedBlock).toBeDefined();
		expect(updatedBlock.childElementIds).toContain("child-1");
		expect(updatedBlock.childElementIds).not.toContain("child-2");

		// Verify child-2 was removed
		const deletedChild = deleteCallArgs.elements.find(
			(el: Element) => el.id === "child-2",
		);
		expect(deletedChild).toBeUndefined();
	});

	it("should handle block deletion with child elements", () => {
		// Create a block with children
		const blockElement = ElementFactory.createBlockElement({
			id: "block-1",
			currentPageId: "page-1",
			childElementIds: ["child-1", "child-2"],
			x: 100,
			y: 100,
			width: 200,
			height: 150,
		});

		const childElement1 = ElementFactory.createTextElement({
			id: "child-1",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 10, // Relative to block
			y: 10,
		});

		const childElement2 = ElementFactory.createShapeElement({
			id: "child-2",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 50, // Relative to block
			y: 10,
		});

		const elementsWithBlock = [
			...mockElements,
			blockElement,
			childElement1,
			childElement2,
		];

		const { result } = renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: elementsWithBlock,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: null,
				selectedElementId: null,
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: null,
				setClipboard: mockSetClipboard,
				isEditing: false,
			}),
		);

		// Delete the block
		act(() => {
			result.current.handleDeleteElement("block-1");
		});

		// Verify block and all children were deleted
		expect(mockSetEditorState).toHaveBeenCalled();
		const deleteCallArgs = mockSetEditorState.mock.calls[0][0];

		// Block should be removed
		const deletedBlock = deleteCallArgs.elements.find(
			(el: Element) => el.id === "block-1",
		);
		expect(deletedBlock).toBeUndefined();

		// Children should be removed
		const deletedChild1 = deleteCallArgs.elements.find(
			(el: Element) => el.id === "child-1",
		);
		expect(deletedChild1).toBeUndefined();

		const deletedChild2 = deleteCallArgs.elements.find(
			(el: Element) => el.id === "child-2",
		);
		expect(deletedChild2).toBeUndefined();

		// Original elements should remain
		expect(deleteCallArgs.elements).toHaveLength(3);
	});

	it("should handle block copy and paste with child elements", () => {
		// Create a block with children
		const blockElement = ElementFactory.createBlockElement({
			id: "block-1",
			currentPageId: "page-1",
			childElementIds: ["child-1", "child-2"],
			x: 100,
			y: 100,
			width: 200,
			height: 150,
		});

		const childElement1 = ElementFactory.createTextElement({
			id: "child-1",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 10, // Relative to block
			y: 10,
			content: "Child 1 content",
		});

		const childElement2 = ElementFactory.createShapeElement({
			id: "child-2",
			currentPageId: "page-1",
			parentId: "block-1",
			x: 50, // Relative to block
			y: 10,
		});

		const elementsWithBlock = [
			...mockElements,
			blockElement,
			childElement1,
			childElement2,
		];
		let clipboard:
			| Element
			| { block: Element; children: Element[] }
			| { childElement: Element; originalParentId: string }
			| null = null;
		const mockSetClipboardLocal = vi.fn((value) => {
			clipboard = value;
		});

		const { result } = renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: elementsWithBlock,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: null,
				selectedElementId: null,
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: clipboard,
				setClipboard: mockSetClipboardLocal,
				isEditing: false,
			}),
		);

		// Copy the block
		act(() => {
			result.current.handleCopyElement("block-1");
		});

		// Verify block and children were copied to clipboard
		expect(mockSetClipboardLocal).toHaveBeenCalled();
		expect(clipboard).toBeDefined();
		if (clipboard && isBlockClipboard(clipboard)) {
			expect(
				(clipboard as { block: Element; children: Element[] }).block,
			).toBeDefined();
			expect(
				(clipboard as { block: Element; children: Element[] }).children,
			).toBeDefined();
		}
		expect(clipboard).toHaveProperty("block");
		if (clipboard && "block" in clipboard) {
			expect((clipboard as { block: Element }).block.id).toBe("block-1");
		}
		expect(clipboard).toHaveProperty("children");
		if (clipboard && "children" in clipboard) {
			expect((clipboard as { children: Element[] }).children).toHaveLength(2);
		}

		// Simulate clipboard update
		clipboard = {
			block: blockElement,
			children: [childElement1, childElement2],
		};

		// Paste the block
		const { result: resultWithClipboard } = renderHook(() =>
			useElementOperations({
				pages: mockPages,
				elements: elementsWithBlock,
				setEditorState: mockSetEditorState,
				commitBatch: mockCommitBatch,
				beginBatch: mockBeginBatch,
				updateDraft: mockUpdateDraft,
				isBatching: mockIsBatching,
				currentPage: "page-1",
				editingBlockId: null,
				selectedElementId: null,
				setSelectedElementId: mockSetSelectedElementId,
				setEditingBlockId: mockSetEditingBlockId,
				setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
				isEditorLocked: false,
				clipboard: clipboard,
				setClipboard: mockSetClipboardLocal,
				isEditing: false,
			}),
		);

		mockSetEditorState.mockClear();

		act(() => {
			resultWithClipboard.current.handlePasteAction("page-1", 200, 200);
		});

		// Verify block and children were pasted with new IDs
		expect(mockSetEditorState).toHaveBeenCalled();
		const pasteCallArgs = mockSetEditorState.mock.calls[0][0];

		// Should have original elements plus new block and children
		expect(pasteCallArgs.elements.length).toBeGreaterThan(
			elementsWithBlock.length,
		);

		// Find the new block (should have different ID but same type)
		const newBlocks = pasteCallArgs.elements.filter(
			(el: Element) => el.type === "block" && el.id !== "block-1",
		);
		expect(newBlocks).toHaveLength(1);

		const newBlock = newBlocks[0];
		expect(newBlock.y).toBe(200);
		expect(newBlock.childElementIds).toHaveLength(2);

		// Find the new children
		const newChildren = pasteCallArgs.elements.filter(
			(el: Element) => el.parentId === newBlock.id,
		);
		expect(newChildren).toHaveLength(2);

		// Verify content was preserved
		const newTextChild = newChildren.find((el: Element) => el.type === "text");
		expect(newTextChild).toBeDefined();
		expect(newTextChild.content).toBe("Child 1 content");
	});
});
