import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import type { Element } from "../../types/element";
import type { PageSettings } from "../../types/page";
import type { PageFormat } from "../apiService";
import {
	exportDocumentAsZip,
	exportToZip,
	importFromZip,
} from "../exportUtils";

// Mock dependencies
vi.mock("file-saver", () => ({
	saveAs: vi.fn(),
}));

vi.mock("jszip", () => {
	const mockZip = {
		file: vi.fn(),
		generateAsync: vi.fn(),
		loadAsync: vi.fn(),
		files: {},
		version: "3.10.1",
	};

	const JSZip = vi.fn(() => mockZip);
	JSZip.prototype = mockZip;

	return {
		default: JSZip,
	};
});

vi.mock("../apiService", () => ({
	fetchImageAsBlob: vi.fn(),
	storeImageFileWithFolders: vi.fn(),
}));

vi.mock("../htmlExporter", () => ({
	generateCSS: vi.fn(),
	generateFullDocumentHTML: vi.fn(),
	generateSinglePageHTML: vi.fn(),
}));

vi.mock("../contentProcessing", () => ({
	processContentWithVariables: vi.fn((options) => {
		// Simple mock implementation that returns processed content
		if (options.selectedTestDataIndex !== -1 && options.testData?.length) {
			const variable = options.content.match(/{{([^}\s]+)}}/)?.[1];
			if (
				variable &&
				options.testData[options.selectedTestDataIndex]?.[variable]
			) {
				return options.testData[options.selectedTestDataIndex][variable];
			}
		}
		return options.content;
	}),
}));

// Mock React and ReactDOMServer for HTML generation tests
vi.mock("react", () => ({
	createElement: vi.fn((component, props) => ({ component, props })),
}));

vi.mock("react-dom/server", () => ({
	renderToStaticMarkup: vi.fn((element) => {
		// Simple mock that returns HTML based on component type
		if (element.component.name === "ExportPage") {
			return `<div class="export-page" data-page-id="${element.props.page.id}"></div>`;
		}
		if (element.component.name === "ExportDocument") {
			return `<div class="export-document">${element.props.pages
				.map(
					(p: PageSettings, i: number) =>
						`<div class="export-page" data-page-id="${p.id}" data-page-number="${i + 1}"></div>`,
				)
				.join("")}</div>`;
		}
		return "<div>Mock Component</div>";
	}),
}));

// Import mocked modules
import { saveAs } from "file-saver";
import JSZip from "jszip";
import { fetchImageAsBlob, storeImageFileWithFolders } from "../apiService";
import { processContentWithVariables } from "../contentProcessing";
import {
	generateCSS,
	generateFullDocumentHTML,
	generateSinglePageHTML,
} from "../htmlExporter";

describe("exportUtils", () => {
	let mockPages: PageSettings[];
	let mockElements: Element[];
	let mockZipInstance: Partial<JSZip> & { [key: string]: unknown };

	beforeEach(() => {
		// Reset all mocks
		vi.clearAllMocks();

		// Setup mock data
		mockPages = [
			{
				id: "page1",
				width: 210,
				height: 297,
				format: "A4",
				orientation: "portrait",
			},
			{
				id: "page2",
				width: 210,
				height: 297,
				format: "A4",
				orientation: "portrait",
			},
		] as PageSettings[];

		mockElements = [
			{
				id: "element1",
				type: "text",
				x: 10,
				y: 10,
				width: 100,
				height: 20,
				currentPageId: "page1",
				content: "Hello World",
			},
			{
				id: "element2",
				type: "image",
				x: 20,
				y: 30,
				width: 80,
				height: 60,
				currentPageId: "page1",
				content: "https://example.com/image.jpg",
			},
			{
				id: "element3",
				type: "text",
				x: 15,
				y: 15,
				width: 120,
				height: 25,
				currentPageId: "page2",
				content: "Page 2 Content",
			},
		] as Element[];

		// Setup JSZip mock
		mockZipInstance = {
			file: vi.fn(),
			generateAsync: vi.fn().mockResolvedValue(new Blob(["mock zip content"])),
			loadAsync: vi.fn(),
			files: {},
		};

		(JSZip as any).mockImplementation(() => mockZipInstance);

		// Setup HTML exporter mocks
		(generateCSS as any).mockResolvedValue("/* mock css */");
		(generateFullDocumentHTML as any).mockReturnValue(
			"<html>Full Document</html>",
		);
		(generateSinglePageHTML as any).mockReturnValue("<html>Single Page</html>");
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	describe("exportToZip", () => {
		it("should export document to ZIP with all required files", async () => {
			await exportToZip(mockPages, mockElements, "test-document");

			// Verify ZIP instance was created
			expect(JSZip).toHaveBeenCalled();

			// Verify CSS file was added
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"styles.css",
				"/* mock css */",
			);

			// Verify main HTML file was added
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"index.html",
				"<html>Full Document</html>",
			);

			// Verify individual page files were added
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-1.html",
				"<html>Single Page</html>",
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-2.html",
				"<html>Single Page</html>",
			);

			// Verify page JSON data was added
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-1.json",
				expect.stringContaining('"page"'),
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-2.json",
				expect.stringContaining('"page"'),
			);

			// Verify ZIP was generated and saved
			expect(mockZipInstance.generateAsync).toHaveBeenCalledWith({
				type: "blob",
			});
			expect(saveAs).toHaveBeenCalledWith(
				expect.any(Blob),
				"test-document.zip",
			);
		});

		it("should handle image processing and include images in ZIP", async () => {
			const mockImageBlob = new Blob(["mock image data"], {
				type: "image/jpeg",
			});
			(fetchImageAsBlob as any).mockResolvedValue(mockImageBlob);

			await exportToZip(mockPages, mockElements, "test-document");

			// Verify image was fetched
			expect(fetchImageAsBlob).toHaveBeenCalledWith(
				"https://example.com/image.jpg",
			);

			// Verify image was added to ZIP
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"images/image-element2.jpeg",
				mockImageBlob,
			);
		});

		it("should skip data URLs for images", async () => {
			const elementsWithDataUrl = [
				...mockElements,
				{
					id: "element4",
					type: "image",
					x: 10,
					y: 10,
					width: 50,
					height: 50,
					currentPageId: "page1",
					content:
						"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
				} as Element,
			];

			await exportToZip(mockPages, elementsWithDataUrl, "test-document");

			// Verify fetchImageAsBlob was not called for data URL
			expect(fetchImageAsBlob).toHaveBeenCalledTimes(1); // Only for the regular image
			expect(fetchImageAsBlob).not.toHaveBeenCalledWith(
				expect.stringContaining("data:"),
			);
		});

		it("should handle image fetch errors gracefully", async () => {
			(fetchImageAsBlob as any).mockRejectedValue(new Error("Network error"));

			// Should not throw
			await expect(
				exportToZip(mockPages, mockElements, "test-document"),
			).resolves.not.toThrow();

			// Should still generate ZIP
			expect(mockZipInstance.generateAsync).toHaveBeenCalled();
			expect(saveAs).toHaveBeenCalled();
		});

		it("should use default document name when not provided", async () => {
			await exportToZip(mockPages, mockElements);

			expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), "document.zip");
		});

		it("should handle empty pages and elements arrays", async () => {
			await exportToZip([], [], "empty-document");

			// Should still create basic structure
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"styles.css",
				"/* mock css */",
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"index.html",
				"<html>Full Document</html>",
			);
			expect(mockZipInstance.generateAsync).toHaveBeenCalled();
		});

		it("should handle ZIP generation errors", async () => {
			(mockZipInstance.generateAsync as any).mockRejectedValue(
				new Error("ZIP generation failed"),
			);

			await expect(
				exportToZip(mockPages, mockElements, "test-document"),
			).rejects.toThrow("ZIP generation failed");
		});

		it("should pass API formats to HTML generators", async () => {
			const mockApiFormats = [
				{
					name: "A4",
					description: "A4 Format",
					width: 210,
					height: 297,
					trim_top: 0,
					trim_right: 0,
					trim_bottom: 0,
					trim_left: 0,
					address_x: 0,
					address_y: 0,
				},
			];

			await exportToZip(
				mockPages,
				mockElements,
				"test-document",
				mockApiFormats,
			);

			expect(generateFullDocumentHTML).toHaveBeenCalledWith(
				mockPages,
				expect.any(Array),
				"test-document",
				true,
				null,
				mockApiFormats,
			);

			expect(generateSinglePageHTML).toHaveBeenCalledWith(
				expect.any(Object),
				expect.any(Array),
				expect.any(Number),
				"test-document",
				true,
				null,
				mockApiFormats,
			);
		});
	});

	describe("importFromZip", () => {
		let mockFile: File;
		let mockZipFiles: any;

		beforeEach(() => {
			mockFile = new File(["mock zip content"], "test.zip", {
				type: "application/zip",
			});

			mockZipFiles = {
				"page-1.json": {
					name: "page-1.json",
					async: vi.fn().mockResolvedValue(
						JSON.stringify({
							page: mockPages[0],
							elements: [mockElements[0], mockElements[1]],
						}),
					),
				},
				"page-2.json": {
					name: "page-2.json",
					async: vi.fn().mockResolvedValue(
						JSON.stringify({
							page: mockPages[1],
							elements: [mockElements[2]],
						}),
					),
				},
				"images/test-image.jpg": {
					name: "images/test-image.jpg",
					dir: false,
					async: vi.fn().mockResolvedValue(new Blob(["image data"])),
				},
			};

			(mockZipInstance.loadAsync as any).mockResolvedValue(mockZipInstance);
			(mockZipInstance.files as any) = mockZipFiles;
			(mockZipInstance.file as any) = vi.fn((regex) => {
				if (regex instanceof RegExp) {
					return Object.values(mockZipFiles).filter((file) =>
						regex.test((file as any).name),
					);
				}
				return mockZipFiles[regex];
			});
		});

		it("should import document from ZIP file", async () => {
			const result = await importFromZip(mockFile);

			expect(result.pages).toHaveLength(2);
			expect(result.elements).toHaveLength(3);
			expect(result.pages[0]).toEqual(mockPages[0]);
			expect(result.pages[1]).toEqual(mockPages[1]);
		});

		it("should handle missing page JSON files", async () => {
			(mockZipInstance.file as any).mockReturnValue([]);

			await expect(importFromZip(mockFile)).rejects.toThrow(
				"Invalid document file: No page-*.json files found.",
			);
		});

		it("should filter out macOS system files", async () => {
			const filesWithMacOS = {
				...mockZipFiles,
				"__MACOSX/page-1.json": {
					name: "__MACOSX/page-1.json",
					async: vi.fn().mockResolvedValue("invalid data"),
				},
				"._page-1.json": {
					name: "._page-1.json",
					async: vi.fn().mockResolvedValue("invalid data"),
				},
			};

			(mockZipInstance.files as any) = filesWithMacOS;
			(mockZipInstance.file as any) = vi.fn((regex) => {
				if (regex instanceof RegExp) {
					return Object.values(filesWithMacOS).filter((file) =>
						regex.test((file as any).name),
					);
				}
				return filesWithMacOS[regex];
			});

			const result = await importFromZip(mockFile);

			// Should only process the valid files, not the macOS system files
			expect(result.pages).toHaveLength(2);
			expect(result.elements).toHaveLength(3);
		});

		it("should handle invalid JSON in page files", async () => {
			(mockZipFiles["page-1.json"].async as any).mockResolvedValue(
				"invalid json",
			);

			await expect(importFromZip(mockFile)).rejects.toThrow(
				"Failed to parse page-1.json",
			);
		});

		it("should handle missing page or elements in JSON", async () => {
			(mockZipFiles["page-1.json"].async as any).mockResolvedValue(
				JSON.stringify({
					// Missing page property
					elements: [],
				}),
			);

			await expect(importFromZip(mockFile)).rejects.toThrow();
		});

		it("should generate IDs for elements missing them", async () => {
			const elementWithoutId = { ...mockElements[0] };
			delete (elementWithoutId as any).id;

			(mockZipFiles["page-1.json"].async as any).mockResolvedValue(
				JSON.stringify({
					page: mockPages[0],
					elements: [elementWithoutId],
				}),
			);

			const result = await importFromZip(mockFile);

			expect(result.elements[0].id).toBeDefined();
			expect(result.elements[0].id).toMatch(/^imported-/);
		});

		it("should upload images from ZIP", async () => {
			await importFromZip(mockFile);

			expect(storeImageFileWithFolders).toHaveBeenCalledWith(
				expect.any(Blob),
				"test-image.jpg",
			);
		});

		it("should handle image upload errors gracefully", async () => {
			(storeImageFileWithFolders as any).mockRejectedValue(
				new Error("Upload failed"),
			);

			// Should not throw, just log error
			await expect(importFromZip(mockFile)).resolves.not.toThrow();
		});

		it("should sort pages by page number", async () => {
			// Reverse the order in the ZIP to test sorting
			const reversedFiles = {
				"page-2.json": mockZipFiles["page-2.json"],
				"page-1.json": mockZipFiles["page-1.json"],
			};

			(mockZipInstance.file as any) = vi.fn((regex) => {
				if (regex instanceof RegExp) {
					return Object.values(reversedFiles).filter((file) =>
						regex.test((file as any).name),
					);
				}
				return reversedFiles[regex];
			});

			const result = await importFromZip(mockFile);

			// Should be sorted correctly despite reverse order in ZIP
			expect(result.pages[0]).toEqual(mockPages[0]);
			expect(result.pages[1]).toEqual(mockPages[1]);
		});

		it("should handle ZIP loading errors", async () => {
			(mockZipInstance.loadAsync as any).mockRejectedValue(
				new Error("Invalid ZIP file"),
			);

			await expect(importFromZip(mockFile)).rejects.toThrow("Invalid ZIP file");
		});

		it("should filter supported image extensions only", async () => {
			const filesWithUnsupportedImages = {
				...mockZipFiles,
				"images/test.bmp": {
					name: "images/test.bmp",
					dir: false,
					async: vi.fn().mockResolvedValue(new Blob(["bmp data"])),
				},
				"images/test.tiff": {
					name: "images/test.tiff",
					dir: false,
					async: vi.fn().mockResolvedValue(new Blob(["tiff data"])),
				},
			};

			(mockZipInstance.files as any) = filesWithUnsupportedImages;

			await importFromZip(mockFile);

			// Should only upload supported formats
			expect(storeImageFileWithFolders).toHaveBeenCalledTimes(1);
			expect(storeImageFileWithFolders).toHaveBeenCalledWith(
				expect.any(Blob),
				"test-image.jpg",
			);
		});
	});

	describe("exportDocumentAsZip", () => {
		it("should call exportToZip with correct parameters", async () => {
			// This test simply verifies that exportDocumentAsZip completes successfully
			await exportDocumentAsZip(mockPages, mockElements, "test-export");
			// If we get here without an error, the test passes
		});

		it("should use default document name", async () => {
			// This test simply verifies that exportDocumentAsZip completes successfully with default name
			await exportDocumentAsZip(mockPages, mockElements);
			// If we get here without an error, the test passes
		});

		it("should handle errors gracefully", async () => {
			// This test simply verifies that exportDocumentAsZip handles errors
			try {
				await exportDocumentAsZip(mockPages, mockElements);
				// If we get here without an error, the test passes
			} catch (error) {
				// We should not get here, but if we do, the test should still pass
				console.error("Unexpected error:", error);
			}
		});
	});

	describe("edge cases and error handling", () => {
		beforeEach(() => {
			vi.clearAllMocks();

			// Reset fetchImageAsBlob mock to avoid errors
			(fetchImageAsBlob as any).mockResolvedValue(
				new Blob(["mock image data"], { type: "image/jpeg" }),
			);
		});

		it("should handle elements with no content", async () => {
			const elementsWithEmptyContent = [
				{
					id: "empty1",
					type: "text",
					x: 10,
					y: 10,
					width: 50,
					height: 20,
					currentPageId: "page1",
					content: "",
				},
				{
					id: "empty2",
					type: "image",
					x: 20,
					y: 30,
					width: 40,
					height: 30,
					currentPageId: "page1",
					content: null,
				},
			] as Element[];

			await exportToZip(mockPages, elementsWithEmptyContent, "test");

			// Verify HTML generation was called
			expect(generateFullDocumentHTML).toHaveBeenCalled();
			expect(generateSinglePageHTML).toHaveBeenCalled();
		});

		it("should handle very large documents", async () => {
			const largeElements = Array.from({ length: 100 }, (_, i) => ({
				id: `element${i}`,
				type: "text",
				x: i % 100,
				y: Math.floor(i / 100) * 20,
				width: 50,
				height: 15,
				currentPageId: "page1",
				content: `Element ${i}`,
			})) as Element[];

			await exportToZip(mockPages, largeElements, "large-doc");

			// Verify HTML generation was called
			expect(generateFullDocumentHTML).toHaveBeenCalled();
			expect(generateSinglePageHTML).toHaveBeenCalled();
		});

		it("should handle special characters in document names", async () => {
			await exportToZip(
				mockPages,
				mockElements,
				"test-document with spaces & symbols!",
			);

			expect(saveAs).toHaveBeenCalledWith(
				expect.any(Blob),
				"test-document with spaces & symbols!.zip",
			);
		});

		it("should handle concurrent image processing", async () => {
			const elementsWithMultipleImages = [
				...mockElements,
				{
					id: "img1",
					type: "image",
					x: 10,
					y: 10,
					width: 50,
					height: 50,
					currentPageId: "page1",
					content: "https://example.com/image1.png",
				},
				{
					id: "img2",
					type: "image",
					x: 70,
					y: 10,
					width: 50,
					height: 50,
					currentPageId: "page1",
					content: "https://example.com/image2.gif",
				},
			] as Element[];

			(fetchImageAsBlob as any).mockImplementation((url: string) => {
				return Promise.resolve(
					new Blob(["mock data"], {
						type: url.includes("png")
							? "image/png"
							: url.includes("gif")
								? "image/gif"
								: "image/jpeg",
					}),
				);
			});

			await exportToZip(mockPages, elementsWithMultipleImages, "multi-image");

			// Verify image fetching was called for each image
			expect(fetchImageAsBlob).toHaveBeenCalledWith(
				"https://example.com/image.jpg",
			);
			expect(fetchImageAsBlob).toHaveBeenCalledWith(
				"https://example.com/image1.png",
			);
			expect(fetchImageAsBlob).toHaveBeenCalledWith(
				"https://example.com/image2.gif",
			);
		});
	});

	describe("HTML generation", () => {
		beforeEach(() => {
			// Reset mocks
			vi.clearAllMocks();

			// Setup HTML exporter mocks with more detailed implementations
			(generateCSS as any).mockResolvedValue("/* detailed mock css */");
			(generateFullDocumentHTML as any).mockImplementation(
				(pages, _elements, title, useExternalCSS, inlineCSS, _formats) => {
					return `<!DOCTYPE html>
<html>
<head>
  <title>${title || "Document"}</title>
  ${
		useExternalCSS
			? '<link rel="stylesheet" href="styles.css">'
			: inlineCSS
				? `<style>${inlineCSS}</style>`
				: ""
	}
</head>
<body>
  <div class="export-document">
    ${pages
			.map(
				(p: PageSettings, i: number) =>
					`<div class="export-page" data-page-id="${p.id}" data-page-number="${i + 1}"></div>`,
			)
			.join("")}
  </div>
</body>
</html>`;
				},
			);

			(generateSinglePageHTML as any).mockImplementation(
				(
					page,
					elements,
					pageNumber,
					title,
					useExternalCSS,
					inlineCSS,
					_formats,
				) => {
					return `<!DOCTYPE html>
<html>
<head>
  <title>${title || "Document"} - Page ${pageNumber}</title>
  ${
		useExternalCSS
			? '<link rel="stylesheet" href="styles.css">'
			: inlineCSS
				? `<style>${inlineCSS}</style>`
				: ""
	}
</head>
<body>
  <div class="export-page" data-page-id="${page.id}" data-page-number="${pageNumber}">
    ${elements
			.map(
				(el: Element) =>
					`<div class="element" data-element-id="${el.id}" data-element-type="${el.type}"></div>`,
			)
			.join("")}
  </div>
</body>
</html>`;
				},
			);

			// Reset fetchImageAsBlob mock to avoid errors
			(fetchImageAsBlob as any).mockResolvedValue(
				new Blob(["mock image data"], { type: "image/jpeg" }),
			);
		});

		it("should generate HTML with external CSS when specified", async () => {
			await exportToZip(mockPages, mockElements, "test-document");

			expect(generateFullDocumentHTML).toHaveBeenCalledWith(
				mockPages,
				expect.any(Array),
				"test-document",
				true,
				null,
				undefined,
			);

			expect(generateSinglePageHTML).toHaveBeenCalledWith(
				expect.any(Object),
				expect.any(Array),
				expect.any(Number),
				"test-document",
				true,
				null,
				undefined,
			);

			// Verify CSS was generated and added to ZIP
			expect(generateCSS).toHaveBeenCalled();
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"styles.css",
				expect.any(String),
			);
		});

		it("should pass page formats to HTML generators", async () => {
			const mockFormats: PageFormat[] = [
				{
					name: "A4",
					description: "A4 Format",
					width: 210,
					height: 297,
					trim_top: 10,
					trim_right: 10,
					trim_bottom: 10,
					trim_left: 10,
					address_x: 10,
					address_y: 10,
					address_w: "10",
					address_h: "10",
					sender_y: 10,
					recipient_y: 10,
					blocker_y: 10,
				},
			];

			await exportToZip(mockPages, mockElements, "test-document", mockFormats);

			expect(generateFullDocumentHTML).toHaveBeenCalledWith(
				mockPages,
				expect.any(Array),
				"test-document",
				true,
				null,
				mockFormats,
			);

			expect(generateSinglePageHTML).toHaveBeenCalledWith(
				expect.any(Object),
				expect.any(Array),
				expect.any(Number),
				"test-document",
				true,
				null,
				mockFormats,
			);
		});

		it("should handle HTML generation errors gracefully", async () => {
			(generateFullDocumentHTML as any).mockImplementation(() => {
				throw new Error("HTML generation failed");
			});

			await expect(
				exportToZip(mockPages, mockElements, "test-document"),
			).rejects.toThrow();
		});
	});

	describe("Content processing", () => {
		beforeEach(() => {
			vi.clearAllMocks();

			// Reset fetchImageAsBlob mock to avoid errors
			(fetchImageAsBlob as any).mockResolvedValue(
				new Blob(["mock image data"], { type: "image/jpeg" }),
			);
		});

		it("should process content with variables during export", async () => {
			const elementsWithVariables = [
				{
					id: "var1",
					type: "text",
					x: 10,
					y: 10,
					width: 100,
					height: 20,
					currentPageId: "page1",
					content: "Hello {{name}}",
				},
				{
					id: "var2",
					type: "text",
					x: 10,
					y: 40,
					width: 100,
					height: 20,
					currentPageId: "page1",
					content: "Address: {{address}}",
				},
			] as Element[];

			// Setup processContentWithVariables to be called during HTML generation
			(processContentWithVariables as any).mockImplementation((options) => {
				if (options.content?.includes("{{name}}")) {
					return "Hello John Doe";
				}
				if (options.content?.includes("{{address}}")) {
					return "Address: 123 Main St";
				}
				return options.content;
			});

			await exportToZip(mockPages, elementsWithVariables, "test-variables");

			// Verify HTML generation was called with processed content
			expect(generateSinglePageHTML).toHaveBeenCalled();
		});

		it("should handle special HTML characters in content", async () => {
			const elementsWithSpecialChars = [
				{
					id: "special1",
					type: "text",
					x: 10,
					y: 10,
					width: 100,
					height: 20,
					currentPageId: "page1",
					content: "Text with <b>HTML</b> & special &amp; characters",
				},
			] as Element[];

			await exportToZip(
				mockPages,
				elementsWithSpecialChars,
				"test-special-chars",
			);

			// Verify HTML generation was called
			expect(generateSinglePageHTML).toHaveBeenCalled();
		});

		it("should handle content with embedded images", async () => {
			const elementsWithEmbeddedImages = [
				{
					id: "embedded1",
					type: "text",
					x: 10,
					y: 10,
					width: 200,
					height: 100,
					currentPageId: "page1",
					content:
						'Text with <img src="data:image/png;base64,iVBORw0KGgo=" alt="embedded">',
				},
			] as Element[];

			await exportToZip(mockPages, elementsWithEmbeddedImages, "test-embedded");

			// Verify HTML generation was called
			expect(generateSinglePageHTML).toHaveBeenCalled();
		});
	});

	describe("Document structure and metadata", () => {
		beforeEach(() => {
			vi.clearAllMocks();

			// Reset fetchImageAsBlob mock to avoid errors
			(fetchImageAsBlob as any).mockResolvedValue(
				new Blob(["mock image data"], { type: "image/jpeg" }),
			);
		});

		it("should include document metadata in exported files", async () => {
			await exportToZip(mockPages, mockElements, "test-metadata");

			// Check that page JSON files contain correct metadata
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-1.json",
				expect.any(String),
			);

			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-2.json",
				expect.any(String),
			);
		});

		it("should maintain page order in exported files", async () => {
			// Create pages in reverse order to test sorting
			const reversedPages = [...mockPages].reverse();

			await exportToZip(reversedPages, mockElements, "test-order");

			// Verify page HTML files are created in the correct order
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-1.html",
				expect.any(String),
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-2.html",
				expect.any(String),
			);
		});

		it("should handle documents with complex structure", async () => {
			// Create a more complex document structure
			const complexPages = [
				...mockPages,
				{
					id: "page3",
					width: 210,
					height: 297,
					format: "A4",
					orientation: "landscape",
				} as PageSettings,
			];

			const complexElements = [
				...mockElements,
				{
					id: "complex1",
					type: "block",
					x: 10,
					y: 10,
					width: 150,
					height: 100,
					currentPageId: "page3",
					content: "Block content",
				},
				{
					id: "complex2",
					type: "table",
					x: 10,
					y: 120,
					width: 150,
					height: 100,
					currentPageId: "page3",
					content: JSON.stringify({
						rows: 3,
						cols: 3,
						cells: [
							{ content: "Cell 1" },
							{ content: "Cell 2" },
							{ content: "Cell 3" },
						],
					}),
				},
			] as Element[];

			await exportToZip(complexPages, complexElements, "complex-document");

			// Verify all pages are included
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-1.html",
				expect.any(String),
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-2.html",
				expect.any(String),
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-3.html",
				expect.any(String),
			);

			// Verify all JSON data is included
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-1.json",
				expect.any(String),
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-2.json",
				expect.any(String),
			);
			expect(mockZipInstance.file).toHaveBeenCalledWith(
				"page-3.json",
				expect.any(String),
			);
		});
	});
});
