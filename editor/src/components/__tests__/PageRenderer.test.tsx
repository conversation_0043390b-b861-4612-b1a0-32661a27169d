import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import React from "react";
import { describe, expect, it, vi } from "vitest";

import { PageRenderer } from "../PageRenderer";

// Mock the EditableElement component
vi.mock("../elements/EditableElement", () => ({
	EditableElement: ({ element }) => (
		<div data-testid={`element-${element.id}`} data-element-type={element.type}>
			Element: {element.type}
		</div>
	),
}));

// Mock the CropMarks component
vi.mock("../CropMarks", () => ({
	CropMarks: () => <div data-testid="crop-marks">Crop Marks</div>,
}));

// Mock the FoldMarks component
vi.mock("../FoldMarks", () => ({
	FoldMarks: () => <div data-testid="fold-marks">Fold Marks</div>,
}));

describe("PageRenderer component", () => {
	// Common props for all tests
	const mockPage = {
		id: "page-1",
		format: "A4",
		orientation: "portrait" as const,
		width: 210,
		height: 297,
	};

	const mockElements = [
		{
			id: "element-1",
			currentPageId: "page-1",
			type: "text" as const,
			x: 10,
			y: 20,
			width: 100,
			height: 50,
			rotation: 0,
			zIndex: 1,
		},
		{
			id: "element-2",
			currentPageId: "page-1",
			type: "image" as const,
			x: 120,
			y: 20,
			width: 80,
			height: 60,
			rotation: 0,
			zIndex: 2,
		},
		{
			id: "element-3",
			currentPageId: "page-2", // Different page
			type: "shape" as const,
			x: 10,
			y: 20,
			width: 100,
			height: 50,
			rotation: 0,
		},
		{
			id: "element-4",
			currentPageId: "page-1",
			type: "block" as const,
			x: 10,
			y: 100,
			width: 150,
			height: 100,
			rotation: 0,
			zIndex: 3,
			childElementIds: ["element-5"],
		},
		{
			id: "element-5",
			currentPageId: "page-1",
			type: "text" as const,
			x: 20,
			y: 110,
			width: 80,
			height: 40,
			rotation: 0,
			parentId: "element-4", // Child of block element
		},
	];

	const mockOnUpdateElement = vi.fn();
	const mockOnDeleteElement = vi.fn();
	const mockOnCopyElement = vi.fn();
	const mockOnCutElement = vi.fn();
	const mockOnSaveAsTemplate = vi.fn();
	const mockOnSelectElement = vi.fn();
	const mockOnEditingChange = vi.fn();
	const mockOnPageClick = vi.fn();
	const mockOnPageContextMenu = vi.fn();
	const mockSetActiveEditor = vi.fn();
	const mockUpdateSnapGuides = vi.fn();
	const mockCalculateSnapGuides = vi
		.fn()
		.mockReturnValue({ snapX: null, snapY: null, guides: [] });
	const mockSetIsTextEditorFocused = vi.fn();
	const mockSetEditingBlockId = vi.fn();

	const pageRef = React.createRef<HTMLDivElement>();

	const defaultProps = {
		page: mockPage,
		elements: mockElements,
		selectedElementId: null,
		editingBlockId: null,
		zoomLevel: 1,
		showElementBorders: true,
		showFoldMarks: false,
		showPageNumbers: false,
		deletedPageNumberIds: new Set<string>(),
		testData: [],
		selectedTestDataIndex: -1,
		highlightVariables: false,
		isEditorLocked: false,
		onUpdateElement: mockOnUpdateElement,
		onDeleteElement: mockOnDeleteElement,
		onCopyElement: mockOnCopyElement,
		onCutElement: mockOnCutElement,
		onSaveAsTemplate: mockOnSaveAsTemplate,
		onSelectElement: mockOnSelectElement,
		onEditingChange: mockOnEditingChange,
		onPageClick: mockOnPageClick,
		onPageContextMenu: mockOnPageContextMenu,
		pageRef,
		setActiveEditor: mockSetActiveEditor,
		updateSnapGuides: mockUpdateSnapGuides,
		calculateSnapGuides: mockCalculateSnapGuides,
		setIsTextEditorFocused: mockSetIsTextEditorFocused,
		setEditingBlockId: mockSetEditingBlockId,
	};

	it("renders page with correct dimensions", () => {
		render(<PageRenderer {...defaultProps} />);

		const pageContainer = screen.getByRole("application");
		expect(pageContainer).toBeInTheDocument();

		// Check that the page has the correct dimensions
		// A4 is 210x297mm, which is converted to pixels in the component
		expect(pageContainer.style.width).toBeTruthy();
		expect(pageContainer.style.height).toBeTruthy();
	});

	it("renders only elements for the current page", () => {
		render(<PageRenderer {...defaultProps} />);

		// Elements on page-1 should be rendered
		expect(screen.getByTestId("element-element-1")).toBeInTheDocument();
		expect(screen.getByTestId("element-element-2")).toBeInTheDocument();
		expect(screen.getByTestId("element-element-4")).toBeInTheDocument();

		// Element on page-2 should not be rendered
		expect(screen.queryByTestId("element-element-3")).not.toBeInTheDocument();

		// Child element of block should not be rendered directly (handled by EditableElement)
		expect(screen.queryByTestId("element-element-5")).not.toBeInTheDocument();
	});

	it("renders elements in correct z-index order", () => {
		render(<PageRenderer {...defaultProps} />);

		const elements = screen.getAllByTestId(/element-element-/);

		// Elements should be rendered in z-index order
		expect(elements[0].getAttribute("data-testid")).toBe("element-element-1"); // zIndex: 1
		expect(elements[1].getAttribute("data-testid")).toBe("element-element-2"); // zIndex: 2
		expect(elements[2].getAttribute("data-testid")).toBe("element-element-4"); // zIndex: 3
	});

	it("renders crop marks", () => {
		render(<PageRenderer {...defaultProps} />);

		expect(screen.getByTestId("crop-marks")).toBeInTheDocument();
	});

	it("renders fold marks when showFoldMarks is true", () => {
		render(<PageRenderer {...defaultProps} showFoldMarks={true} />);

		expect(screen.getByTestId("fold-marks")).toBeInTheDocument();
	});

	it("does not render fold marks when showFoldMarks is false", () => {
		render(<PageRenderer {...defaultProps} showFoldMarks={false} />);

		expect(screen.queryByTestId("fold-marks")).not.toBeInTheDocument();
	});

	it("calls onPageClick when page is clicked", async () => {
		const user = userEvent.setup();
		render(<PageRenderer {...defaultProps} />);

		const pageContainer = screen.getByRole("application");
		await user.click(pageContainer);

		expect(mockOnPageClick).toHaveBeenCalledTimes(1);
	});

	it("calls onPageContextMenu when right-clicking the page", async () => {
		const user = userEvent.setup();
		render(<PageRenderer {...defaultProps} />);

		const pageContainer = screen.getByRole("application");
		await user.pointer({ keys: "[MouseRight]", target: pageContainer });

		expect(mockOnPageContextMenu).toHaveBeenCalledTimes(1);
		expect(mockOnPageContextMenu).toHaveBeenCalledWith(
			expect.anything(),
			mockPage,
		);
	});

	it("renders page number when showPageNumbers is true", () => {
		const pageWithPageNumber = {
			...mockPage,
			pagenumber_x: 10,
			pagenumber_y: 10,
		};

		render(
			<PageRenderer
				{...defaultProps}
				page={pageWithPageNumber}
				showPageNumbers={true}
			/>,
		);

		// The page number is calculated based on the index of the page in the elements array
		// In our test setup, it's showing "0" instead of "1"
		expect(screen.getByText("0")).toBeInTheDocument();
	});

	it("does not render page number when showPageNumbers is false", () => {
		const pageWithPageNumber = {
			...mockPage,
			pagenumber_x: 10,
			pagenumber_y: 10,
		};

		render(
			<PageRenderer
				{...defaultProps}
				page={pageWithPageNumber}
				showPageNumbers={false}
			/>,
		);

		expect(screen.queryByText("1")).not.toBeInTheDocument();
	});

	it("applies zoom level to page", () => {
		render(<PageRenderer {...defaultProps} zoomLevel={0.5} />);

		const pageContainer = screen.getByRole("application");
		expect(pageContainer.style.transform).toBe("scale(0.5)");
	});
});
