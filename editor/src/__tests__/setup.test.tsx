import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";

// Simple test component
const TestComponent = () => <div data-testid="test">Hello Test</div>;

describe("Test Setup Verification", () => {
	it("should render React components correctly", () => {
		render(<TestComponent />);
		expect(screen.getByTestId("test")).toBeInTheDocument();
		expect(screen.getByText("Hello Test")).toBeInTheDocument();
	});

	it("should have access to custom matchers", () => {
		const mockElement = {
			id: "test-element",
			type: "text",
			x: 10,
			y: 20,
			width: 100,
			height: 50,
			zIndex: 1,
		};

		expect(mockElement).toBeValidElement();
		expect(mockElement).toHaveValidPosition();
		expect(mockElement).toHaveValidZIndex();
	});

	it("should have mocked browser APIs available", () => {
		expect(window.matchMedia).toBeDefined();
		expect(global.ResizeObserver).toBeDefined();
		expect(global.IntersectionObserver).toBeDefined();
		expect(window.localStorage).toBeDefined();
		expect(window.sessionStorage).toBeDefined();
	});

	it("should have mocked canvas context", () => {
		const canvas = document.createElement("canvas");
		const ctx = canvas.getContext("2d");
		expect(ctx).toBeDefined();
		expect(ctx?.fillRect).toBeDefined();
		expect(vi.isMockFunction(ctx?.fillRect)).toBe(true);
	});

	it("should clean up mocks between tests", () => {
		const mockFn = vi.fn();
		mockFn("test");
		expect(mockFn).toHaveBeenCalledWith("test");

		// This test verifies that cleanup happens automatically
		// The actual cleanup verification happens in the afterEach hook
	});
});
