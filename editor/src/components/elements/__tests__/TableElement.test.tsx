import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";
import type { Element } from "@/types/element";
import type { TableProperties } from "@/types/table";
import { TableElement } from "../TableElement";

// Mock the Table and TableCreateDialog components only for TableElement tests
vi.mock("@/components/table/Table", () => ({
	Table: () => <div data-testid="table-component">Table Mock</div>,
}));

vi.mock("@/components/table/TableCreateDialog", () => ({
	TableCreateDialog: ({ onCreateTable, onClose }) => (
		<div data-testid="table-create-dialog">
			<button
				data-testid="create-table-btn"
				type="button"
				onClick={() =>
					onCreateTable({
						rows: 3,
						columns: 3,
						borderWidth: 1,
						borderStyle: "solid",
						cells: [
							[
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
							],
							[
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
							],
							[
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
								{
									content: "",
									colspan: 1,
									rowspan: 1,
									backgroundColor: null,
									borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
								},
							],
						],
						columnWidths: [20, 20, 20],
						rowHeights: [10, 10, 10],
					})
				}
			>
				Create Table
			</button>
			<button data-testid="cancel-btn" type="button" onClick={onClose}>
				Cancel
			</button>
		</div>
	),
}));

describe("TableElement component", () => {
	beforeAll(() => {
		vi.mock("@/components/table/Table", () => ({
			Table: () => <div data-testid="table-component">Table Mock</div>,
		}));
		vi.mock("@/components/table/TableCreateDialog", () => ({
			TableCreateDialog: ({ onCreateTable, onClose }) => (
				<div data-testid="table-create-dialog">
					<button
						data-testid="create-table-btn"
						type="button"
						onClick={() =>
							onCreateTable({
								rows: 3,
								columns: 3,
								borderWidth: 1,
								borderStyle: "solid",
								cells: [
									[
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
									],
									[
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
									],
									[
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
										{
											content: "",
											colspan: 1,
											rowspan: 1,
											backgroundColor: null,
											borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
										},
									],
								],
								columnWidths: [20, 20, 20],
								rowHeights: [10, 10, 10],
							})
						}
					>
						Create Table
					</button>
					<button data-testid="cancel-btn" type="button" onClick={onClose}>
						Cancel
					</button>
				</div>
			),
		}));
	});

	afterEach(() => {
		vi.resetModules();
	});

	const mockTableProperties: TableProperties = {
		rows: 3,
		columns: 3,
		borderWidth: 1,
		borderStyle: "solid",
		cells: [
			[
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
			],
			[
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
			],
			[
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
				{
					content: "",
					colspan: 1,
					rowspan: 1,
					backgroundColor: null,
					borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				},
			],
		],
		columnWidths: [20, 20, 20],
		rowHeights: [10, 10, 10],
	};

	const mockElement: Element = {
		id: "table-1",
		currentPageId: "page-1",
		type: "table",
		x: 10,
		y: 20,
		width: 60, // Sum of column widths
		height: 30, // Sum of row heights
		rotation: 0,
		tableProperties: mockTableProperties,
	};

	const mockTestData = [{ id: "1", name: "Test Data" }];
	const mockOnUpdate = vi.fn();
	const mockOnDelete = vi.fn();

	it("renders Table component when tableProperties exist", () => {
		render(
			<TableElement
				element={mockElement}
				testData={mockTestData}
				selectedTestDataIndex={0}
				highlightVariables={false}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
			/>,
		);

		expect(screen.getByTestId("table-component")).toBeInTheDocument();
	});

	it("renders TableCreateDialog when no tableProperties exist", async () => {
		const user = userEvent.setup();
		const elementWithoutTableProps = {
			...mockElement,
			tableProperties: undefined,
		};

		render(
			<TableElement
				element={elementWithoutTableProps}
				testData={mockTestData}
				selectedTestDataIndex={0}
				highlightVariables={false}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
			/>,
		);

		expect(screen.getByTestId("table-create-dialog")).toBeInTheDocument();

		// Test creating a table
		const createButton = screen.getByTestId("create-table-btn");
		await user.click(createButton);

		expect(mockOnUpdate).toHaveBeenCalledTimes(1);
		expect(mockOnUpdate).toHaveBeenCalledWith(
			expect.objectContaining({
				id: "table-1",
				tableProperties: expect.objectContaining({
					rows: 3,
					columns: 3,
				}),
				width: 60, // Sum of column widths
				height: 30, // Sum of row heights
			}),
			true,
		);
	});

	it("calls onDelete when cancel is clicked in TableCreateDialog", async () => {
		const user = userEvent.setup();
		const elementWithoutTableProps = {
			...mockElement,
			tableProperties: undefined,
		};

		render(
			<TableElement
				element={elementWithoutTableProps}
				testData={mockTestData}
				selectedTestDataIndex={0}
				highlightVariables={false}
				onUpdate={mockOnUpdate}
				onDelete={mockOnDelete}
			/>,
		);

		const cancelButton = screen.getByTestId("cancel-btn");
		await user.click(cancelButton);

		expect(mockOnDelete).toHaveBeenCalledTimes(1);
		expect(mockOnDelete).toHaveBeenCalledWith("table-1");
	});
});
