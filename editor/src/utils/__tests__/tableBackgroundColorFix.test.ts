import { describe, expect, it } from "vitest";
import type { TableCell } from "@/types/table";
import { isCellSelectedInLogicalGrid } from "../tableUtils";

describe("Table Background Color Selection Fix", () => {
	// Create a test table with colspan cells that could cause the original bug
	const createTestTableWithColspan = (): TableCell[][] => [
		// Row 0: [A1, B1-C1 (colspan=2), D1]
		[
			{
				content: "A1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B1-C1",
				colspan: 2,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
		// Row 1: [A2, B2, C2, D2]
		[
			{
				content: "A2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
		// Row 2: [A3, B3, C3, D3]
		[
			{
				content: "A3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
	];

	it("should correctly identify selected cells when selecting B2 and C2 in a table with colspan", () => {
		const cells = createTestTableWithColspan();

		// Selection: logical cells B2 and C2 (row 1, cols 1-2)
		const selection = {
			start: { row: 1, col: 1 },
			end: { row: 1, col: 2 },
		};

		// Test each cell to verify correct selection detection

		// Row 0 cells should NOT be selected
		expect(isCellSelectedInLogicalGrid(cells, 0, 0, selection)).toBe(false); // A1
		expect(isCellSelectedInLogicalGrid(cells, 0, 1, selection)).toBe(false); // B1-C1 (colspan)
		expect(isCellSelectedInLogicalGrid(cells, 0, 2, selection)).toBe(false); // D1

		// Row 1 cells: only B2 and C2 should be selected
		expect(isCellSelectedInLogicalGrid(cells, 1, 0, selection)).toBe(false); // A2
		expect(isCellSelectedInLogicalGrid(cells, 1, 1, selection)).toBe(true); // B2 (selected)
		expect(isCellSelectedInLogicalGrid(cells, 1, 2, selection)).toBe(true); // C2 (selected)
		expect(isCellSelectedInLogicalGrid(cells, 1, 3, selection)).toBe(false); // D2

		// Row 2 cells should NOT be selected
		expect(isCellSelectedInLogicalGrid(cells, 2, 0, selection)).toBe(false); // A3
		expect(isCellSelectedInLogicalGrid(cells, 2, 1, selection)).toBe(false); // B3
		expect(isCellSelectedInLogicalGrid(cells, 2, 2, selection)).toBe(false); // C3
		expect(isCellSelectedInLogicalGrid(cells, 2, 3, selection)).toBe(false); // D3
	});

	it("should correctly identify selected cells when selecting the colspan cell B1-C1", () => {
		const cells = createTestTableWithColspan();

		// Selection: logical cells B1 and C1 (row 0, cols 1-2) - this selects the colspan cell
		const selection = {
			start: { row: 0, col: 1 },
			end: { row: 0, col: 2 },
		};

		// Row 0: only the B1-C1 colspan cell should be selected
		expect(isCellSelectedInLogicalGrid(cells, 0, 0, selection)).toBe(false); // A1
		expect(isCellSelectedInLogicalGrid(cells, 0, 1, selection)).toBe(true); // B1-C1 (selected)
		expect(isCellSelectedInLogicalGrid(cells, 0, 2, selection)).toBe(false); // D1

		// Other rows should not be selected
		expect(isCellSelectedInLogicalGrid(cells, 1, 0, selection)).toBe(false); // A2
		expect(isCellSelectedInLogicalGrid(cells, 1, 1, selection)).toBe(false); // B2
		expect(isCellSelectedInLogicalGrid(cells, 1, 2, selection)).toBe(false); // C2
		expect(isCellSelectedInLogicalGrid(cells, 1, 3, selection)).toBe(false); // D2
	});

	it("should correctly handle single cell selection in a table with colspan", () => {
		const cells = createTestTableWithColspan();

		// Selection: only C2 (row 1, col 2)
		const selection = {
			start: { row: 1, col: 2 },
			end: { row: 1, col: 2 },
		};

		// Only C2 should be selected
		expect(isCellSelectedInLogicalGrid(cells, 1, 2, selection)).toBe(true); // C2 (selected)

		// All other cells should not be selected
		expect(isCellSelectedInLogicalGrid(cells, 0, 0, selection)).toBe(false); // A1
		expect(isCellSelectedInLogicalGrid(cells, 0, 1, selection)).toBe(false); // B1-C1
		expect(isCellSelectedInLogicalGrid(cells, 0, 2, selection)).toBe(false); // D1
		expect(isCellSelectedInLogicalGrid(cells, 1, 0, selection)).toBe(false); // A2
		expect(isCellSelectedInLogicalGrid(cells, 1, 1, selection)).toBe(false); // B2
		expect(isCellSelectedInLogicalGrid(cells, 1, 3, selection)).toBe(false); // D2
		expect(isCellSelectedInLogicalGrid(cells, 2, 0, selection)).toBe(false); // A3
		expect(isCellSelectedInLogicalGrid(cells, 2, 1, selection)).toBe(false); // B3
		expect(isCellSelectedInLogicalGrid(cells, 2, 2, selection)).toBe(false); // C3
		expect(isCellSelectedInLogicalGrid(cells, 2, 3, selection)).toBe(false); // D3
	});
});
