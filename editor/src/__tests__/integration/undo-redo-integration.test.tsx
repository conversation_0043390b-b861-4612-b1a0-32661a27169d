import { act, renderHook } from "@testing-library/react";
import { useCallback, useState } from "react";
import { describe, expect, it } from "vitest";

// Mock version of useHistory without throttling for testing
function useTestHistory<T>(initialState: T) {
	const [history, setHistory] = useState<{
		past: T[];
		present: T;
		future: T[];
	}>({
		past: [],
		present: initialState,
		future: [],
	});

	const setState = useCallback((newPresent: T) => {
		setHistory((prev) => ({
			past: [...prev.past, prev.present],
			present: newPresent,
			future: [], // Clear future when adding new history entry
		}));
	}, []);

	const undo = useCallback(() => {
		setHistory((prev) => {
			if (prev.past.length === 0) return prev;

			// Take the last item from past as new present
			const previous = prev.past[prev.past.length - 1];
			const newPast = prev.past.slice(0, -1);

			return {
				past: newPast,
				present: previous,
				future: [prev.present, ...prev.future],
			};
		});
	}, []);

	const redo = useCallback(() => {
		setHistory((prev) => {
			if (prev.future.length === 0) return prev;

			const next = prev.future[0];
			const newFuture = prev.future.slice(1);

			return {
				past: [...prev.past, prev.present],
				present: next,
				future: newFuture,
			};
		});
	}, []);

	return {
		state: history.present,
		setState,
		undo,
		redo,
		canUndo: history.past.length > 0,
		canRedo: history.future.length > 0,
	};
}

describe("Undo/Redo Integration Tests", () => {
	it("should handle basic undo/redo operations", () => {
		// Create a history hook with initial state
		const initialState = { count: 0 };
		const { result } = renderHook(() => useTestHistory(initialState));

		// Initial state check
		expect(result.current.state.count).toBe(0);
		expect(result.current.canUndo).toBe(false);
		expect(result.current.canRedo).toBe(false);

		// Step 1: Increment count
		act(() => {
			result.current.setState({ count: 1 });
		});

		// Verify state was updated
		expect(result.current.state.count).toBe(1);
		expect(result.current.canUndo).toBe(true);
		expect(result.current.canRedo).toBe(false);

		// Step 2: Increment count again
		act(() => {
			result.current.setState({ count: 2 });
		});

		// Verify state was updated
		expect(result.current.state.count).toBe(2);
		expect(result.current.canUndo).toBe(true);
		expect(result.current.canRedo).toBe(false);

		// Step 3: Undo once
		act(() => {
			result.current.undo();
		});

		// Verify state went back to 1
		expect(result.current.state.count).toBe(1);
		expect(result.current.canUndo).toBe(true);
		expect(result.current.canRedo).toBe(true);

		// Step 4: Undo again
		act(() => {
			result.current.undo();
		});

		// Verify state went back to 0
		expect(result.current.state.count).toBe(0);
		expect(result.current.canUndo).toBe(false);
		expect(result.current.canRedo).toBe(true);

		// Step 5: Redo once
		act(() => {
			result.current.redo();
		});

		// Verify state went forward to 1
		expect(result.current.state.count).toBe(1);
		expect(result.current.canUndo).toBe(true);
		expect(result.current.canRedo).toBe(true);

		// Step 6: Redo again
		act(() => {
			result.current.redo();
		});

		// Verify state went forward to 2
		expect(result.current.state.count).toBe(2);
		expect(result.current.canUndo).toBe(true);
		expect(result.current.canRedo).toBe(false);
	});

	it("should handle history branching", () => {
		// Create a history hook with initial state
		const initialState = { count: 0 };
		const { result } = renderHook(() => useTestHistory(initialState));

		// Step 1: Increment count to 1
		act(() => {
			result.current.setState({ count: 1 });
		});

		// Step 2: Increment count to 2
		act(() => {
			result.current.setState({ count: 2 });
		});

		// Step 3: Undo back to 1
		act(() => {
			result.current.undo();
		});

		// Verify state is 1
		expect(result.current.state.count).toBe(1);

		// Step 4: Create a new branch by setting count to 10
		act(() => {
			result.current.setState({ count: 10 });
		});

		// Verify state is 10
		expect(result.current.state.count).toBe(10);
		expect(result.current.canUndo).toBe(true);
		expect(result.current.canRedo).toBe(false); // Future should be cleared

		// Step 5: Undo back to 1
		act(() => {
			result.current.undo();
		});

		// Verify state is 1
		expect(result.current.state.count).toBe(1);

		// Step 6: Undo back to 0
		act(() => {
			result.current.undo();
		});

		// Verify state is 0
		expect(result.current.state.count).toBe(0);

		// Step 7: Redo to 1
		act(() => {
			result.current.redo();
		});

		// Verify state is 1
		expect(result.current.state.count).toBe(1);

		// Step 8: Redo to 10 (not 2, since we created a new branch)
		act(() => {
			result.current.redo();
		});

		// Verify state is 10
		expect(result.current.state.count).toBe(10);
	});
});
