import type { VersionHistoryEntry } from "@/utils/apiService";
import { ToolbarEmpty, ToolbarHeader } from "./ToolbarBase";

interface VersionToolbarProps {
	versionHistory: VersionHistoryEntry[];
	currentVersion: string;
	onVersionChange: (newVersion: string) => void;
}

export function VersionToolbar({
	versionHistory,
	currentVersion,
	onVersionChange,
}: VersionToolbarProps) {
	// Sort version history in descending order (newest first)
	const sortedHistory = [...versionHistory].sort(
		(a, b) => b.version - a.version,
	);

	return (
		<div className="flex flex-col h-full">
			<ToolbarHeader title="Historie" />
			{sortedHistory.length > 0 ? (
				<ul className="flex-1 overflow-y-auto space-y-3 pr-2">
					{sortedHistory.map((entry) => {
						const isCurrent = entry.version.toString() === currentVersion;
						return (
							<li key={entry.version}>
								<button
									type="button"
									onClick={() => onVersionChange(entry.version.toString())}
									disabled={isCurrent}
									className={`block w-full text-left p-2 rounded-md transition-colors
                                                ${
																									isCurrent
																										? "bg-blue-100 text-blue-700 cursor-default"
																										: "hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
																								}
                                    `}
								>
									<span
										className={`font-medium ${isCurrent ? "" : "text-blue-600 hover:text-blue-800"}`}
									>
										Version {entry.version}
									</span>
									<p className="text-xs text-gray-600 mt-0.5">
										von {entry.user}
									</p>
									<p className="text-xs text-gray-500">
										{new Date(entry.created_at).toLocaleString("de-DE")}
									</p>
								</button>
							</li>
						);
					})}
				</ul>
			) : (
				<ToolbarEmpty message="Keine Versionshistorie verfügbar." />
			)}
		</div>
	);
}
