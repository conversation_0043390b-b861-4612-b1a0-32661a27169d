import { LayoutGrid } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { getScrollAwarePosition } from "@/utils/elementHelpers";
import type { Element } from "../../types/element";
import type { BaseToolbarProps, PageContextProps } from "../../types/toolbar";
import { PositionSizeSettings } from "./PositionSizeSettings";
import { ToolbarButton, ToolbarContainer, ToolbarEmpty } from "./ToolbarBase";

interface BlockToolbarProps extends BaseToolbarProps, PageContextProps {
	// Block-specific props can be added here if needed
}

export function BlockToolbar({
	onAddElement,
	selectedElement,
	onUpdateElement,
	currentPageId,
}: BlockToolbarProps) {
	const handleAddBlock = () => {
		if (currentPageId && onAddElement) {
			// Get scroll-aware position for the new block
			const { x, y } = getScrollAwarePosition();

			const newBlockElement: Element = {
				id: uuidv4(),
				type: "block",
				x: x,
				y: y,
				width: 100, // Default width in mm
				height: 50, // Default height in mm
				rotation: 0,
				currentPageId: currentPageId,
				childElementIds: [],
				zIndex: 1, // Default zIndex
			};
			onAddElement(newBlockElement);
		} else {
			// TODO: Maybe show a message to the user that a page needs to be active
			console.warn("Cannot add block: No current page selected.");
		}
	};

	const handleUpdateBlock = (updatedElement: Element) => {
		if (
			onUpdateElement &&
			selectedElement &&
			updatedElement.id === selectedElement.id
		) {
			onUpdateElement(updatedElement);
		}
	};

	return (
		<ToolbarContainer>
			<ToolbarButton
				icon={LayoutGrid}
				label="Baustein hinzufügen"
				onClick={handleAddBlock}
				disabled={!currentPageId}
			/>

			<PositionSizeSettings
				element={
					selectedElement && selectedElement.type === "block"
						? selectedElement
						: null
				}
				onUpdate={
					selectedElement && selectedElement.type === "block" && onUpdateElement
						? handleUpdateBlock
						: () => {}
				}
			/>

			{(!selectedElement || selectedElement.type !== "block") && (
				<ToolbarEmpty message="Wählen Sie einen Baustein aus, um dessen Position und Größe zu bearbeiten." />
			)}
		</ToolbarContainer>
	);
}
