import { type CommandProps, Extension, InputRule } from "@tiptap/core";
import { Color } from "@tiptap/extension-color";
import Paragraph, { type ParagraphOptions } from "@tiptap/extension-paragraph";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import TextAlign from "@tiptap/extension-text-align";
import { TextStyle } from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import type { Node as PMNode } from "@tiptap/pm/model";
import { type Editor, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useCallback, useEffect, useRef } from "react";

interface RichTextEditorProps {
	content: string;
	onChange: (content: string) => void;
	setActiveEditor?: (editor: Editor | null) => void;
	verticalAlign?: "top" | "middle" | "bottom";
	setIsTextEditorFocused?: (focused: boolean) => void;
	/**
	 * Optional callback that will be invoked when the underlying editor loses focus.
	 * This is useful for higher-level components (e.g. TableEditor) that need to
	 * react to the editor exiting its editing mode.
	 */
	onBlurCallback?: () => void;
	defaultFontSize?: string;
}

// Create a custom extension to handle font family and size
const CustomTextStyle = Extension.create({
	name: "customTextStyle",

	addGlobalAttributes() {
		return [
			{
				types: ["textStyle"],
				attributes: {
					fontFamily: {
						default: null,
						parseHTML: (element) => element.style.fontFamily,
						renderHTML: (attributes) => {
							if (!attributes.fontFamily) {
								return {};
							}
							return {
								style: `font-family: ${attributes.fontFamily}`,
							};
						},
					},
					fontSize: {
						default: null,
						parseHTML: (element) => element.style.fontSize,
						renderHTML: (attributes) => {
							if (!attributes.fontSize) {
								return {};
							}
							return {
								style: `font-size: ${attributes.fontSize}`,
							};
						},
					},
					verticalAlign: {
						default: null,
						parseHTML: (element) => element.style.verticalAlign,
						renderHTML: (attributes) => {
							if (!attributes.verticalAlign) {
								return {};
							}
							return {
								style: `vertical-align: ${attributes.verticalAlign}`,
								class: `vertical-align-${attributes.verticalAlign}`,
							};
						},
					},
					lineHeight: {
						default: null,
						parseHTML: (element) => element.style.lineHeight,
						renderHTML: (attributes) => {
							if (!attributes.lineHeight) {
								return {};
							}
							return {
								style: `line-height: ${attributes.lineHeight}`,
							};
						},
					},
					letterSpacing: {
						default: null,
						parseHTML: (element) => element.style.letterSpacing,
						renderHTML: (attributes) => {
							if (!attributes.letterSpacing) {
								return {};
							}
							return {
								style: `letter-spacing: ${attributes.letterSpacing}`,
							};
						},
					},
				},
			},
		];
	},
});

// Define options for the custom paragraph extension
interface CustomParagraphOptions extends ParagraphOptions {
	maxIndentLevel: number;
}

// Custom Paragraph extension that includes indent attributes
const CustomParagraph = Paragraph.extend<CustomParagraphOptions>({
	name: "paragraph", // Keep the name 'paragraph' for compatibility

	addAttributes() {
		return {
			...this.parent?.(),
			indent: {
				default: 0,
				renderHTML: (attributes) => {
					if (!attributes.indent || attributes.indent === 0) {
						return {};
					}
					return {
						style: `padding-left: ${attributes.indent * 0.75}em;`,
						class: `indent-${attributes.indent}`,
					};
				},
				parseHTML: (element) => {
					const indentMatch = element
						.getAttribute("class")
						?.match(/indent-(\d+)/);
					return indentMatch ? parseInt(indentMatch[1], 10) : 0;
				},
			},
		};
	},

	// Add input rules for auto-creating lists
	addInputRules() {
		return [
			// Bullet list rule: Start line with * or - followed by space
			new InputRule({
				find: /^\s*([-*])\s$/,
				handler: ({ range, commands }) => {
					commands.deleteRange(range);
					commands.insertContent("• ");
				},
			}),
			// Numbered list rule: Start line with number+dot+space (e.g., 1. )
			new InputRule({
				find: /^\s*(\d+)\.\s$/,
				handler: ({ range, match, commands }) => {
					commands.deleteRange(range);
					commands.insertContent(`${match[1]}. `);
				},
			}),
		];
	},
});

// Extension to register paragraph formatting commands globally
const ParagraphCommands = Extension.create({
	name: "paragraphCommands",

	addCommands() {
		return {
			increaseIndent:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;

					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							// Ensure the node is within the actual selection range
							const nodeStart = pos;
							const nodeEnd = pos + node.nodeSize;
							// Only process if the node is fully or partially selected
							if (
								Math.max(from, nodeStart) < Math.min(to, nodeEnd) ||
								(from === to && from > nodeStart && from < nodeEnd)
							) {
								const currentIndent = node.attrs.indent || 0;
								const newIndent = currentIndent + 1;

								// Optionally, add a max indent level check here if desired
								// if (newIndent <= MAX_INDENT_LEVEL) {
								tr = tr.setNodeMarkup(pos, undefined, {
									...node.attrs,
									indent: newIndent,
								});
								madeChange = true;
								// }
							}
						}
						return node.isBlock; // Continue traversal for block nodes
					});

					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
			decreaseIndent:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;

					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							// Ensure the node is within the actual selection range
							const nodeStart = pos;
							const nodeEnd = pos + node.nodeSize;
							if (
								Math.max(from, nodeStart) < Math.min(to, nodeEnd) ||
								(from === to && from > nodeStart && from < nodeEnd)
							) {
								const currentIndent = node.attrs.indent || 0;
								const newIndent = Math.max(currentIndent - 1, 0);

								if (currentIndent !== newIndent) {
									tr = tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										indent: newIndent,
									});
									madeChange = true;
								}
							}
						}
						return node.isBlock; // Continue traversal for block nodes
					});

					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
			toggleBulletList:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;
					const targets: { pos: number; node: PMNode }[] = [];
					let totalSelected = 0;
					let bulletCount = 0;
					const bulletPrefix = "• ";
					const numberRegex = /^(\d+\.\s)/;

					// 1. Identify targets and count current state
					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							const paragraphEndPos = pos + node.nodeSize;
							const overlaps =
								(pos < to && paragraphEndPos > from) ||
								(from === to && pos < from && paragraphEndPos > from);
							if (overlaps) {
								targets.push({ pos, node });
								totalSelected++;
								if (node.textContent.startsWith(bulletPrefix)) {
									bulletCount++;
								}
							}
						}
						return node.isBlock;
					});

					if (targets.length === 0) return false;

					// 2. Determine final action for the whole selection
					const finalAction = bulletCount === totalSelected ? "remove" : "add";

					// 3. Apply changes in reverse order based on finalAction
					targets.reverse().forEach((target) => {
						const { pos, node } = target;
						const paragraphStartPos = pos + 1;
						const currentText = node.textContent;
						const isCurrentlyBullet = currentText.startsWith(bulletPrefix);
						const numberMatch = currentText.match(numberRegex);

						if (finalAction === "remove") {
							if (isCurrentlyBullet) {
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + bulletPrefix.length,
								);
								madeChange = true;
							}
						} else {
							// finalAction === 'add'
							if (numberMatch) {
								// Remove existing number prefix first
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + numberMatch[1].length,
								);
							}
							if (!isCurrentlyBullet) {
								// Add bullet prefix if it doesn't exist
								tr = tr.insertText(bulletPrefix, paragraphStartPos);
								madeChange = true;
							}
						}
					});

					// 4. Dispatch transaction
					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
			toggleNumberedList:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;
					const targets: { pos: number; node: PMNode; finalNumber?: number }[] =
						[];
					let totalSelected = 0;
					let numberedCount = 0;
					const numberRegex = /^(\d+\.\s)/;
					const bulletPrefix = "• ";

					// 1. Identify targets and count current state
					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							const paragraphEndPos = pos + node.nodeSize;
							const overlaps =
								(pos < to && paragraphEndPos > from) ||
								(from === to && pos < from && paragraphEndPos > from);
							if (overlaps) {
								targets.push({ pos, node });
								totalSelected++;
								if (node.textContent.match(numberRegex)) {
									numberedCount++;
								}
							}
						}
						return node.isBlock;
					});

					if (targets.length === 0) return false;

					// 2. Determine final action for the whole selection
					const finalAction =
						numberedCount === totalSelected ? "remove" : "add";

					// 3. Pre-calculate numbers if adding
					if (finalAction === "add") {
						let listCounter = 1;
						targets.forEach((target) => {
							target.finalNumber = listCounter++;
						});
					}

					// 4. Apply changes in reverse order based on finalAction
					targets.reverse().forEach((target) => {
						const { pos, node, finalNumber } = target;
						const paragraphStartPos = pos + 1;
						const currentText = node.textContent;
						const currentNumberMatch = currentText.match(numberRegex);
						const isCurrentlyBullet = currentText.startsWith(bulletPrefix);

						if (finalAction === "remove") {
							if (currentNumberMatch) {
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + currentNumberMatch[1].length,
								);
								madeChange = true;
							}
						} else {
							// finalAction === 'add'
							const newNumberPrefix = `${finalNumber}. `;
							let needsInsert = false;

							// Remove bullet if present
							if (isCurrentlyBullet) {
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + bulletPrefix.length,
								);
								needsInsert = true; // Need to insert number after removing bullet
								madeChange = true;
							}
							// Remove existing number if different
							if (currentNumberMatch) {
								if (currentNumberMatch[0] !== newNumberPrefix) {
									tr = tr.delete(
										paragraphStartPos,
										paragraphStartPos + currentNumberMatch[1].length,
									);
									needsInsert = true;
									madeChange = true;
								} else {
									// Correct number exists, only insert if bullet was removed
									needsInsert = isCurrentlyBullet;
								}
							} else if (!isCurrentlyBullet) {
								// Neither bullet nor number exists, needs insert
								needsInsert = true;
							}

							// Insert the new number prefix if determined necessary
							if (needsInsert) {
								tr = tr.insertText(newNumberPrefix, paragraphStartPos);
								madeChange = true; // Ensure change is recorded if inserted
							}
						}
					});

					// 5. Dispatch transaction
					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
		};
	},

	addKeyboardShortcuts() {
		return {
			Tab: () => this.editor.commands.increaseIndent(),
			"Shift-Tab": () => this.editor.commands.decreaseIndent(),
			// 'Mod-Shift-8': () => this.editor.commands.toggleBulletList(), // Removed shortcut
			// 'Mod-Shift-7': () => this.editor.commands.toggleNumberedList(), // Removed shortcut

			Enter: () => {
				const { state, view } = this.editor;
				if (!view) return false;
				const { dispatch } = view;
				const { $from } = state.selection;
				const node = $from.node($from.depth);

				if (!node || node.type.name !== "paragraph") return false;

				const text = node.textContent;
				const bulletPrefix = "• ";
				const numberRegex = /^(\d+)\.\s/;
				const numberMatch = text.match(numberRegex);
				const isBulletLine = text.startsWith(bulletPrefix);
				const isNumberedLine = !!numberMatch;
				const isEmptyListItem =
					(isBulletLine && text === bulletPrefix) ||
					(isNumberedLine && text === numberMatch[0]);

				// Handle Enter on empty list item -> Untoggle list on the current line
				if (isEmptyListItem) {
					// Calculate the start position and length of the prefix
					const paragraphStartPos = $from.before($from.depth) + 1;
					const prefixLength = isBulletLine
						? bulletPrefix.length
						: numberMatch
							? numberMatch[0].length
							: 0;

					if (prefixLength > 0 && dispatch) {
						// Ensure dispatch exists
						// 1. Get existing marks to preserve them
						const marks = state.storedMarks || $from.marks();

						// 2. Create transaction, delete prefix, and set stored marks
						let tr = state.tr;
						tr = tr.delete(paragraphStartPos, paragraphStartPos + prefixLength);
						tr = tr.setStoredMarks(marks);

						// 3. Dispatch the transaction
						dispatch(tr);
						return true; // Command was handled
					} else {
						// Fallback if no prefix or dispatch is not available
						return false;
					}
				}

				// Handle standard list continuation using command chaining
				if (isBulletLine) {
					return this.editor
						.chain()
						.focus()
						.splitBlock({ keepMarks: true }) // Split and keep marks
						.insertContent(bulletPrefix) // Insert prefix (marks should be inherited)
						.run();
				}

				if (numberMatch) {
					const currentNumber = parseInt(numberMatch[1], 10);
					const nextNumber = currentNumber + 1;
					const nextNumberPrefix = `${nextNumber}. `;
					return this.editor
						.chain()
						.focus()
						.splitBlock({ keepMarks: true }) // Split and keep marks
						.insertContent(nextNumberPrefix) // Insert prefix (marks should be inherited)
						.run();
				}

				return false; // Fallback to default Enter behavior
			},

			Backspace: () => {
				const { state } = this.editor;
				const { selection } = state;
				const { $from, empty } = selection;

				if (!empty) return false; // Only handle cursor selections

				const node = $from.node($from.depth);
				if (!node || node.type.name !== "paragraph") return false;

				const textBeforeCursor = node.textContent.substring(
					0,
					$from.parentOffset,
				);
				const bulletPrefix = "• ";
				const numberRegex = /^(\d+)\.\s/;
				const numberMatch = textBeforeCursor.match(numberRegex);

				// Case 1: Backspace right after the prefix -> Untoggle list
				if (textBeforeCursor === bulletPrefix) {
					return this.editor.commands.toggleBulletList();
				}
				if (numberMatch && textBeforeCursor === numberMatch[0]) {
					return this.editor.commands.toggleNumberedList();
				}

				// Case 2: Backspace at the very start of an indented line -> Outdent
				if ($from.parentOffset === 0 && (node.attrs.indent || 0) > 0) {
					return this.editor.commands.decreaseIndent();
				}

				return false; // Fallback to default Backspace behavior
			},
		};
	},
});

// Declare the commands for TypeScript
declare module "@tiptap/core" {
	interface Commands<ReturnType> {
		// Define commands provided by ParagraphCommands extension
		paragraphCommands: {
			increaseIndent: () => ReturnType;
			decreaseIndent: () => ReturnType;
			toggleBulletList: () => ReturnType;
			toggleNumberedList: () => ReturnType;
		};
	}
}

export function RichTextEditor({
	content,
	onChange,
	setActiveEditor,
	verticalAlign: initialVerticalAlign,
	setIsTextEditorFocused,
	onBlurCallback,
	defaultFontSize = "8pt",
}: RichTextEditorProps) {
	const editorRef = useRef<HTMLDivElement>(null);

	// Helper function to detect vertical alignment in content
	const detectVerticalAlign = useCallback(
		(htmlContent: string): string | null => {
			// Check for both style attribute and class-based alignment
			const styleMatch = htmlContent.match(
				/vertical-align:\s*(top|middle|bottom)/,
			);
			const classMatch = htmlContent.match(
				/vertical-align-(top|middle|bottom)/,
			);
			return styleMatch ? styleMatch[1] : classMatch ? classMatch[1] : null;
		},
		[],
	);

	// Helper function to apply vertical alignment to editor
	const applyVerticalAlignToEditor = useCallback(
		(verticalAlign: string | null) => {
			if (!editorRef.current) return;

			const editorElement = editorRef.current.querySelector(".ProseMirror");
			if (!editorElement) return;

			if (verticalAlign) {
				// Set the style attribute for vertical alignment
				editorElement.setAttribute(
					"style",
					`vertical-align: ${verticalAlign}; display: flex; flex-direction: column; height: 100%; justify-content: ${
						verticalAlign === "top"
							? "flex-start"
							: verticalAlign === "middle"
								? "center"
								: verticalAlign === "bottom"
									? "flex-end"
									: "flex-start"
					}`,
				);

				// Also add the class for CSS targeting
				editorElement.classList.remove(
					"vertical-align-top",
					"vertical-align-middle",
					"vertical-align-bottom",
				);
				editorElement.classList.add(`vertical-align-${verticalAlign}`);
			} else {
				// Remove alignment styles but keep other styles
				const currentStyle = editorElement.getAttribute("style") || "";
				const newStyle = currentStyle
					.replace(/vertical-align:\s*[^;]+;?/, "")
					.replace(/justify-content:\s*[^;]+;?/, "")
					.trim();

				if (newStyle) {
					editorElement.setAttribute("style", newStyle);
				} else {
					editorElement.setAttribute(
						"style",
						"display: flex; flex-direction: column; height: 100%;",
					);
				}

				// Remove alignment classes
				editorElement.classList.remove(
					"vertical-align-top",
					"vertical-align-middle",
					"vertical-align-bottom",
				);
			}
		},
		[],
	);

	const editor = useEditor({
		extensions: [
			StarterKit.configure({
				paragraph: false, // Use CustomParagraph instead
				orderedList: false,
				bulletList: false,
				listItem: false,
				history: {},
			}),
			CustomParagraph, // Handles attributes
			ParagraphCommands, // Handles commands and shortcuts
			Underline,
			TextAlign.configure({
				types: ["paragraph", "heading"],
				alignments: ["left", "center", "right", "justify"],
			}),
			TextStyle,
			CustomTextStyle,
			Color,
			Superscript,
			Subscript,
		],
		content: content || "<p></p>",
		editable: true,
		onFocus: ({ editor }) => {
			// Existing focus state handling
			if (setIsTextEditorFocused) {
				setIsTextEditorFocused(true);
			}

			// Ensure default text styles are present so the very first character the user types
			// inherits the desired formatting (this is especially important for editors that are
			// rendered lazily, e.g. inside table cells, where the initial `onCreate` focus might
			// not have been applied successfully).
			const currentFontSize = editor.getAttributes("textStyle")?.fontSize;
			const currentColor = editor.getAttributes("textStyle")?.color;

			// Apply default font size if not present in stored marks
			if (!currentFontSize) {
				editor
					.chain()
					.setMark("textStyle", { fontSize: defaultFontSize })
					.run();
			}

			// Apply default color if not present in stored marks
			if (!currentColor) {
				editor.chain().setColor("rgb(7, 30, 127)").run();
			}
		},
		onBlur: () => {
			if (setIsTextEditorFocused) {
				setIsTextEditorFocused(false);
			}

			// Inform parent components (like TableEditor) that the editor has lost focus
			if (onBlurCallback) {
				onBlurCallback();
			}
		},
		onCreate: ({ editor }) => {
			// Set default font size of 8pt if no font size is specified in the content
			const currentContent = content || "<p></p>";
			const hasFontSize = currentContent.includes("font-size:");
			const hasFontColor = currentContent.includes("color:");

			if (!hasFontSize) {
				// Apply default font size of 8pt to all content
				editor
					.chain()
					.focus()
					.selectAll()
					.setMark("textStyle", { fontSize: defaultFontSize })
					.run();
				// Clear selection after applying
				editor.commands.setTextSelection(0);
				// Ensure the stored marks include the default font size so newly typed text inherits it
				editor
					.chain()
					.setMark("textStyle", { fontSize: defaultFontSize })
					.run();
			}

			if (!hasFontColor) {
				// Apply default font color to all content
				editor.chain().focus().selectAll().setColor("rgb(7, 30, 127)").run();
				// Clear selection after applying (keep caret at start)
				editor.commands.setTextSelection(0);
				// Ensure newly typed text inherits the default color
				editor.chain().setColor("rgb(7, 30, 127)").run();
			}

			// First check for the initialVerticalAlign prop
			if (initialVerticalAlign) {
				// Apply vertical alignment to all content
				editor
					.chain()
					.focus()
					.selectAll()
					.setMark("textStyle", { verticalAlign: initialVerticalAlign })
					.run();
				// Clear selection after applying
				editor.commands.setTextSelection(0);
				// Ensure the stored marks include the vertical alignment so newly typed text inherits it
				editor
					.chain()
					.setMark("textStyle", { verticalAlign: initialVerticalAlign })
					.run();
				// Apply to the editor element
				applyVerticalAlignToEditor(initialVerticalAlign);
			} else {
				// Fallback: check for vertical alignment in the content
				const contentVerticalAlign = detectVerticalAlign(content || "");
				applyVerticalAlignToEditor(contentVerticalAlign);
			}
		},

		onUpdate: ({ editor }) => {
			// Get the HTML content
			const html = editor.getHTML();

			// Check for vertical alignment in the editor
			// First try to get it from the editor state
			let verticalAlign = editor.getAttributes("textStyle")?.verticalAlign;

			// If not found in the editor state, try to detect it from the HTML
			if (!verticalAlign) {
				verticalAlign = detectVerticalAlign(html);
			}

			// Apply the vertical alignment to the editor
			applyVerticalAlignToEditor(verticalAlign);

			onChange(html);
		},
		parseOptions: {
			preserveWhitespace: "full",
		},
		onSelectionUpdate: ({ editor }) => {
			const { fontSize, color, lineHeight } =
				editor.getAttributes("textStyle") ?? {};

			if (!fontSize) {
				editor
					.chain()
					.setMark("textStyle", { fontSize: defaultFontSize })
					.run();
			}

			if (!color) {
				editor.chain().setColor("rgb(7, 30, 127)").run();
			}

			if (!lineHeight) {
				editor.chain().setMark("textStyle", { lineHeight: "1.2" }).run();
			}
		},
	});

	// Ensure the activeEditor is properly set and cleaned up
	useEffect(() => {
		if (editor && setActiveEditor) {
			// Set the active editor when it's available
			setActiveEditor(editor);

			// Clean up when component unmounts
			return () => {
				setActiveEditor(null);
			};
		}
	}, [editor, setActiveEditor]);

	// Only update content from props when it actually changes
	useEffect(() => {
		if (editor && content !== editor.getHTML()) {
			// Ensure content is never completely empty
			const newContent = content || "<p></p>";

			// Set the content in the editor
			editor.commands.setContent(newContent, false);

			// Set default font size of 8pt if no font size is specified in the content
			const hasFontSize = newContent.includes("font-size:");
			const hasFontColor = newContent.includes("color:");
			if (!hasFontSize) {
				// Apply default font size of 8pt
				editor
					.chain()
					.focus()
					.setMark("textStyle", { fontSize: defaultFontSize })
					.run();
			}

			if (!hasFontColor) {
				// Apply default font color
				editor.chain().focus().setColor("rgb(7, 30, 127)").run();
			}

			// Apply vertical alignment based on props or content
			if (initialVerticalAlign) {
				// Set the vertical alignment mark and ensure newly typed text inherits it
				editor
					.chain()
					.setMark("textStyle", { verticalAlign: initialVerticalAlign })
					.run();
				// Apply to the editor element
				applyVerticalAlignToEditor(initialVerticalAlign);
			} else {
				// Fallback: check for vertical alignment in the content
				const contentVerticalAlign = detectVerticalAlign(newContent);
				applyVerticalAlignToEditor(contentVerticalAlign);
			}
		}
	}, [
		content,
		editor,
		initialVerticalAlign,
		applyVerticalAlignToEditor,
		detectVerticalAlign,
		defaultFontSize,
	]);

	// Apply vertical alignment when it changes
	useEffect(() => {
		if (editor && initialVerticalAlign) {
			// Set the vertical alignment mark and ensure newly typed text inherits it
			editor
				.chain()
				.setMark("textStyle", { verticalAlign: initialVerticalAlign })
				.run();
			// Apply to the editor element
			applyVerticalAlignToEditor(initialVerticalAlign);
		}
	}, [
		initialVerticalAlign,
		editor, // Apply to the editor element
		applyVerticalAlignToEditor,
	]);

	return (
		<div
			className="w-full h-full"
			ref={editorRef}
			style={{ padding: 0, margin: 0 }}
		>
			<EditorContent
				editor={editor}
				className="max-w-none w-full h-full focus:outline-none overflow-hidden"
				style={{
					overflowWrap: "break-word",
					wordWrap: "break-word",
					wordBreak: "break-word",
					whiteSpace: "pre-wrap",
					maxWidth: "100%",
					display: "flex",
					flexDirection: "column",
					height: "100%",
					padding: 0,
					margin: 0,
				}}
			/>
		</div>
	);
}

export type { Editor } from "@tiptap/react";
