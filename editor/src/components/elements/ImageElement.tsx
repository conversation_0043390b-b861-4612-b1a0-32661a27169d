import type { Element } from "../../types/element";
import { getImageStyles } from "../../utils/elementStyles";

interface ImageElementProps {
	element: Element;
	imageObjectURL: string | null;
	previewMode: boolean;
}

export function ImageElement({
	element,
	imageObjectURL,
	previewMode,
}: ImageElementProps) {
	const imageStyles = getImageStyles(element);

	return (
		<div
			className="w-full h-full flex items-center justify-center bg-gray-50 select-none"
			style={imageStyles}
		>
			{imageObjectURL ? (
				<img
					src={imageObjectURL}
					alt=""
					className="w-full h-full object-contain select-none pointer-events-none"
					draggable={false}
				/>
			) : !previewMode && element.content ? (
				<img
					src={element.content}
					alt=""
					className="w-full h-full object-contain select-none pointer-events-none"
					draggable={false}
				/>
			) : (
				<div className="text-gray-400 text-sm">Kein Bild</div>
			)}
		</div>
	);
}
