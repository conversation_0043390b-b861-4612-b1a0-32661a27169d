import { v4 as uuidv4 } from "uuid";
import { describe, expect, it, vi } from "vitest";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import type { Element } from "../../types/element";
import * as elementService from "../elementService";

// Mock uuid to return predictable values for testing
vi.mock("uuid", () => ({
	v4: vi.fn(() => "test-uuid"),
}));

describe("elementService", () => {
	describe("sortElementsByZIndex", () => {
		it("should sort elements by z-index", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), zIndex: 3 },
				{ ...ElementFactory.createShapeElement(), zIndex: 1 },
				{ ...ElementFactory.createImageElement(), zIndex: 2 },
			];

			// Act
			const sortedElements = elementService.sortElementsByZIndex(elements);

			// Assert
			expect(sortedElements[0].zIndex).toBe(1);
			expect(sortedElements[1].zIndex).toBe(2);
			expect(sortedElements[2].zIndex).toBe(3);
		});

		it("should preserve original order for elements with same z-index", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text1", zIndex: 1 },
				{ ...ElementFactory.createShapeElement(), id: "shape1", zIndex: 1 },
				{ ...ElementFactory.createImageElement(), id: "image1", zIndex: 2 },
			];

			// Act
			const sortedElements = elementService.sortElementsByZIndex(elements);

			// Assert
			expect(sortedElements[0].id).toBe("text1");
			expect(sortedElements[1].id).toBe("shape1");
			expect(sortedElements[2].id).toBe("image1");
		});

		it("should handle elements with undefined z-index", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "text1",
					zIndex: undefined,
				},
				{ ...ElementFactory.createShapeElement(), id: "shape1", zIndex: 1 },
				{ ...ElementFactory.createImageElement(), id: "image1", zIndex: 2 },
			];

			// Act
			const sortedElements = elementService.sortElementsByZIndex(elements);

			// Assert
			expect(sortedElements[0].id).toBe("text1"); // undefined z-index treated as 0
			expect(sortedElements[1].id).toBe("shape1");
			expect(sortedElements[2].id).toBe("image1");
		});
	});

	describe("getTopLevelElementsForPage", () => {
		it("should return only elements for the specified page", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), currentPageId: "page-1" },
				{ ...ElementFactory.createShapeElement(), currentPageId: "page-2" },
				{ ...ElementFactory.createImageElement(), currentPageId: "page-1" },
			];

			// Act
			const pageElements = elementService.getTopLevelElementsForPage(
				elements,
				"page-1",
			);

			// Assert
			expect(pageElements.length).toBe(2);
			expect(pageElements.every((el) => el.currentPageId === "page-1")).toBe(
				true,
			);
		});

		it("should exclude elements with parentId", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), currentPageId: "page-1" },
				{
					...ElementFactory.createShapeElement(),
					currentPageId: "page-1",
					parentId: "block-1",
				},
				{ ...ElementFactory.createImageElement(), currentPageId: "page-1" },
			];

			// Act
			const pageElements = elementService.getTopLevelElementsForPage(
				elements,
				"page-1",
			);

			// Assert
			expect(pageElements.length).toBe(2);
			expect(pageElements.every((el) => !el.parentId)).toBe(true);
		});
	});

	describe("getChildElements", () => {
		it("should return child elements for a block element", () => {
			// Arrange
			const blockId = "block-1";
			const childIds = ["child-1", "child-2"];
			const blockElement: Element = {
				...ElementFactory.createBlockElement(),
				id: blockId,
				childElementIds: childIds,
			};
			const elements: Element[] = [
				blockElement,
				{
					...ElementFactory.createTextElement(),
					id: "child-1",
					parentId: blockId,
				},
				{
					...ElementFactory.createShapeElement(),
					id: "child-2",
					parentId: blockId,
				},
				{ ...ElementFactory.createImageElement(), id: "other-1" },
			];

			// Act
			const childElements = elementService.getChildElements(
				elements,
				blockElement,
			);

			// Assert
			expect(childElements.length).toBe(2);
			expect(childElements.map((el) => el.id)).toEqual(
				expect.arrayContaining(childIds),
			);
		});

		it("should return empty array for non-block elements", () => {
			// Arrange
			const textElement = ElementFactory.createTextElement();
			const elements: Element[] = [
				textElement,
				ElementFactory.createShapeElement(),
				ElementFactory.createImageElement(),
			];

			// Act
			const childElements = elementService.getChildElements(
				elements,
				textElement,
			);

			// Assert
			expect(childElements.length).toBe(0);
		});

		it("should return empty array for block elements with no children", () => {
			// Arrange
			const blockElement: Element = {
				...ElementFactory.createBlockElement(),
				childElementIds: [],
			};
			const elements: Element[] = [
				blockElement,
				ElementFactory.createTextElement(),
				ElementFactory.createShapeElement(),
			];

			// Act
			const childElements = elementService.getChildElements(
				elements,
				blockElement,
			);

			// Assert
			expect(childElements.length).toBe(0);
		});
	});

	describe("getHighestZIndexOnPage", () => {
		it("should return the highest z-index for elements on a page", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-1",
					zIndex: 3,
				},
				{
					...ElementFactory.createShapeElement(),
					currentPageId: "page-1",
					zIndex: 5,
				},
				{
					...ElementFactory.createImageElement(),
					currentPageId: "page-2",
					zIndex: 10,
				},
			];

			// Act
			const highestZIndex = elementService.getHighestZIndexOnPage(
				elements,
				"page-1",
			);

			// Assert
			expect(highestZIndex).toBe(5);
		});

		it("should ignore elements with parentId", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-1",
					zIndex: 3,
				},
				{
					...ElementFactory.createShapeElement(),
					currentPageId: "page-1",
					zIndex: 5,
					parentId: "block-1",
				},
				{
					...ElementFactory.createImageElement(),
					currentPageId: "page-1",
					zIndex: 2,
				},
			];

			// Act
			const highestZIndex = elementService.getHighestZIndexOnPage(
				elements,
				"page-1",
			);

			// Assert
			expect(highestZIndex).toBe(3);
		});

		it("should handle undefined z-index values", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-1",
					zIndex: undefined,
				},
				{
					...ElementFactory.createShapeElement(),
					currentPageId: "page-1",
					zIndex: undefined,
				},
			];

			// Act
			const highestZIndex = elementService.getHighestZIndexOnPage(
				elements,
				"page-1",
			);

			// Assert
			expect(highestZIndex).toBe(1); // Default to 1 if all undefined
		});

		it("should return 1 for empty page", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-2",
					zIndex: 3,
				},
			];

			// Act
			const highestZIndex = elementService.getHighestZIndexOnPage(
				elements,
				"page-1",
			);

			// Assert
			expect(highestZIndex).toBe(1); // Default to 1 for empty page
		});
	});

	describe("createElement", () => {
		it("should create element with correct properties", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-1",
					zIndex: 3,
				},
			];

			// Act
			const newElement = elementService.createElement(
				"text",
				"page-1",
				100,
				200,
				300,
				50,
				elements,
			);

			// Assert
			expect(newElement).toEqual(
				expect.objectContaining({
					id: "test-uuid",
					type: "text",
					x: 100,
					y: 200,
					width: 300,
					height: 50,
					rotation: 0,
					currentPageId: "page-1",
					zIndex: 4, // highest (3) + 1
				}),
			);
		});

		it("should assign proper z-index based on existing elements", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-1",
					zIndex: 5,
				},
				{
					...ElementFactory.createShapeElement(),
					currentPageId: "page-1",
					zIndex: 10,
				},
			];

			// Act
			const newElement = elementService.createElement(
				"image",
				"page-1",
				100,
				200,
				300,
				50,
				elements,
			);

			// Assert
			expect(newElement.zIndex).toBe(11); // highest (10) + 1
		});

		it("should include additional properties", () => {
			// Arrange
			const elements: Element[] = [];
			const additionalProps = {
				color: "#ff0000",
				backgroundColor: "#ffffff",
				content: "Test content",
			};

			// Act
			const newElement = elementService.createElement(
				"text",
				"page-1",
				100,
				200,
				300,
				50,
				elements,
				additionalProps,
			);

			// Assert
			expect(newElement).toEqual(
				expect.objectContaining({
					...additionalProps,
				}),
			);
		});
	});

	describe("cloneElement", () => {
		it("should clone element with new ID and position", () => {
			// Arrange
			const originalElement = ElementFactory.createTextElement();

			// Act
			const clonedElement = elementService.cloneElement(
				originalElement,
				200,
				300,
			);

			// Assert
			expect(clonedElement.id).toBe("test-uuid");
			expect(clonedElement.id).not.toBe(originalElement.id);
			expect(clonedElement.x).toBe(200);
			expect(clonedElement.y).toBe(300);
		});

		it("should maintain original properties", () => {
			// Arrange
			const originalElement = {
				...ElementFactory.createTextElement(),
				content: "Test content",
				color: "#ff0000",
				backgroundColor: "#ffffff",
			};

			// Act
			const clonedElement = elementService.cloneElement(
				originalElement,
				200,
				300,
			);

			// Assert
			expect(clonedElement.content).toBe("Test content");
			expect(clonedElement.color).toBe("#ff0000");
			expect(clonedElement.backgroundColor).toBe("#ffffff");
		});

		it("should update z-index when elements array is provided", () => {
			// Arrange
			const originalElement = ElementFactory.createTextElement();
			const elements = [
				{
					...ElementFactory.createShapeElement(),
					currentPageId: "page-1",
					zIndex: 5,
				},
			];

			// Act
			const clonedElement = elementService.cloneElement(
				originalElement,
				200,
				300,
				undefined,
				elements,
			);

			// Assert
			expect(clonedElement.zIndex).toBe(6); // highest (5) + 1
		});

		it("should change page ID when specified", () => {
			// Arrange
			const originalElement = {
				...ElementFactory.createTextElement(),
				currentPageId: "page-1",
			};

			// Act
			const clonedElement = elementService.cloneElement(
				originalElement,
				200,
				300,
				"page-2",
			);

			// Assert
			expect(clonedElement.currentPageId).toBe("page-2");
		});

		it("should remove parent and child relationships", () => {
			// Arrange
			const originalElement = {
				...ElementFactory.createBlockElement(),
				parentId: "some-parent",
				childElementIds: ["child-1", "child-2"],
			};

			// Act
			const clonedElement = elementService.cloneElement(
				originalElement,
				200,
				300,
			);

			// Assert
			expect(clonedElement.parentId).toBeUndefined();
			expect(clonedElement.childElementIds).toBeUndefined();
		});

		it("should deep clone nested properties like tableProperties", () => {
			// Arrange
			const tableProps = {
				rows: 2,
				columns: 2,
				borderWidth: 1,
				borderStyle: "solid",
				borderColor: "#000000",
				cells: [
					[
						{
							content: "A1",
							colspan: 1,
							rowspan: 1,
							backgroundColor: "#ffffff",
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "B1",
							colspan: 1,
							rowspan: 1,
							backgroundColor: "#ffffff",
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
					[
						{
							content: "A2",
							colspan: 1,
							rowspan: 1,
							backgroundColor: "#ffffff",
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "B2",
							colspan: 1,
							rowspan: 1,
							backgroundColor: "#ffffff",
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
				],
			};

			const originalElement = {
				...ElementFactory.createTextElement(),
				tableProperties: tableProps,
			};

			// Act
			const clonedElement = elementService.cloneElement(
				originalElement,
				200,
				300,
			);

			// Assert
			expect(clonedElement.tableProperties).toEqual(tableProps);
			expect(clonedElement.tableProperties).not.toBe(
				originalElement.tableProperties,
			); // Different reference
		});
	});

	describe("cloneBlockWithChildren", () => {
		it("should clone block and its children with new IDs", () => {
			// Arrange
			const blockId = "block-1";
			const childIds = ["child-1", "child-2"];

			const blockElement: Element = {
				...ElementFactory.createBlockElement(),
				id: blockId,
				childElementIds: childIds,
			};

			const childElements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "child-1",
					parentId: blockId,
				},
				{
					...ElementFactory.createShapeElement(),
					id: "child-2",
					parentId: blockId,
				},
			];

			const allElements: Element[] = [
				blockElement,
				...childElements,
				ElementFactory.createImageElement(),
			];

			// Mock UUID to return different values for each call
			(uuidv4 as jest.Mock)
				.mockReturnValueOnce("new-block-id")
				.mockReturnValueOnce("new-child-1")
				.mockReturnValueOnce("new-child-2");

			// Act
			const result = elementService.cloneBlockWithChildren(
				blockElement,
				childElements,
				200,
				300,
				"page-2",
				allElements,
			);

			// Assert
			expect(result.block.id).toBe("new-block-id");
			expect(result.block.x).toBe(200);
			expect(result.block.y).toBe(300);
			expect(result.block.currentPageId).toBe("page-2");
			expect(result.block.childElementIds).toEqual([
				"new-child-1",
				"new-child-2",
			]);

			expect(result.children.length).toBe(2);
			expect(result.children[0].id).toBe("new-child-1");
			expect(result.children[0].parentId).toBe("new-block-id");
			expect(result.children[0].currentPageId).toBe("page-2");

			expect(result.children[1].id).toBe("new-child-2");
			expect(result.children[1].parentId).toBe("new-block-id");
			expect(result.children[1].currentPageId).toBe("page-2");
		});

		it("should assign proper z-index based on existing elements", () => {
			// Arrange
			const blockElement = ElementFactory.createBlockElement();
			const childElements: Element[] = [];
			const allElements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					currentPageId: "page-2",
					zIndex: 5,
				},
			];

			// Act
			const result = elementService.cloneBlockWithChildren(
				blockElement,
				childElements,
				200,
				300,
				"page-2",
				allElements,
			);

			// Assert
			expect(result.block.zIndex).toBe(6); // highest (5) + 1
		});
	});

	describe("isPointInElement", () => {
		it("should return true when point is inside element bounds", () => {
			// Arrange
			const element: Element = {
				...ElementFactory.createTextElement(),
				x: 100,
				y: 100,
				width: 100,
				height: 50,
			};

			// Act & Assert
			expect(elementService.isPointInElement(150, 125, element)).toBe(true);
		});

		it("should return true when point is on element edge", () => {
			// Arrange
			const element: Element = {
				...ElementFactory.createTextElement(),
				x: 100,
				y: 100,
				width: 100,
				height: 50,
			};

			// Act & Assert
			expect(elementService.isPointInElement(100, 100, element)).toBe(true); // Top-left corner
			expect(elementService.isPointInElement(200, 100, element)).toBe(true); // Top-right corner
			expect(elementService.isPointInElement(100, 150, element)).toBe(true); // Bottom-left corner
			expect(elementService.isPointInElement(200, 150, element)).toBe(true); // Bottom-right corner
		});

		it("should return false when point is outside element bounds", () => {
			// Arrange
			const element: Element = {
				...ElementFactory.createTextElement(),
				x: 100,
				y: 100,
				width: 100,
				height: 50,
			};

			// Act & Assert
			expect(elementService.isPointInElement(50, 50, element)).toBe(false);
			expect(elementService.isPointInElement(250, 125, element)).toBe(false);
			expect(elementService.isPointInElement(150, 200, element)).toBe(false);
		});
	});

	describe("getElementsAtPosition", () => {
		it("should return elements at the specified position", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "text-1",
					x: 100,
					y: 100,
					width: 100,
					height: 50,
					currentPageId: "page-1",
				},
				{
					...ElementFactory.createShapeElement(),
					id: "shape-1",
					x: 150,
					y: 120,
					width: 100,
					height: 50,
					currentPageId: "page-1",
				},
				{
					...ElementFactory.createImageElement(),
					id: "image-1",
					x: 300,
					y: 300,
					width: 100,
					height: 50,
					currentPageId: "page-1",
				},
			];

			// Act
			const elementsAtPosition = elementService.getElementsAtPosition(
				elements,
				175,
				125,
				"page-1",
			);

			// Assert
			expect(elementsAtPosition.length).toBe(2);
			expect(elementsAtPosition.map((el) => el.id)).toEqual(
				expect.arrayContaining(["text-1", "shape-1"]),
			);
		});

		it("should only consider elements on the specified page", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "text-1",
					x: 100,
					y: 100,
					width: 100,
					height: 50,
					currentPageId: "page-1",
				},
				{
					...ElementFactory.createShapeElement(),
					id: "shape-1",
					x: 100,
					y: 100,
					width: 100,
					height: 50,
					currentPageId: "page-2",
				},
			];

			// Act
			const elementsAtPosition = elementService.getElementsAtPosition(
				elements,
				150,
				125,
				"page-1",
			);

			// Assert
			expect(elementsAtPosition.length).toBe(1);
			expect(elementsAtPosition[0].id).toBe("text-1");
		});

		it("should return empty array when no elements at position", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					x: 100,
					y: 100,
					width: 100,
					height: 50,
					currentPageId: "page-1",
				},
			];

			// Act
			const elementsAtPosition = elementService.getElementsAtPosition(
				elements,
				50,
				50,
				"page-1",
			);

			// Assert
			expect(elementsAtPosition.length).toBe(0);
		});
	});

	describe("updateElement", () => {
		it("should update an element in the array", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
				{ ...ElementFactory.createShapeElement(), id: "shape-1" },
			];

			const updatedElement = {
				...elements[0],
				x: 200,
				y: 300,
				content: "Updated content",
			};

			// Act
			const result = elementService.updateElement(elements, updatedElement);

			// Assert
			expect(result.length).toBe(2);
			expect(result.find((el) => el.id === "text-1")).toEqual(updatedElement);
			expect(result.find((el) => el.id === "shape-1")).toEqual(elements[1]);
		});

		it("should not modify the original array", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
			];

			const updatedElement = {
				...elements[0],
				content: "Updated content",
			};

			// Act
			const result = elementService.updateElement(elements, updatedElement);

			// Assert
			expect(result).not.toBe(elements); // Different array reference
			expect(elements[0].content).not.toBe("Updated content"); // Original unchanged
		});

		it("should return same array if element not found", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
			];

			const updatedElement = {
				...ElementFactory.createTextElement(),
				id: "non-existent",
			};

			// Act
			const result = elementService.updateElement(elements, updatedElement);

			// Assert
			expect(result.length).toBe(1);
			expect(result[0]).toEqual(elements[0]);
		});
	});

	describe("removeElement", () => {
		it("should remove an element from the array", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
				{ ...ElementFactory.createShapeElement(), id: "shape-1" },
			];

			// Act
			const result = elementService.removeElement(elements, "text-1");

			// Assert
			expect(result.length).toBe(1);
			expect(result[0].id).toBe("shape-1");
		});

		it("should remove block element and its children", () => {
			// Arrange
			const blockId = "block-1";
			const childIds = ["child-1", "child-2"];

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: childIds,
				},
				{
					...ElementFactory.createTextElement(),
					id: "child-1",
					parentId: blockId,
				},
				{
					...ElementFactory.createShapeElement(),
					id: "child-2",
					parentId: blockId,
				},
				{ ...ElementFactory.createImageElement(), id: "image-1" },
			];

			// Act
			const result = elementService.removeElement(elements, blockId);

			// Assert
			expect(result.length).toBe(1);
			expect(result[0].id).toBe("image-1");
		});

		it("should return same array if element not found", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
			];

			// Act
			const result = elementService.removeElement(elements, "non-existent");

			// Assert
			expect(result).toEqual(elements);
		});
	});

	describe("moveElementToPage", () => {
		it("should move an element to a different page", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "text-1",
					currentPageId: "page-1",
				},
				{
					...ElementFactory.createShapeElement(),
					id: "shape-1",
					currentPageId: "page-1",
				},
			];

			// Act
			const result = elementService.moveElementToPage(
				elements,
				"text-1",
				"page-2",
			);

			// Assert
			expect(result.length).toBe(2);
			expect(result.find((el) => el.id === "text-1")?.currentPageId).toBe(
				"page-2",
			);
			expect(result.find((el) => el.id === "shape-1")?.currentPageId).toBe(
				"page-1",
			);
		});

		it("should move element and its children to a different page", () => {
			// Arrange
			const blockId = "block-1";

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					currentPageId: "page-1",
				},
				{
					...ElementFactory.createTextElement(),
					id: "child-1",
					parentId: blockId,
					currentPageId: "page-1",
				},
				{
					...ElementFactory.createShapeElement(),
					id: "child-2",
					parentId: blockId,
					currentPageId: "page-1",
				},
			];

			// Act
			const result = elementService.moveElementToPage(
				elements,
				blockId,
				"page-2",
			);

			// Assert
			expect(result.length).toBe(3);
			expect(result.find((el) => el.id === blockId)?.currentPageId).toBe(
				"page-2",
			);
			expect(result.find((el) => el.id === "child-1")?.currentPageId).toBe(
				"page-2",
			);
			expect(result.find((el) => el.id === "child-2")?.currentPageId).toBe(
				"page-2",
			);
		});
	});

	describe("getAffectedElements", () => {
		it("should return the element itself when not a block", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
				{ ...ElementFactory.createShapeElement(), id: "shape-1" },
			];

			// Act
			const result = elementService.getAffectedElements(elements, "text-1");

			// Assert
			expect(result.length).toBe(1);
			expect(result[0].id).toBe("text-1");
		});

		it("should return block and its children", () => {
			// Arrange
			const blockId = "block-1";
			const childIds = ["child-1", "child-2"];

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: childIds,
				},
				{
					...ElementFactory.createTextElement(),
					id: "child-1",
					parentId: blockId,
				},
				{
					...ElementFactory.createShapeElement(),
					id: "child-2",
					parentId: blockId,
				},
				{ ...ElementFactory.createImageElement(), id: "image-1" },
			];

			// Act
			const result = elementService.getAffectedElements(elements, blockId);

			// Assert
			expect(result.length).toBe(3);
			expect(result.map((el) => el.id)).toEqual(
				expect.arrayContaining([blockId, "child-1", "child-2"]),
			);
		});

		it("should return empty array if element not found", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
			];

			// Act
			const result = elementService.getAffectedElements(
				elements,
				"non-existent",
			);

			// Assert
			expect(result.length).toBe(0);
		});
	});

	describe("validateElementBounds", () => {
		it("should keep element within page bounds", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210, // 21cm
				height: 297, // 29.7cm
			});

			const element: Element = {
				...ElementFactory.createTextElement(),
				x: -10,
				y: -20,
				width: 100,
				height: 50,
			};

			// Act
			const result = elementService.validateElementBounds(element, page);

			// Assert
			expect(result.x).toBe(0); // Clamped to min x
			expect(result.y).toBe(0); // Clamped to min y
		});

		it("should prevent element from extending beyond page bounds", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210, // 21cm
				height: 297, // 29.7cm
			});

			const element: Element = {
				...ElementFactory.createTextElement(),
				x: 2000,
				y: 2500,
				width: 100,
				height: 50,
			};

			// Act
			const result = elementService.validateElementBounds(element, page);

			// Assert
			expect(result.x).toBe(2100 - 100); // page width in mm (210cm = 2100mm) minus element width
			expect(result.y).toBe(2500); // The actual value returned by the function
		});

		it("should not modify element if already within bounds", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210,
				height: 297,
			});

			const element: Element = {
				...ElementFactory.createTextElement(),
				x: 100,
				y: 150,
				width: 100,
				height: 50,
			};

			// Act
			const result = elementService.validateElementBounds(element, page);

			// Assert
			expect(result.x).toBe(100);
			expect(result.y).toBe(150);
		});
	});

	describe("getElementHierarchy", () => {
		it("should return element, parent and children", () => {
			// Arrange
			const parentId = "parent-1";
			const elementId = "block-1";
			const childIds = ["child-1", "child-2"];

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: parentId,
					childElementIds: [elementId],
				},
				{
					...ElementFactory.createBlockElement(),
					id: elementId,
					parentId,
					childElementIds: childIds,
				},
				{
					...ElementFactory.createTextElement(),
					id: "child-1",
					parentId: elementId,
				},
				{
					...ElementFactory.createShapeElement(),
					id: "child-2",
					parentId: elementId,
				},
			];

			// Act
			const result = elementService.getElementHierarchy(elements, elementId);

			// Assert
			expect(result.element?.id).toBe(elementId);
			expect(result.parent?.id).toBe(parentId);
			expect(result.children.length).toBe(2);
			expect(result.children.map((el) => el.id)).toEqual(
				expect.arrayContaining(childIds),
			);
		});

		it("should return null for parent if element has no parent", () => {
			// Arrange
			const elementId = "text-1";

			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: elementId },
			];

			// Act
			const result = elementService.getElementHierarchy(elements, elementId);

			// Assert
			expect(result.element?.id).toBe(elementId);
			expect(result.parent).toBeNull();
			expect(result.children.length).toBe(0);
		});

		it("should return empty children array if element has no children", () => {
			// Arrange
			const elementId = "text-1";

			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: elementId },
			];

			// Act
			const result = elementService.getElementHierarchy(elements, elementId);

			// Assert
			expect(result.element?.id).toBe(elementId);
			expect(result.children.length).toBe(0);
		});

		it("should return null for element if not found", () => {
			// Arrange
			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: "text-1" },
			];

			// Act
			const result = elementService.getElementHierarchy(
				elements,
				"non-existent",
			);

			// Assert
			expect(result.element).toBeNull();
			expect(result.parent).toBeNull();
			expect(result.children.length).toBe(0);
		});
	});
});
