import { useEffect, useRef, useState } from "react";
import type { Element } from "@/types/element";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import { Input } from "./input";
import { Label } from "./label";
import { Switch } from "./switch";

interface CornerRadiusSettingsProps {
	element: Element | null;
	onUpdate: (updatedElement: Element) => void;
}

export function CornerRadiusSettings({
	element,
	onUpdate,
}: CornerRadiusSettingsProps) {
	const [useIndividualCorners, setUseIndividualCorners] = useState(false);
	const [globalRadius, setGlobalRadius] = useState<string>("0");
	const [cornerRadii, setCornerRadii] = useState<string[]>([
		"0",
		"0",
		"0",
		"0",
	]);

	// Use a ref to track if we're in the middle of an update
	const isUpdatingRef = useRef(false);
	// Add a ref to track if the component has been initialized
	const initializedRef = useRef(false);

	// Initialize values from element
	useEffect(() => {
		if (!element?.borderRadius || isUpdatingRef.current) return;

		// Only set the initial state if we haven't initialized yet
		if (!initializedRef.current) {
			// Check if all corners have the same value
			const allSame = element.borderRadius.every(
				(r) => r === element.borderRadius?.[0],
			);

			if (allSame) {
				setGlobalRadius(element.borderRadius[0].toString());
				setUseIndividualCorners(false);
			} else {
				setCornerRadii(element.borderRadius.map((r) => r.toString()));
				setUseIndividualCorners(true);
			}

			initializedRef.current = true;
		} else {
			// If already initialized, just update the values without changing the mode
			if (useIndividualCorners) {
				setCornerRadii(element.borderRadius.map((r) => r.toString()));
			} else {
				// If using global radius, check if all values are the same
				const allSame = element.borderRadius.every(
					(r) => r === element.borderRadius?.[0],
				);
				if (allSame) {
					setGlobalRadius(element.borderRadius[0].toString());
				}
			}
		}
	}, [element, useIndividualCorners]);

	const handleGlobalRadiusChange = (value: string) => {
		if (!element) return;

		setGlobalRadius(value);

		// Convert empty string to 0
		const numValue = value === "" ? 0 : parseFloat(value);
		if (Number.isNaN(numValue) || numValue < 0) return;

		// Update all corners with the same value
		const newBorderRadius = [numValue, numValue, numValue, numValue].map(
			roundToTwoDecimals,
		);

		isUpdatingRef.current = true;
		onUpdate({
			...element,
			borderRadius: newBorderRadius,
		});
		isUpdatingRef.current = false;
	};

	const handleCornerRadiusChange = (index: number, value: string) => {
		if (!element) return;

		// Update the state with the string value
		const newCornerRadii = [...cornerRadii];
		newCornerRadii[index] = value;
		setCornerRadii(newCornerRadii);

		// Convert empty string to 0
		const numValue = value === "" ? 0 : parseFloat(value);
		if (Number.isNaN(numValue) || numValue < 0) return;

		// Create a new array with updated values
		const newBorderRadiusValues = newCornerRadii.map((r, i) => {
			if (i === index) {
				return roundToTwoDecimals(numValue);
			}
			return r === "" ? 0 : roundToTwoDecimals(parseFloat(r));
		});

		isUpdatingRef.current = true;
		onUpdate({
			...element,
			borderRadius: newBorderRadiusValues,
		});
		isUpdatingRef.current = false;
	};

	const handleToggleIndividualCorners = (checked: boolean) => {
		setUseIndividualCorners(checked);

		if (!element) return;

		if (checked) {
			// When switching to individual corners, initialize all with the global value
			const globalValue = globalRadius === "" ? 0 : parseFloat(globalRadius);
			const newCornerRadii = [
				globalValue.toString(),
				globalValue.toString(),
				globalValue.toString(),
				globalValue.toString(),
			];
			setCornerRadii(newCornerRadii);

			isUpdatingRef.current = true;
			onUpdate({
				...element,
				borderRadius: [globalValue, globalValue, globalValue, globalValue],
			});
			isUpdatingRef.current = false;
		} else {
			// When switching to global, use the first corner's value for all
			const firstCornerValue =
				cornerRadii[0] === "" ? 0 : parseFloat(cornerRadii[0]);
			setGlobalRadius(firstCornerValue.toString());

			isUpdatingRef.current = true;
			onUpdate({
				...element,
				borderRadius: [
					firstCornerValue,
					firstCornerValue,
					firstCornerValue,
					firstCornerValue,
				],
			});
			isUpdatingRef.current = false;
		}
	};

	// Determine if the component should be disabled
	const isDisabled =
		!element || (element.type !== "shape" && element.type !== "image");

	return (
		<div className="space-y-4">
			<div>
				<Label className="text-sm font-medium">Eckenradius</Label>
			</div>

			<div className="flex items-center space-x-2 ml-2">
				<Switch
					id="individual-corners"
					checked={useIndividualCorners}
					onCheckedChange={handleToggleIndividualCorners}
					disabled={isDisabled}
				/>
				<Label
					htmlFor="individual-corners"
					className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
				>
					Individuelle Ecken
				</Label>
			</div>

			{!useIndividualCorners ? (
				<div className="space-y-1">
					<Label
						htmlFor="global-radius"
						className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
					>
						Radius (mm)
					</Label>
					<Input
						id="global-radius"
						type="number"
						min="0"
						value={globalRadius}
						onChange={(e) => handleGlobalRadiusChange(e.target.value)}
						className="h-8"
						disabled={isDisabled}
					/>
				</div>
			) : (
				<div className="space-y-3">
					<div className="space-y-1">
						<Label
							htmlFor="top-left"
							className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
						>
							Oben Links (mm)
						</Label>
						<Input
							id="top-left"
							type="number"
							min="0"
							value={cornerRadii[0]}
							onChange={(e) => handleCornerRadiusChange(0, e.target.value)}
							className="h-8"
							disabled={isDisabled}
						/>
					</div>
					<div className="space-y-1">
						<Label
							htmlFor="top-right"
							className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
						>
							Oben Rechts (mm)
						</Label>
						<Input
							id="top-right"
							type="number"
							min="0"
							value={cornerRadii[1]}
							onChange={(e) => handleCornerRadiusChange(1, e.target.value)}
							className="h-8"
							disabled={isDisabled}
						/>
					</div>
					<div className="space-y-1">
						<Label
							htmlFor="bottom-right"
							className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
						>
							Unten Rechts (mm)
						</Label>
						<Input
							id="bottom-right"
							type="number"
							min="0"
							value={cornerRadii[2]}
							onChange={(e) => handleCornerRadiusChange(2, e.target.value)}
							className="h-8"
							disabled={isDisabled}
						/>
					</div>
					<div className="space-y-1">
						<Label
							htmlFor="bottom-left"
							className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
						>
							Unten Links (mm)
						</Label>
						<Input
							id="bottom-left"
							type="number"
							min="0"
							value={cornerRadii[3]}
							onChange={(e) => handleCornerRadiusChange(3, e.target.value)}
							className="h-8"
							disabled={isDisabled}
						/>
					</div>
				</div>
			)}
		</div>
	);
}
