# Architecture Overview

## Application Structure

This is a React-based document editor application built with TypeScript and Vite. The application allows users to create, edit, and export multi-page documents with various elements like text, shapes, images, tables, and blocks.

## Core Technologies

- **React 19.1.0** - UI framework
- **TypeScript** - Type safety and development experience
- **Vite** - Build tool and development server
- **TipTap** - Rich text editor for text elements
- **Radix UI** - Component library for UI primitives
- **Tailwind CSS** - Styling framework
- **Playwright** - End-to-end testing

## Project Structure

```
src/
├── components/          # React components
│   ├── elements/       # Element-specific components
│   ├── sidebar/        # Sidebar components
│   └── ui/            # Reusable UI components
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── services/           # API services
├── contexts/           # React contexts
└── styles/             # CSS files
```

## Key Architectural Patterns

### 1. Custom Hooks for State Management
The application uses a sophisticated hook-based architecture to manage complex state:

- `useHistory` - Undo/redo functionality with state snapshots
- `useHistoryBatch` - Batched operations for performance
- `useDocumentState` - Document-level state management
- `useUIState` - UI-specific state management
- `useElementOperations` - Element CRUD operations
- `usePageOperations` - Page management operations

### 2. Element-Based Architecture
All content is represented as elements with a common interface:

```typescript
interface Element {
  id: string;
  currentPageId: string;
  type: ElementType;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  zIndex?: number;
  // ... additional properties
}
```

### 3. Page-Based Document Structure
Documents consist of pages with specific formats and dimensions:

```typescript
interface PageSettings {
  id: string;
  format: string;
  orientation: "portrait" | "landscape";
  width: number;
  height: number;
  trim_top?: number;
  trim_right?: number;
  trim_bottom?: number;
  trim_left?: number;
  // ... additional properties
}
```

## State Management Strategy

The application uses a centralized state management approach with the following key principles:

1. **Immutable Updates** - All state changes create new objects
2. **History Tracking** - Every significant change is tracked for undo/redo
3. **Batched Operations** - Multiple related changes are grouped together
4. **Optimistic Updates** - UI updates immediately, with rollback on errors

## Component Hierarchy

```
App
└── Page (Main Editor Component)
    ├── TopBar (Document controls)
    ├── Sidebar (Element properties)
    ├── PageInsertToolbar (Add elements)
    ├── ZoomControls
    ├── PageMinimap
    └── EditableElement[] (Rendered elements)
```

## Data Flow

1. **User Interaction** → Event handlers in components
2. **Event Handlers** → Custom hooks (operations)
3. **Operations** → State updates via setEditorState
4. **State Changes** → Component re-renders
5. **History Tracking** → Automatic undo/redo capability

## Performance Optimizations

- **Memoized Components** - Prevent unnecessary re-renders
- **Batched Updates** - Group related state changes
- **Lazy Loading** - Components loaded on demand
- **Optimized Rendering** - Only render visible elements
- **Debounced Operations** - Reduce API calls and state updates