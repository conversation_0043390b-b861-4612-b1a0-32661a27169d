// Constants for unit conversion
// Standard DPI (dots per inch) for screens is 96
// 1 inch = 25.4 mm
// So 1 mm = 96/25.4 pixels = 3.779528 pixels
export const MM_TO_PX_RATIO = 3.779528;
export const PX_TO_MM_RATIO = 1 / MM_TO_PX_RATIO;

export const PX_PER_MM = 3.7795275591; // Standard 96 DPI

/**
 * Convert millimeters to pixels
 * @param mm Value in millimeters
 * @returns Value in pixels
 */
export function mmToPx(mm: number): number {
	return mm * MM_TO_PX_RATIO;
}

/**
 * Convert pixels to millimeters
 * @param px Value in pixels
 * @returns Value in millimeters
 */
export function pxToMm(px: number): number {
	return px * PX_TO_MM_RATIO;
}

/**
 * Round a number to 2 decimal places
 * @param num Number to round
 * @returns Rounded number
 */
export function roundToTwoDecimals(num: number): number {
	return Math.round(num * 100) / 100;
}

// Points (pt) to Pixels (px) and vice-versa
// Assuming 96 DPI (standard for web), 1 inch = 96px
// 1 point = 1/72 inch
// So, 1 point = (1/72) * 96 px = 96/72 px = 4/3 px
export const PT_TO_PX_RATIO = 4 / 3;
export const ptToPx = (pt: number): number =>
	roundToTwoDecimals(pt * PT_TO_PX_RATIO);
export const pxToPt = (px: number): number => px / PT_TO_PX_RATIO;
