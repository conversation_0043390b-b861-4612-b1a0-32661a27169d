import { useCallback, useState } from "react";
import type { Element } from "../types/element";
import type { TableProperties } from "../types/table";
import { pxToMm } from "./unitConversion";

export interface SnapGuideInfo {
	type: "horizontal" | "vertical";
	position: number; // in mm, relative to its context (page or block)
	start: number; // in mm, relative to its context
	end: number; // in mm, relative to its context
}

export interface SnapGuidesState {
	visible: boolean;
	guides: SnapGuideInfo[];
	relativeToBlockId?: string | null;
	blockX?: number; // Page X of the block, in mm
	blockY?: number; // Page Y of the block, in mm
	blockWidth?: number; // in mm
	blockHeight?: number; // in mm
}

export interface SnapResult {
	snapX: number | null;
	snapY: number | null;
	guides: SnapGuideInfo[];
}

export interface SnapCalculationParams {
	elementId: string;
	newX: number;
	newY: number;
	elementWidth: number;
	elementHeight: number;
	elements: Element[];
	parentBlockId?: string | null;
	snapThreshold?: number;
	/**
	 * If resizing, specify the handle that is being dragged (e.g., 'bottom-right').
	 * This allows snapping to consider only the moving edges/points, avoiding guides
	 * for edges that remain static.
	 */
	movingHandle?:
		| "top"
		| "bottom"
		| "left"
		| "right"
		| "top-left"
		| "top-right"
		| "bottom-left"
		| "bottom-right";
}

// Point priorities for snapping preference
const POINT_PRIORITIES = {
	left: 1,
	centerX: 2,
	right: 1,
	top: 1,
	centerY: 2,
	bottom: 1,
};

// --- Helper to measure table border contributions (similar to logic in EditableElement) ---
function getTableBorderContributions(tableProperties?: TableProperties) {
	if (!tableProperties) {
		return { h: 0, v: 0, left: 0, top: 0 };
	}
	const { cells, rows, columns } = tableProperties;

	let maxLeftBorderPx = 0;
	if (cells) {
		cells.forEach((row) => {
			if (row?.[0]) {
				const cell = row[0];
				const borderWidth =
					cell.borderSettings?.left?.width ?? cell.borderWidths?.left ?? 0;
				if (borderWidth > maxLeftBorderPx) maxLeftBorderPx = borderWidth;
			}
		});
	}

	let maxRightBorderPx = 0;
	if (cells && columns > 0) {
		cells.forEach((row) => {
			let logicalCol = 0;
			for (const cell of row) {
				if (!cell) continue;
				logicalCol += cell.colspan;
				if (logicalCol >= columns) {
					const borderWidth =
						cell.borderSettings?.right?.width ?? cell.borderWidths?.right ?? 0;
					if (borderWidth > maxRightBorderPx) maxRightBorderPx = borderWidth;
					break;
				}
			}
		});
	}

	let maxTopBorderPx = 0;
	if (cells?.[0]) {
		cells[0].forEach((cell) => {
			if (cell) {
				const borderWidth =
					cell.borderSettings?.top?.width ?? cell.borderWidths?.top ?? 0;
				if (borderWidth > maxTopBorderPx) maxTopBorderPx = borderWidth;
			}
		});
	}

	let maxBottomBorderPx = 0;
	if (cells && rows > 0) {
		cells.forEach((row, rowIndex) => {
			row.forEach((cell) => {
				if (cell && rowIndex + cell.rowspan >= rows) {
					const borderWidth =
						cell.borderSettings?.bottom?.width ??
						cell.borderWidths?.bottom ??
						0;
					if (borderWidth > maxBottomBorderPx) maxBottomBorderPx = borderWidth;
				}
			});
		});
	}

	// Half of the border width extends outside the table when border-collapse is active.
	const horizontalContributionMm = pxToMm(
		(maxLeftBorderPx + maxRightBorderPx) / 2,
	);
	const verticalContributionMm = pxToMm(
		(maxTopBorderPx + maxBottomBorderPx) / 2,
	);
	const leftShiftMm = pxToMm(maxLeftBorderPx / 2);
	const topShiftMm = pxToMm(maxTopBorderPx / 2);

	return {
		h: horizontalContributionMm,
		v: verticalContributionMm,
		left: leftShiftMm,
		top: topShiftMm,
	};
}

/**
 * Calculate snap guides for an element being moved or resized
 */
export function calculateSnapGuides({
	elementId,
	newX,
	newY,
	elementWidth,
	elementHeight,
	elements,
	parentBlockId,
	snapThreshold = 1,
	movingHandle,
}: SnapCalculationParams): SnapResult {
	let snapX: number | null = null;
	let snapY: number | null = null;
	const guides: SnapGuideInfo[] = [];

	// Get the element that's being dragged/resized
	const targetElement = elements.find((el) => el.id === elementId);
	if (!targetElement) {
		return { snapX, snapY, guides };
	}

	// Determine which elements to use for snapping
	let relevantElements: Element[] = [];

	// If we're editing inside a block, we want to snap to:
	// 1. Other elements within the same block
	// 2. The block's boundaries
	if (parentBlockId) {
		// Find the parent block
		const parentBlock = elements.find((el) => el.id === parentBlockId);
		if (parentBlock) {
			// Add siblings (other elements in the same block)
			relevantElements = elements.filter(
				(el) => el.parentId === parentBlockId && el.id !== elementId,
			);
		}
	}
	// If we're not editing inside a block, we want to snap to:
	// 1. Other top-level elements on the same page
	else {
		// Only consider other top-level elements on the same page
		relevantElements = elements.filter(
			(el) =>
				!el.parentId && // Only top-level elements
				el.currentPageId === targetElement.currentPageId && // On the same page
				el.id !== elementId, // Not the element itself
		);
	}

	if (relevantElements.length === 0) {
		return { snapX, snapY, guides };
	}

	// Calculate boundaries of the element being moved/resized, taking possible thick table borders into account
	const computeBounding = (
		el: Element,
		baseX: number,
		baseY: number,
		baseW: number,
		baseH: number,
	) => {
		if (el.type === "table") {
			const {
				h,
				v,
				left: lShift,
				top: tShift,
			} = getTableBorderContributions(el.tableProperties as TableProperties);
			const adjLeft = baseX - lShift;
			const adjTop = baseY - tShift;
			const width = baseW + h;
			const height = baseH + v;
			return {
				left: adjLeft,
				right: adjLeft + width,
				top: adjTop,
				bottom: adjTop + height,
				centerX: adjLeft + width / 2,
				centerY: adjTop + height / 2,
			};
		}
		// Non-table elements
		return {
			left: baseX,
			right: baseX + baseW,
			top: baseY,
			bottom: baseY + baseH,
			centerX: baseX + baseW / 2,
			centerY: baseY + baseH / 2,
		};
	};

	const { left, right, top, bottom, centerX, centerY } = computeBounding(
		targetElement,
		newX,
		newY,
		elementWidth,
		elementHeight,
	);

	// Update best snap structure to store the element snapped to
	let bestHorizontalSnap = {
		point: null as string | null,
		target: null as number | null,
		distance: snapThreshold + 1,
		priority: 999,
		snappedToElement: null as Element | null,
	};
	let bestVerticalSnap = {
		point: null as string | null,
		target: null as number | null,
		distance: snapThreshold + 1,
		priority: 999,
		snappedToElement: null as Element | null,
	};

	let xPoints = [
		{ name: "left", value: left, priority: POINT_PRIORITIES.left },
		{ name: "centerX", value: centerX, priority: POINT_PRIORITIES.centerX },
		{ name: "right", value: right, priority: POINT_PRIORITIES.right },
	];
	let yPoints = [
		{ name: "top", value: top, priority: POINT_PRIORITIES.top },
		{ name: "centerY", value: centerY, priority: POINT_PRIORITIES.centerY },
		{ name: "bottom", value: bottom, priority: POINT_PRIORITIES.bottom },
	];

	// If a moving handle is specified, filter points so that only axes which move are considered.
	if (movingHandle) {
		// Horizontal axis filtering
		if (movingHandle.includes("left")) {
			xPoints = xPoints.filter(
				(pt) => pt.name === "left" || pt.name === "centerX",
			);
		} else if (movingHandle.includes("right")) {
			xPoints = xPoints.filter(
				(pt) => pt.name === "right" || pt.name === "centerX",
			);
		} else {
			// Handle does not move horizontally (top/bottom), so skip horizontal snapping
			xPoints = [];
		}

		// Vertical axis filtering
		if (movingHandle.includes("top")) {
			yPoints = yPoints.filter(
				(pt) => pt.name === "top" || pt.name === "centerY",
			);
		} else if (movingHandle.includes("bottom")) {
			yPoints = yPoints.filter(
				(pt) => pt.name === "bottom" || pt.name === "centerY",
			);
		} else {
			// Handle does not move vertically (left/right), so skip vertical snapping
			yPoints = [];
		}
	}

	relevantElements.forEach((otherElement) => {
		const otherBounds = computeBounding(
			otherElement,
			otherElement.x,
			otherElement.y,
			otherElement.width,
			otherElement.height,
		);

		const otherXPoints = [
			{
				name: "left",
				value: otherBounds.left,
				priority: POINT_PRIORITIES.left,
			},
			{
				name: "centerX",
				value: otherBounds.centerX,
				priority: POINT_PRIORITIES.centerX,
			},
			{
				name: "right",
				value: otherBounds.right,
				priority: POINT_PRIORITIES.right,
			},
		];
		const otherYPoints = [
			{ name: "top", value: otherBounds.top, priority: POINT_PRIORITIES.top },
			{
				name: "centerY",
				value: otherBounds.centerY,
				priority: POINT_PRIORITIES.centerY,
			},
			{
				name: "bottom",
				value: otherBounds.bottom,
				priority: POINT_PRIORITIES.bottom,
			},
		];

		// Check horizontal snapping
		xPoints.forEach((point) => {
			otherXPoints.forEach((otherPoint) => {
				const distance = Math.abs(point.value - otherPoint.value);
				if (distance <= snapThreshold) {
					const combinedPriority = point.priority + otherPoint.priority;
					const sameTypeBonus =
						(point.name === "centerX" && otherPoint.name === "centerX") ||
						(point.name !== "centerX" && otherPoint.name !== "centerX")
							? -1
							: 0;
					const finalPriority = combinedPriority + sameTypeBonus;

					if (
						distance < bestHorizontalSnap.distance ||
						(distance === bestHorizontalSnap.distance &&
							finalPriority < bestHorizontalSnap.priority)
					) {
						bestHorizontalSnap = {
							point: point.name,
							target: otherPoint.value,
							distance: distance,
							priority: finalPriority,
							snappedToElement: otherElement,
						};
					}
				}
			});
		});

		// Check vertical snapping
		yPoints.forEach((point) => {
			otherYPoints.forEach((otherPoint) => {
				const distance = Math.abs(point.value - otherPoint.value);
				if (distance <= snapThreshold) {
					const combinedPriority = point.priority + otherPoint.priority;
					const sameTypeBonus =
						(point.name === "centerY" && otherPoint.name === "centerY") ||
						(point.name !== "centerY" && otherPoint.name !== "centerY")
							? -1
							: 0;
					const finalPriority = combinedPriority + sameTypeBonus;

					if (
						distance < bestVerticalSnap.distance ||
						(distance === bestVerticalSnap.distance &&
							finalPriority < bestVerticalSnap.priority)
					) {
						bestVerticalSnap = {
							point: point.name,
							target: otherPoint.value,
							distance: distance,
							priority: finalPriority,
							snappedToElement: otherElement,
						};
					}
				}
			});
		});
	});

	// Apply best horizontal snap if found and generate guide with correct extent
	if (bestHorizontalSnap.target !== null) {
		snapX = bestHorizontalSnap.target;
		let guideStart = 0;
		let guideEnd = 1000; // Default large extent
		const snappedTo = bestHorizontalSnap.snappedToElement;

		if (snappedTo) {
			// Check if snapped to a virtual parent boundary
			const isParentSnap = snappedTo.id?.startsWith("parent-");
			if (isParentSnap && parentBlockId) {
				const parentBlock = elements.find((el) => el.id === parentBlockId);
				if (parentBlock) {
					guideStart = 0;
					guideEnd = parentBlock.height;
				}
			} else {
				// Snap to sibling or page element - calculate union of vertical extents
				const snappedBounds = computeBounding(
					snappedTo,
					snappedTo.x,
					snappedTo.y,
					snappedTo.width,
					snappedTo.height,
				);
				guideStart = Math.min(top, snappedBounds.top);
				guideEnd = Math.max(bottom, snappedBounds.bottom);
			}
		}
		guides.push({
			type: "vertical",
			position: snapX,
			start: guideStart,
			end: guideEnd,
		});
	}

	// Apply best vertical snap if found and generate guide with correct extent
	if (bestVerticalSnap.target !== null) {
		snapY = bestVerticalSnap.target;
		let guideStart = 0;
		let guideEnd = 1000; // Default large extent
		const snappedTo = bestVerticalSnap.snappedToElement;

		if (snappedTo) {
			// Check if snapped to a virtual parent boundary
			const isParentSnap = snappedTo.id?.startsWith("parent-");
			if (isParentSnap && parentBlockId) {
				const parentBlock = elements.find((el) => el.id === parentBlockId);
				if (parentBlock) {
					guideStart = 0;
					guideEnd = parentBlock.width;
				}
			} else {
				// Snap to sibling or page element - calculate union of horizontal extents
				const snappedBounds = computeBounding(
					snappedTo,
					snappedTo.x,
					snappedTo.y,
					snappedTo.width,
					snappedTo.height,
				);
				guideStart = Math.min(left, snappedBounds.left);
				guideEnd = Math.max(right, snappedBounds.right);
			}
		}
		guides.push({
			type: "horizontal",
			position: snapY,
			start: guideStart,
			end: guideEnd,
		});
	}

	return { snapX, snapY, guides };
}

/**
 * Update snap guides state with new guide information
 */
export function updateSnapGuides(
	visible: boolean,
	guidesData: SnapGuideInfo[],
	elements: Element[],
	relativeToBlockId?: string | null,
): SnapGuidesState {
	if (relativeToBlockId) {
		const block = elements.find((el) => el.id === relativeToBlockId);
		if (block) {
			return {
				visible,
				guides: guidesData,
				relativeToBlockId,
				blockX: block.x,
				blockY: block.y,
				blockWidth: block.width,
				blockHeight: block.height,
			};
		} else {
			return { visible, guides: guidesData, relativeToBlockId: null };
		}
	} else {
		return { visible, guides: guidesData, relativeToBlockId: null };
	}
}

/**
 * React hook for managing snap guides state
 */
export function useSnapGuides(elements: Element[]) {
	const [snapGuides, setSnapGuides] = useState<SnapGuidesState>({
		visible: false,
		guides: [],
		relativeToBlockId: null,
	});

	const updateSnapGuidesToState = useCallback(
		(
			visible: boolean,
			guidesData: SnapGuideInfo[],
			relativeToBlockId?: string | null,
		) => {
			const newState = updateSnapGuides(
				visible,
				guidesData,
				elements,
				relativeToBlockId,
			);
			setSnapGuides(newState);
		},
		[elements],
	);

	const calculateElementSnapGuides = useCallback(
		(params: Omit<SnapCalculationParams, "elements">): SnapResult => {
			return calculateSnapGuides({
				...params,
				elements,
			});
		},
		[elements],
	);

	const clearSnapGuides = useCallback(() => {
		setSnapGuides({
			visible: false,
			guides: [],
			relativeToBlockId: null,
		});
	}, []);

	return {
		snapGuides,
		updateSnapGuidesToState,
		calculateElementSnapGuides,
		clearSnapGuides,
	};
}
