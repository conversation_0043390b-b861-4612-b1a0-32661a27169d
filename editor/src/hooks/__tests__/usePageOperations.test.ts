import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import { usePageOperations } from "../usePageOperations";

// Mock uuid to return predictable values
vi.mock("uuid", () => ({
	v4: vi
		.fn()
		.mockReturnValueOnce("page-front-1")
		.mockReturnValueOnce("page-back-1")
		.mockReturnValueOnce("page-front-2")
		.mockReturnValueOnce("page-back-2")
		.mockReturnValueOnce("page-front-3")
		.mockReturnValueOnce("page-back-3")
		.mockReturnValueOnce("page-front-4")
		.mockReturnValueOnce("page-back-4"),
}));

describe("usePageOperations", () => {
	// Setup common test variables
	const mockPages = [
		PageFactory.createPage({ id: "page-1" }),
		PageFactory.createPage({ id: "page-2" }),
	];

	const mockElements = [
		ElementFactory.createTextElement({ id: "text-1", currentPageId: "page-1" }),
		ElementFactory.createShapeElement({
			id: "shape-1",
			currentPageId: "page-2",
		}),
	];

	const mockApiFormats = [
		{
			name: "A4",
			description: "A4 Format",
			width: 210, // mm
			height: 297, // mm
			trim_top: 5,
			trim_right: 5,
			trim_bottom: 5,
			trim_left: 5,
			pagenumber_w: 105,
			pagenumber_y: 280,
			address_x: 20,
			address_y: 50,
		},
		{
			name: "A4_landscape",
			description: "A4 Landscape Format",
			width: 297, // mm
			height: 210, // mm
			trim_top: 5,
			trim_right: 5,
			trim_bottom: 5,
			trim_left: 5,
			pagenumber_w: 148,
			pagenumber_y: 190,
			address_x: 20,
			address_y: 50,
		},
	];

	const mockSetEditorState = vi.fn();
	const mockSetCurrentPage = vi.fn();

	const defaultProps = {
		pages: mockPages,
		elements: mockElements,
		setEditorState: mockSetEditorState,
		currentPage: "page-1",
		setCurrentPage: mockSetCurrentPage,
		isEditorLocked: false,
		apiFormats: mockApiFormats,
	};

	beforeEach(() => {
		vi.clearAllMocks();
	});

	// Test page creation
	describe("handleAddPage", () => {
		it("should add a new page pair with the specified format", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			act(() => {
				result.current.handleAddPage("A4");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			// Should have added two new pages (front and back)
			expect(newPages.length).toBe(4);

			// Check the new pages
			const frontPage = newPages[2]; // Added at the end
			const backPage = newPages[3];

			expect(frontPage.id).toBe("page-front-1");
			expect(backPage.id).toBe("page-back-1");

			expect(frontPage.format).toBe("A4");
			expect(backPage.format).toBe("A4");

			expect(frontPage.width).toBe(21); // 210mm / 10 = 21cm
			expect(frontPage.height).toBe(29.7); // 297mm / 10 = 29.7cm

			// Should set current page to the new front page
			expect(mockSetCurrentPage).toHaveBeenCalledWith("page-front-1");
		});

		it("should add a new page pair at the specified index", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			act(() => {
				result.current.handleAddPage("A4", 1); // Insert after the first page
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			// Should have added two new pages at index 1
			expect(newPages.length).toBe(4);
			expect(newPages[1].id).toBe("page-front-2"); // Second pair of UUIDs
			expect(newPages[2].id).toBe("page-back-2");

			// Original pages should be at index 0 and 3
			expect(newPages[0].id).toBe("page-1");
			expect(newPages[3].id).toBe("page-2");
		});

		it("should handle landscape format correctly", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			act(() => {
				result.current.handleAddPage("A4_landscape");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			const frontPage = newPages[2];
			expect(frontPage.orientation).toBe("landscape");
			expect(frontPage.width).toBe(29.7); // 297mm / 10 = 29.7cm
			expect(frontPage.height).toBe(21); // 210mm / 10 = 21cm
		});

		it("should use fallback formats when API formats are not available", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					apiFormats: [], // Empty API formats
				}),
			);

			act(() => {
				result.current.handleAddPage("A4");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			const frontPage = newPages[2];
			expect(frontPage.format).toBe("A4");
			expect(frontPage.width).toBe(21); // From fallback
			expect(frontPage.height).toBe(29.7); // From fallback
		});

		it("should not add pages if editor is locked", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					isEditorLocked: true,
				}),
			);

			act(() => {
				result.current.handleAddPage("A4");
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});
	});

	// Test page deletion
	describe("handleDeletePage", () => {
		it("should delete a page and its pair when both are empty", () => {
			// Create pages that are paired (even/odd indices)
			const pairedPages = [
				PageFactory.createPage({ id: "front-1" }),
				PageFactory.createPage({ id: "back-1" }),
				PageFactory.createPage({ id: "front-2" }),
				PageFactory.createPage({ id: "back-2" }),
			];

			// Elements only on the first pair
			const pairedElements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "front-1",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					pages: pairedPages,
					elements: pairedElements,
					currentPage: "front-2",
				}),
			);

			act(() => {
				result.current.handleDeletePage("front-2");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;
			const newElements = mockSetEditorState.mock.calls[0][0].elements;

			// Should have deleted both pages in the pair
			expect(newPages.length).toBe(2);
			expect(newPages[0].id).toBe("front-1");
			expect(newPages[1].id).toBe("back-1");

			// Elements should remain unchanged
			expect(newElements).toEqual(pairedElements);

			// Should update current page
			expect(mockSetCurrentPage).toHaveBeenCalledWith("front-1");
		});

		it("should only clear elements when the paired page has content", () => {
			// Create pages that are paired (even/odd indices)
			const pairedPages = [
				PageFactory.createPage({ id: "front-1" }),
				PageFactory.createPage({ id: "back-1" }),
			];

			// Elements on both pages of the pair
			const pairedElements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "front-1",
				}),
				ElementFactory.createShapeElement({
					id: "shape-1",
					currentPageId: "back-1",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					pages: pairedPages,
					elements: pairedElements,
				}),
			);

			act(() => {
				result.current.handleDeletePage("front-1");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;
			const newElements = mockSetEditorState.mock.calls[0][0].elements;

			// Pages should remain unchanged
			expect(newPages).toEqual(pairedPages);

			// Only elements from the deleted page should be removed
			expect(newElements.length).toBe(1);
			expect(newElements[0].id).toBe("shape-1");
		});

		it("should ignore page number elements when determining if a page is empty", () => {
			// Create pages that are paired (even/odd indices)
			const pairedPages = [
				PageFactory.createPage({ id: "front-1" }),
				PageFactory.createPage({ id: "back-1" }),
			];

			// Only page number element on back-1
			const pairedElements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "front-1",
				}),
				ElementFactory.createTextElement({
					id: "page-num",
					currentPageId: "back-1",
					isPageNumber: true,
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					pages: pairedPages,
					elements: pairedElements,
				}),
			);

			act(() => {
				result.current.handleDeletePage("front-1");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			// Both pages should be deleted since back-1 only has a page number element
			expect(newPages.length).toBe(0);
		});

		it("should not delete pages if editor is locked", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					isEditorLocked: true,
				}),
			);

			act(() => {
				result.current.handleDeletePage("page-1");
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});
	});

	// Test page reordering
	describe("handleReorderPages", () => {
		it("should reorder page pairs", () => {
			// Create multiple page pairs
			const multiPages = [
				PageFactory.createPage({ id: "front-1" }),
				PageFactory.createPage({ id: "back-1" }),
				PageFactory.createPage({ id: "front-2" }),
				PageFactory.createPage({ id: "back-2" }),
				PageFactory.createPage({ id: "front-3" }),
				PageFactory.createPage({ id: "back-3" }),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					pages: multiPages,
				}),
			);

			// Move the first pair (index 0,1) to after the second pair (to index 4)
			act(() => {
				result.current.handleReorderPages(0, 4);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			// Expected order: front-2, back-2, front-1, back-1, front-3, back-3
			expect(newPages.map((p) => p.id)).toEqual([
				"front-2",
				"back-2",
				"front-1",
				"back-1",
				"front-3",
				"back-3",
			]);
		});

		it("should not reorder if source and target indices are in the same pair", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			// Try to reorder within the same pair (both indices map to pair index 0)
			act(() => {
				result.current.handleReorderPages(0, 1);
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should not reorder if editor is locked", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					isEditorLocked: true,
				}),
			);

			act(() => {
				result.current.handleReorderPages(0, 2);
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});
	});

	// Test page swapping
	describe("handleSwapPages", () => {
		it("should swap two pages", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			act(() => {
				result.current.handleSwapPages(0, 1);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newPages = mockSetEditorState.mock.calls[0][0].pages;

			// Pages should be swapped
			expect(newPages[0].id).toBe("page-2");
			expect(newPages[1].id).toBe("page-1");
		});

		it("should not swap if indices are the same", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			act(() => {
				result.current.handleSwapPages(0, 0);
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should not swap if indices are out of bounds", () => {
			const { result } = renderHook(() => usePageOperations(defaultProps));

			act(() => {
				result.current.handleSwapPages(0, 5); // Index 5 is out of bounds
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should not swap if editor is locked", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					isEditorLocked: true,
				}),
			);

			act(() => {
				result.current.handleSwapPages(0, 1);
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});
	});

	// Test page-element relationship management
	describe("page-element relationship management", () => {
		it("should maintain element-page relationships when deleting pages", () => {
			// Create pages and elements on different pages
			const testPages = [
				PageFactory.createPage({ id: "page-1" }),
				PageFactory.createPage({ id: "page-2" }),
				PageFactory.createPage({ id: "page-3" }),
				PageFactory.createPage({ id: "page-4" }),
			];

			const testElements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "page-1",
				}),
				ElementFactory.createShapeElement({
					id: "shape-1",
					currentPageId: "page-2",
				}),
				ElementFactory.createImageElement({
					id: "image-1",
					currentPageId: "page-3",
				}),
				ElementFactory.createTableElement({
					id: "table-1",
					currentPageId: "page-4",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					pages: testPages,
					elements: testElements,
				}),
			);

			// Delete page-3 (which should only clear elements from page-3 since page-4 has content)
			act(() => {
				result.current.handleDeletePage("page-3");
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newElements = mockSetEditorState.mock.calls[0][0].elements;

			// Only elements from page-3 should be removed (page-4 has content so it stays)
			expect(newElements.length).toBe(3);
			expect(newElements.find((el) => el.id === "image-1")).toBeUndefined();
			expect(newElements.find((el) => el.id === "table-1")).toBeDefined(); // page-4 element remains

			// Elements from page-1 and page-2 should remain
			expect(newElements.find((el) => el.id === "text-1")).toBeDefined();
			expect(newElements.find((el) => el.id === "shape-1")).toBeDefined();
		});

		it("should preserve elements when reordering pages", () => {
			// Create pages and elements
			const testPages = [
				PageFactory.createPage({ id: "page-1" }),
				PageFactory.createPage({ id: "page-2" }),
				PageFactory.createPage({ id: "page-3" }),
				PageFactory.createPage({ id: "page-4" }),
			];

			const testElements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "page-1",
				}),
				ElementFactory.createShapeElement({
					id: "shape-1",
					currentPageId: "page-2",
				}),
				ElementFactory.createImageElement({
					id: "image-1",
					currentPageId: "page-3",
				}),
				ElementFactory.createTableElement({
					id: "table-1",
					currentPageId: "page-4",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					...defaultProps,
					pages: testPages,
					elements: testElements,
				}),
			);

			// Reorder pages (move first pair to after second pair)
			act(() => {
				result.current.handleReorderPages(0, 4);
			});

			expect(mockSetEditorState).toHaveBeenCalled();
			const newElements = mockSetEditorState.mock.calls[0][0].elements;

			// All elements should be preserved
			expect(newElements.length).toBe(4);
			expect(newElements).toEqual(testElements);
		});
	});
});
