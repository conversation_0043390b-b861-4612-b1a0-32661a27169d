import { useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

interface ElementOperationsProps {
	pages: PageSettings[];
	elements: Element[];
	setEditorState: (
		state: { pages: PageSettings[]; elements: Element[] },
		addToHistory?: boolean,
	) => void;
	commitBatch: (state: { pages: PageSettings[]; elements: Element[] }) => void;
	beginBatch: () => void;
	updateDraft: (state: { pages: PageSettings[]; elements: Element[] }) => void;
	isBatching: () => boolean;
	currentPage: string | null;
	editingBlockId: string | null;
	selectedElementId: string | null;
	setSelectedElementId: (id: string | null) => void;
	setEditingBlockId: (id: string | null) => void;
	setDeletedPageNumberIds: (fn: (prev: Set<string>) => Set<string>) => void;
	isEditorLocked: boolean;
	clipboard:
		| Element
		| { block: Element; children: Element[] }
		| { childElement: Element; originalParentId: string }
		| null;
	setClipboard: (
		clipboard:
			| Element
			| { block: Element; children: Element[] }
			| { childElement: Element; originalParentId: string }
			| null,
	) => void;
	isEditing: boolean;
}

export function useElementOperations({
	pages,
	elements,
	setEditorState,
	commitBatch,
	beginBatch,
	updateDraft,
	isBatching,
	currentPage,
	editingBlockId,
	selectedElementId,
	setSelectedElementId,
	setEditingBlockId,
	setDeletedPageNumberIds,
	isEditorLocked,
	clipboard,
	setClipboard,
	isEditing,
}: ElementOperationsProps) {
	const handleAddElement = useCallback(
		(newElement: Element | import("../types/element").BatchDeleteOperation) => {
			if (isEditorLocked) return;

			// Check if this is a batch delete operation
			if ("_batchDelete" in newElement && newElement._batchDelete) {
				const batchDeleteData =
					newElement as unknown as import("../types/element").BatchDeleteOperation;
				console.log(
					"[ElementOps] Batch delete operation for elements:",
					batchDeleteData.elementIds,
				);

				// Remove all elements with the specified IDs in a single operation
				const filteredElements = elements.filter(
					(el) => !batchDeleteData.elementIds.includes(el.id),
				);
				console.log(
					"[ElementOps] Removing",
					batchDeleteData.elementIds.length,
					"elements, new array length:",
					filteredElements.length,
				);
				setEditorState({ pages, elements: filteredElements });

				// Clear selection if any of the deleted elements were selected
				if (
					selectedElementId &&
					batchDeleteData.elementIds.includes(selectedElementId)
				) {
					setSelectedElementId(null);
				}
				return;
			}

			// Check if this is a regular Element (not BatchDeleteOperation)
			if (!("_batchDelete" in newElement)) {
				const element = newElement as import("../types/element").Element;

				// Check if this is a delete operation
				if (element._delete) {
					console.log("[ElementOps] Delete operation for element:", element.id);
					// Remove the element
					const filteredElements = elements.filter(
						(el) => el.id !== element.id,
					);
					console.log(
						"[ElementOps] Removing element, new array length:",
						filteredElements.length,
					);
					setEditorState({ pages, elements: filteredElements });

					// Clear selection if the deleted element was selected
					if (selectedElementId === element.id) {
						setSelectedElementId(null);
					}
					return;
				}

				// Set the current page if the element doesn't have one
				if (!element.currentPageId && currentPage) {
					element.currentPageId = currentPage;
				}

				// If we're editing a block, add the element to that block
				if (editingBlockId) {
					const blockIndex = elements.findIndex(
						(el) => el.id === editingBlockId,
					);
					if (blockIndex !== -1) {
						const blockElement = { ...elements[blockIndex] };

						// Ensure childElementIds exists and append this element's id later
						if (!blockElement.childElementIds) {
							blockElement.childElementIds = [];
						}
						blockElement.childElementIds.push(element.id);

						// Convert element position to be relative to block
						element.x = Math.max(0, element.x - blockElement.x);
						element.y = Math.max(0, element.y - blockElement.y);

						element.parentId = blockElement.id;
						element.currentPageId = blockElement.currentPageId;

						// Replace block in elements array lazily after loop (collect updates)
						elements[blockIndex] = blockElement;
					}
				}

				// Only add if element has a page assigned
				if (!element.currentPageId) {
					console.warn("[ElementOps] Cannot add element without currentPageId");
					return;
				}

				console.log(
					"[ElementOps] Adding element to page:",
					element.currentPageId,
				);

				// Find the highest z-index on the current page
				const elementsOnCurrentPage = elements.filter(
					(el) => el.currentPageId === element.currentPageId,
				);

				const highestZIndex = Math.max(
					0,
					...elementsOnCurrentPage.map((el) => el.zIndex || 0),
				);
				// Assign z-index to the new element if not already set
				if (element.zIndex === undefined) {
					element.zIndex = highestZIndex + 1;
				}

				const newElementsArray = [...elements, element];

				setEditorState({ pages, elements: newElementsArray });
				setSelectedElementId(element.id);
				console.log(
					"[ElementOps] Element added successfully, selected element ID set to:",
					element.id,
				);
			}
		},
		[
			isEditorLocked,
			elements,
			pages,
			setEditorState,
			selectedElementId,
			setSelectedElementId,
			currentPage,
			editingBlockId,
		],
	);

	const handleAddElements = useCallback(
		(newElements: Element[]) => {
			if (isEditorLocked) return;
			if (newElements.length === 0) return;

			// Process all elements
			const processedElements = newElements
				.map((element) => {
					// Set the current page if the element doesn't have one
					if (!element.currentPageId && currentPage) {
						element.currentPageId = currentPage;
					}

					// If we're editing a block, add the element to that block
					if (editingBlockId) {
						const blockIndex = elements.findIndex(
							(el) => el.id === editingBlockId,
						);
						if (blockIndex !== -1) {
							const blockElement = { ...elements[blockIndex] };

							// Ensure childElementIds exists and append this element's id later
							if (!blockElement.childElementIds) {
								blockElement.childElementIds = [];
							}
							blockElement.childElementIds.push(element.id);

							// Convert element position to be relative to block
							element.x = Math.max(0, element.x - blockElement.x);
							element.y = Math.max(0, element.y - blockElement.y);

							element.parentId = blockElement.id;
							element.currentPageId = blockElement.currentPageId;

							// Replace block in elements array lazily after loop (collect updates)
							elements[blockIndex] = blockElement;
						}
					}

					// Only include if element has a page assigned
					if (!element.currentPageId) {
						console.warn(
							"[ElementOps] Skipping element without currentPageId:",
							element.id,
						);
						return null;
					}

					return element;
				})
				.filter(Boolean) as Element[];

			if (processedElements.length === 0) {
				console.warn("[ElementOps] No valid elements to add");
				return;
			}

			// Find the highest z-index on the current page
			const elementsOnCurrentPage = elements.filter(
				(el) => el.currentPageId === processedElements[0].currentPageId,
			);

			const highestZIndex = Math.max(
				0,
				...elementsOnCurrentPage.map((el) => el.zIndex || 0),
			);

			// Assign z-index values to all new elements
			processedElements.forEach((element, index) => {
				if (element.zIndex === undefined) {
					element.zIndex = highestZIndex + index + 1;
				}
			});

			// Add all elements in a single state update
			const newElementsArray = [...elements, ...processedElements];

			setEditorState({ pages, elements: newElementsArray });

			// Select the appropriate element based on what was added
			if (processedElements.length > 0) {
				// If address field elements are being added, select the address container instead of text elements
				const addressContainer = processedElements.find(
					(el) => el.type === "address" && el.isAddressField,
				);
				const elementToSelect =
					addressContainer || processedElements[processedElements.length - 1];

				setSelectedElementId(elementToSelect.id);
			}
		},
		[
			isEditorLocked,
			elements,
			pages,
			setEditorState,
			currentPage,
			editingBlockId,
			setSelectedElementId,
		],
	);

	const handleUpdateElement = useCallback(
		(updatedElement: Element, isFinal: boolean) => {
			// Intercept z-index actions
			if (updatedElement._action) {
				const action = updatedElement._action;
				// Remove the temporary action property before processing
				const elementToUpdate = { ...updatedElement };
				delete elementToUpdate._action;

				// Get elements on the same page
				const elementsOnPage = elements.filter(
					(el) => el.currentPageId === elementToUpdate.currentPageId,
				);

				let newElements: Element[] = [];

				if (action === "moveToFront") {
					const otherElements = elementsOnPage.filter(
						(el) => el.id !== elementToUpdate.id,
					);
					const sortedOthers = [...otherElements].sort(
						(a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0),
					);

					// Re-index the other elements sequentially
					const reindexedOthers = sortedOthers.map((el, index) => ({
						...el,
						zIndex: index,
					}));

					// Set the target element's z-index to the highest
					elementToUpdate.zIndex = reindexedOthers.length;

					// Combine the elements
					newElements = elements.map((el) => {
						if (el.currentPageId !== elementToUpdate.currentPageId) return el;
						if (el.id === elementToUpdate.id) return elementToUpdate;
						return reindexedOthers.find((reo) => reo.id === el.id) || el; // Find the re-indexed version
					});
				} else if (action === "moveToBack") {
					const otherElements = elementsOnPage.filter(
						(el) => el.id !== elementToUpdate.id,
					);
					const sortedOthers = [...otherElements].sort(
						(a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0),
					);

					// Re-index the other elements starting from 1
					const reindexedOthers = sortedOthers.map((el, index) => ({
						...el,
						zIndex: index + 1,
					}));

					// Set the target element's z-index to 0
					elementToUpdate.zIndex = 0;

					// Combine the elements
					newElements = elements.map((el) => {
						if (el.currentPageId !== elementToUpdate.currentPageId) return el;
						if (el.id === elementToUpdate.id) return elementToUpdate;
						return reindexedOthers.find((reo) => reo.id === el.id) || el; // Find the re-indexed version
					});
				} else if (action === "setZIndex") {
					// Direct set, just update the single element's zIndex
					// Need to compress other elements if the new zIndex creates a gap or conflict
					const newZIndex = elementToUpdate.zIndex ?? 0;
					const otherElements = elementsOnPage.filter(
						(el) => el.id !== elementToUpdate.id,
					);

					// Sort others to easily find conflicts/gaps
					const sortedOthers = [...otherElements].sort(
						(a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0),
					);

					let needsReindex = false;
					// Check if the new zIndex is higher than the max possible index, or if it conflicts
					if (newZIndex > sortedOthers.length) {
						elementToUpdate.zIndex = sortedOthers.length; // Cap the zIndex
						needsReindex = true; // Re-index others to ensure sequence
					} else {
						// Check if this z-index is already taken by another element
						if (sortedOthers.some((el) => (el.zIndex ?? 0) === newZIndex)) {
							needsReindex = true; // Collision requires re-indexing
						}
					}

					if (needsReindex) {
						// Re-index other elements sequentially, leaving a space for the target element
						const reindexedOthers = sortedOthers.map((el, index) => {
							// Shift elements up if they are at or above the target's new index
							return {
								...el,
								zIndex:
									index >= (elementToUpdate.zIndex ?? 0) ? index + 1 : index,
							};
						});
						// Combine the elements
						newElements = elements.map((el) => {
							if (el.currentPageId !== elementToUpdate.currentPageId) return el;
							if (el.id === elementToUpdate.id) return elementToUpdate;
							return reindexedOthers.find((reo) => reo.id === el.id) || el;
						});
					} else {
						// No re-indexing needed, just update the target element
						newElements = elements.map((el) =>
							el.id === elementToUpdate.id ? elementToUpdate : el,
						);
					}
				}

				// Update the state with the reordered/updated elements
				// Use commitBatch directly because reordering affects multiple elements
				commitBatch({ pages, elements: newElements });
			} else {
				// Standard update logic for position, size, content etc.
				// Do not update if locked, unless it's a non-final update (e.g. dragging animation)
				// However, the EditableElement itself should prevent this for locked state.
				// For safety, we can add a check here too for final commits.
				if (isEditorLocked && isFinal) return;

				const currentElements = elements.map((el) =>
					el.id === updatedElement.id ? updatedElement : el,
				);
				const newState = { pages, elements: currentElements };

				if (isFinal) {
					commitBatch(newState);
				} else {
					if (!isBatching()) {
						beginBatch();
					}
					updateDraft(newState);
				}
			}
		},
		[
			elements,
			pages,
			commitBatch,
			isEditorLocked,
			isBatching,
			beginBatch,
			updateDraft,
		],
	);

	const handleDeleteElement = useCallback(
		(elementId: string) => {
			if (isEditorLocked) return;

			const elementToDelete = elements.find((el) => el.id === elementId);
			if (!elementToDelete) return;

			// Prevent deletion of address field elements
			if (elementToDelete.isAddressField) {
				console.warn(
					"[ElementOps] Cannot delete address field element:",
					elementId,
				);
				return;
			}

			// If we're deleting a block that's currently being edited, exit block edit mode
			if (editingBlockId === elementId) {
				setEditingBlockId(null);
				console.log(
					"[ElementOps] Exiting block edit mode because the block is being deleted:",
					elementId,
				);
			}

			let newElements = [...elements];
			let parentBlockToUpdate: Element | undefined;

			// If the element to delete is a page number, update deletedPageNumberIds
			if (elementToDelete.isPageNumber && elementToDelete.currentPageId) {
				setDeletedPageNumberIds((prev) =>
					new Set(prev).add(elementToDelete.currentPageId),
				);
			}

			// If the deleted element is a block, also delete its children
			if (elementToDelete.type === "block" && elementToDelete.childElementIds) {
				const childIdsToDelete = new Set(elementToDelete.childElementIds);
				newElements = newElements.filter(
					(el) => el.id !== elementId && !childIdsToDelete.has(el.id),
				);
			}
			// If the deleted element is a child of a block, remove it from parent's childElementIds
			else if (elementToDelete.parentId) {
				const parentBlock = newElements.find(
					(el) => el.id === elementToDelete.parentId,
				);
				if (parentBlock && parentBlock.type === "block") {
					parentBlockToUpdate = {
						...parentBlock,
						childElementIds: (parentBlock.childElementIds || []).filter(
							(childId) => childId !== elementId,
						),
					};
					newElements = newElements
						.map((el) => (el.id === parentBlock.id ? parentBlockToUpdate : el))
						.filter((el): el is Element => el !== undefined);
				}
				// Now filter out the element itself
				newElements = newElements.filter((el) => el.id !== elementId);
			}
			// Standard element deletion (not a block and not a child of a block)
			else {
				newElements = newElements.filter((el) => el.id !== elementId);
			}

			setEditorState({ pages, elements: newElements });
		},
		[
			isEditorLocked,
			elements,
			editingBlockId,
			setEditingBlockId,
			setDeletedPageNumberIds,
			pages,
			setEditorState,
		],
	);

	const handleCopyElement = useCallback(
		(elementId: string) => {
			if (isEditorLocked) return;
			const elementToCopy = elements.find((el) => el.id === elementId);
			if (elementToCopy) {
				// Prevent copying of address field elements
				if (elementToCopy.isAddressField) {
					return;
				}
				if (elementToCopy.type === "block" && elementToCopy.childElementIds) {
					const childElementsToCopy = elements.filter((el) =>
						elementToCopy.childElementIds?.includes(el.id),
					);
					setClipboard({ block: elementToCopy, children: childElementsToCopy });
				} else if (elementToCopy.parentId) {
					// This is a child element within a block - copy it as an individual child element
					setClipboard({
						childElement: elementToCopy,
						originalParentId: elementToCopy.parentId,
					});
				} else {
					// This is a standalone element
					setClipboard(elementToCopy);
				}
			}
		},
		[isEditorLocked, elements, setClipboard],
	);

	const handleCutElement = useCallback(
		(elementId: string) => {
			if (isEditorLocked) return;

			const elementToCut = elements.find((el) => el.id === elementId);
			if (!elementToCut) return;

			// Prevent cutting of address field elements
			if (elementToCut.isAddressField) {
				return;
			}

			// handle copying the element first
			handleCopyElement(elementId);

			// Then delete the element (handleDeleteElement will manage children if it's a block)
			handleDeleteElement(elementId);
		},
		[isEditorLocked, elements, handleCopyElement, handleDeleteElement],
	);

	const handlePasteAction = useCallback(
		(targetPageId: string, pasteX: number, pasteY: number) => {
			if (isEditorLocked) return;
			if (!clipboard) return;
			// Don't attempt to paste elements if a text field is being edited
			if (isEditing) return;

			// Determine the type of clipboard content
			const isBlockPaste =
				clipboard && "block" in clipboard && "children" in clipboard;
			const isChildElementPaste =
				clipboard &&
				"childElement" in clipboard &&
				"originalParentId" in clipboard;

			if (isBlockPaste) {
				const { block: originalBlock, children: originalChildren } =
					clipboard as { block: Element; children: Element[] };
				if (originalBlock.isAddressField) return;

				const newBlockId = uuidv4();
				const newBlock: Element = {
					...originalBlock,
					id: newBlockId,
					x: roundToTwoDecimals(pasteX - originalBlock.width / 2),
					y: pasteY,
					currentPageId: targetPageId,
					childElementIds: [],
				};

				const newChildren: Element[] = [];
				const newChildIds: string[] = [];

				originalChildren.forEach((child) => {
					const newChildId = uuidv4();
					newChildren.push({
						...child,
						id: newChildId,
						parentId: newBlockId,
						currentPageId: targetPageId,
					});
					newChildIds.push(newChildId);
				});
				newBlock.childElementIds = newChildIds;

				setEditorState({
					pages,
					elements: [...elements, newBlock, ...newChildren],
				});
				setSelectedElementId(newBlock.id);
			} else if (isChildElementPaste) {
				// Handle pasting a child element as a standalone element
				const { childElement: originalChild } = clipboard as {
					childElement: Element;
					originalParentId: string;
				};
				if (originalChild.isAddressField) return;

				const newElement: Element = {
					...originalChild,
					id: uuidv4(),
					currentPageId: targetPageId,
					x: roundToTwoDecimals(pasteX - originalChild.width / 2),
					y: pasteY,
					parentId: undefined, // Remove parent relationship to make it standalone
				};

				const elementsOnTargetPage = elements.filter(
					(el) =>
						el.currentPageId === targetPageId &&
						(!el.parentId ||
							(editingBlockId && el.parentId === editingBlockId)),
				);
				const highestZIndex = elementsOnTargetPage.reduce((max, el) => {
					return Math.max(max, el.zIndex !== undefined ? el.zIndex : 1);
				}, 1);

				const finalElement = {
					...newElement,
					zIndex:
						newElement.zIndex !== undefined
							? newElement.zIndex
							: highestZIndex + 1,
				};

				setEditorState({ pages, elements: [...elements, finalElement] });
				setSelectedElementId(finalElement.id);
			} else {
				// Handle pasting a standalone element
				const singleElementToPaste = clipboard as Element;
				if (singleElementToPaste.isAddressField) return;

				const newElement: Element = {
					...singleElementToPaste,
					id: uuidv4(),
					currentPageId: targetPageId,
					x: roundToTwoDecimals(pasteX - singleElementToPaste.width / 2),
					y: pasteY,
				};

				const elementsOnTargetPage = elements.filter(
					(el) =>
						el.currentPageId === targetPageId &&
						(!el.parentId ||
							(editingBlockId && el.parentId === editingBlockId)),
				);
				const highestZIndex = elementsOnTargetPage.reduce((max, el) => {
					return Math.max(max, el.zIndex !== undefined ? el.zIndex : 1);
				}, 1);

				const finalElement = {
					...newElement,
					zIndex:
						newElement.zIndex !== undefined
							? newElement.zIndex
							: highestZIndex + 1,
				};

				setEditorState({ pages, elements: [...elements, finalElement] });
				setSelectedElementId(finalElement.id);
			}
		},
		[
			isEditorLocked,
			clipboard,
			isEditing,
			pages,
			elements,
			setEditorState,
			setSelectedElementId,
			editingBlockId,
		],
	);

	return {
		handleAddElement,
		handleAddElements,
		handleUpdateElement,
		handleDeleteElement,
		handleCopyElement,
		handleCutElement,
		handlePasteAction,
	};
}
