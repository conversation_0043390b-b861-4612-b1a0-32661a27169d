import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import { useHistory } from "../useHistory";

// Mock Date.now to control throttling behavior
const mockDateNow = vi.spyOn(Date, "now");

describe("useHistory", () => {
	beforeEach(() => {
		// Reset the mock date implementation
		mockDateNow.mockReset();
		// Start with a fixed timestamp
		mockDateNow.mockReturnValue(1000);
	});

	// Test initialization
	it("should initialize with the provided initial state", () => {
		const initialState = { count: 0 };
		const { result } = renderHook(() => useHistory(initialState));

		expect(result.current.state).toEqual(initialState);
		expect(result.current.canUndo).toBe(false);
		expect(result.current.canRedo).toBe(false);
	});

	// Test state updates
	describe("setState", () => {
		it("should update state and add to history", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First call should add to history since enough time has passed (throttle starts at 0)
			mockDateNow.mockReturnValue(3000); // More than 2s from initial 1000

			act(() => {
				result.current.setState({ count: 1 });
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(false);
		});

		it("should update state without adding to history when addToHistory is false", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			act(() => {
				result.current.setState({ count: 1 }, false);
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(false); // No history entry added
			expect(result.current.canRedo).toBe(false);
		});

		it("should throttle history updates within the throttle duration", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First update - should add to history (enough time from initial 1000)
			mockDateNow.mockReturnValue(3500);
			act(() => {
				result.current.setState({ count: 1 });
			});

			// Second update within throttle duration - should not add to history
			mockDateNow.mockReturnValue(4500); // 1 second later (less than 2s throttle)
			act(() => {
				result.current.setState({ count: 2 });
			});

			expect(result.current.state).toEqual({ count: 2 });

			// Verify we can only undo once (not twice)
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
		});

		it("should add to history after throttle duration has passed", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First update - should add to history
			mockDateNow.mockReturnValue(3000); // Start with sufficient time
			act(() => {
				result.current.setState({ count: 1 });
			});

			// Second update after throttle duration - should add to history
			mockDateNow.mockReturnValue(6000); // 3 seconds later (more than 2s throttle)
			act(() => {
				result.current.setState({ count: 2 });
			});

			expect(result.current.state).toEqual({ count: 2 });

			// Verify we can undo twice
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(true);

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
		});
	});

	// Test undo functionality
	describe("undo", () => {
		it("should restore previous state", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add some history entries
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000); // After throttle duration
			act(() => {
				result.current.setState({ count: 2 });
			});

			// Undo to previous state
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(true);
		});

		it("should do nothing if there is no history to undo", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
			expect(result.current.canRedo).toBe(false);
		});

		it("should handle multiple undo operations", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add multiple history entries with sufficient time between them
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(9000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			// Undo multiple times
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 2 });

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 });

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
			expect(result.current.canRedo).toBe(true);
		});
	});

	// Test redo functionality
	describe("redo", () => {
		it("should restore undone state", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add history and then undo
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			act(() => {
				result.current.undo();
			});

			// Redo to restore the undone state
			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(false);
		});

		it("should do nothing if there is no state to redo", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
			expect(result.current.canRedo).toBe(false);
		});

		it("should handle multiple redo operations", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add multiple history entries
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(9000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			// Undo multiple times
			act(() => {
				result.current.undo();
				result.current.undo();
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });

			// Redo multiple times
			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 1 });

			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 2 });

			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 3 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(false);
		});
	});

	// Test history optimization
	describe("history optimization", () => {
		it("should clear future history when adding new state", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add history
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			// Undo to create future history
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canRedo).toBe(true);

			// Add new state - should clear future history
			mockDateNow.mockReturnValue(9000); // More than 2s from last update at 6000
			act(() => {
				result.current.setState({ count: 3 });
			});

			expect(result.current.state).toEqual({ count: 3 });
			expect(result.current.canRedo).toBe(false); // Future history should be cleared
		});

		it("should batch operations within throttle duration", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First update - should add to history
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			// Multiple updates within throttle duration - should not add to history
			mockDateNow.mockReturnValue(3500);
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(4000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			mockDateNow.mockReturnValue(4500);
			act(() => {
				result.current.setState({ count: 4 });
			});

			expect(result.current.state).toEqual({ count: 4 });

			// Undo should go directly back to initial state
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
		});
	});
});
