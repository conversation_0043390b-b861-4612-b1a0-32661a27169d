import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import {
	getTopLevelElementsForPage,
	sortElementsByZIndex,
} from "./elementService";

export interface SnapGuide {
	type: "horizontal" | "vertical";
	position: number;
	start: number;
	end: number;
}

export interface SnapGuides {
	visible: boolean;
	guides: SnapGuide[];
	relativeToBlockId?: string;
	blockX?: number;
	blockY?: number;
	blockWidth?: number;
	blockHeight?: number;
}

export interface PageDimensions {
	trimLeftCm: number;
	trimRightCm: number;
	trimTopCm: number;
	trimBottomCm: number;
	outerWidthCm: number;
	outerHeightCm: number;
	pageWidthPx: number;
	pageHeightPx: number;
	trimLeftPx: number;
	trimRightPx: number;
	trimTopPx: number;
	trimBottomPx: number;
	contentWidthPx: number;
	contentHeightPx: number;
}

export interface PageInfo {
	page: PageSettings;
	index: number;
	isFrontPage: boolean;
	isBackPage: boolean;
	isLastPageOfPhysicalPair: boolean;
	dimensions: PageDimensions;
}

export interface SnapGuideStyle {
	position: "absolute";
	backgroundColor: string;
	pointerEvents: "none";
	height?: string;
	width?: string;
	left?: string;
	top?: string;
	transform?: string;
}

// Conversion factor from cm to px
const CM_TO_PX = 3.7795275591;

/**
 * Calculate page dimensions including trim and pixel conversions
 */
export function calculatePageDimensions(page: PageSettings): PageDimensions {
	const trimLeftCm = page.trim_left ?? 0;
	const trimRightCm = page.trim_right ?? 0;
	const trimTopCm = page.trim_top ?? 0;
	const trimBottomCm = page.trim_bottom ?? 0;

	const outerWidthCm = page.width + trimLeftCm + trimRightCm;
	const outerHeightCm = page.height + trimTopCm + trimBottomCm;

	// Convert to pixels
	const pageWidthPx = page.width * 10 * CM_TO_PX;
	const pageHeightPx = page.height * 10 * CM_TO_PX;
	const trimLeftPx = (page.trim_left ?? 0) * 10 * CM_TO_PX;
	const trimRightPx = (page.trim_right ?? 0) * 10 * CM_TO_PX;
	const trimTopPx = (page.trim_top ?? 0) * 10 * CM_TO_PX;
	const trimBottomPx = (page.trim_bottom ?? 0) * 10 * CM_TO_PX;

	const contentWidthPx = pageWidthPx - trimLeftPx - trimRightPx;
	const contentHeightPx = pageHeightPx - trimTopPx - trimBottomPx;

	return {
		trimLeftCm,
		trimRightCm,
		trimTopCm,
		trimBottomCm,
		outerWidthCm,
		outerHeightCm,
		pageWidthPx,
		pageHeightPx,
		trimLeftPx,
		trimRightPx,
		trimTopPx,
		trimBottomPx,
		contentWidthPx,
		contentHeightPx,
	};
}

/**
 * Get page information including type and dimensions
 */
export function getPageInfo(
	page: PageSettings,
	index: number,
	totalPages: number,
): PageInfo {
	const isFrontPage = index % 2 === 0;
	const isBackPage = !isFrontPage;
	const isLastPageOfPhysicalPair = isBackPage || index === totalPages - 1;
	const dimensions = calculatePageDimensions(page);

	return {
		page,
		index,
		isFrontPage,
		isBackPage,
		isLastPageOfPhysicalPair,
		dimensions,
	};
}

/**
 * Get readable format description
 */
export function getFormatDescription(
	formatId: string,
	apiFormats: Array<{ name: string; description: string }>,
): string {
	if (apiFormats && apiFormats.length > 0) {
		const format = apiFormats.find((f) => f.name === formatId);
		if (format) {
			return format.description;
		}
	}
	// If format not found in API formats, create a readable name
	return formatId.replace("_", " ").replace(/\b\w/g, (c) => c.toUpperCase());
}

/**
 * Check if page should show page numbers
 */
export function shouldShowPageNumber(
	page: PageSettings,
	showPageNumbers: boolean,
	deletedPageNumberIds: Set<string>,
): boolean {
	return (
		showPageNumbers &&
		!deletedPageNumberIds.has(page.id) &&
		typeof page.pagenumber_x === "number" &&
		typeof page.pagenumber_y === "number"
	);
}

/**
 * Calculate page number for display
 */
export function calculatePageNumber(pageIndex: number): number {
	return Math.floor(pageIndex / 2) + 1;
}

/**
 * Get elements to render for a specific page
 */
export function getElementsForPage(
	elements: Element[],
	pageId: string,
): Element[] {
	const topLevelElements = getTopLevelElementsForPage(elements, pageId);
	return sortElementsByZIndex(topLevelElements);
}

/**
 * Calculate snap guide styles
 */
export function calculateSnapGuideStyles(
	guide: SnapGuide,
	isHorizontal: boolean,
): SnapGuideStyle {
	const style: SnapGuideStyle = {
		position: "absolute",
		backgroundColor: "#3b82f6", // Blue guideline
		pointerEvents: "none",
	};

	// Guide positions/extents are in mm, relative to their container (block or page content area)
	if (isHorizontal) {
		style.height = "1px";
		style.left = `${guide.start}mm`; // Use start/end for extent
		style.width = `${guide.end - guide.start}mm`;
		style.top = `${guide.position}mm`;
	} else {
		style.width = "1px";
		style.top = `${guide.start}mm`; // Use start/end for extent
		style.height = `${guide.end - guide.start}mm`;
		style.left = `${guide.position}mm`;
	}

	return style;
}

/**
 * Calculate snap guide container style
 */
export function calculateSnapGuideContainerStyle(
	snapGuides: SnapGuides,
	page: PageSettings,
	trimTopCm: number,
	trimLeftCm: number,
): React.CSSProperties {
	if (snapGuides.relativeToBlockId) {
		// Guides are relative to a block
		return {
			top: `${(trimTopCm * 10 + (snapGuides.blockY ?? 0)) / 10}cm`,
			left: `${(trimLeftCm * 10 + (snapGuides.blockX ?? 0)) / 10}cm`,
			width: `${(snapGuides.blockWidth ?? 0) / 10}cm`,
			height: `${(snapGuides.blockHeight ?? 0) / 10}cm`,
			zIndex: 9999,
		};
	} else {
		// Guides are relative to the page
		return {
			top: `${trimTopCm}cm`,
			left: `${trimLeftCm}cm`,
			width: `${page.width}cm`,
			height: `${page.height}cm`,
			zIndex: 9999,
		};
	}
}

/**
 * Check if snap guides should be visible for a page
 */
export function shouldShowSnapGuides(
	snapGuides: SnapGuides,
	currentPage: string | null,
	pageId: string,
	elements: Element[],
): boolean {
	return (
		snapGuides.visible &&
		(currentPage === pageId ||
			elements.find((el) => el.id === snapGuides.relativeToBlockId)
				?.currentPageId === pageId)
	);
}

/**
 * Convert mouse position from page coordinates to content coordinates
 */
export function convertMousePosition(
	clientX: number,
	clientY: number,
	pageElement: HTMLElement,
	trimLeftCm: number,
	trimTopCm: number,
): { x: number; y: number } {
	const rect = pageElement.getBoundingClientRect();

	// Get position relative to the outer container in pixels
	const xPxOuter = clientX - rect.left + pageElement.scrollLeft;
	const yPxOuter = clientY - rect.top + pageElement.scrollTop;

	// Convert outer pixel position to mm
	const xMmOuter = pxToMm(xPxOuter);
	const yMmOuter = pxToMm(yPxOuter);

	// Subtract trim (in mm) to get position relative to the inner content area
	const xMmInner = xMmOuter - trimLeftCm * 10;
	const yMmInner = yMmOuter - trimTopCm * 10;

	return {
		x: roundToTwoDecimals(xMmInner),
		y: roundToTwoDecimals(yMmInner),
	};
}

/**
 * Convert pixels to millimeters
 */
function pxToMm(px: number): number {
	return (px / CM_TO_PX) * 10;
}

/**
 * Round number to two decimal places
 */
function roundToTwoDecimals(num: number): number {
	return Math.round(num * 100) / 100;
}

/**
 * Calculate zoom-adjusted styles
 */
export function calculateZoomStyles(
	zoomLevel: number,
	editingBlockId: string | null,
): React.CSSProperties {
	return {
		transform: `scale(${zoomLevel})`,
		transformOrigin: "top center",
		padding: `${8 * zoomLevel}px`,
		marginBottom: `${50 * zoomLevel}px`,
		pointerEvents: "auto",
		// Lift the entire page content container above the overlay when editing a block
		position: "relative",
		zIndex: editingBlockId ? 5001 : "auto",
	};
}

/**
 * Get page container styles
 */
export function getPageContainerStyles(
	dimensions: PageDimensions,
): React.CSSProperties {
	return {
		width: `${dimensions.outerWidthCm}cm`,
		height: `${dimensions.outerHeightCm}cm`,
		minWidth: `${dimensions.outerWidthCm}cm`,
		flexShrink: 0,
	};
}

/**
 * Get content area styles
 */
export function getContentAreaStyles(
	page: PageSettings,
	dimensions: PageDimensions,
): React.CSSProperties {
	return {
		top: `${dimensions.trimTopCm}cm`,
		left: `${dimensions.trimLeftCm}cm`,
		width: `${page.width}cm`,
		height: `${page.height}cm`,
	};
}

/**
 * Get page number styles
 */
export function getPageNumberStyles(page: PageSettings): React.CSSProperties {
	return {
		position: "absolute",
		left: `${(page.pagenumber_x ?? 0) * 10 * CM_TO_PX}px`,
		top: `${(page.pagenumber_y ?? 0) * 10 * CM_TO_PX}px`,
		fontSize: "12px",
		color: "#666",
		pointerEvents: "none",
		userSelect: "none",
	};
}
