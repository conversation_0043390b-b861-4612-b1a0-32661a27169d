import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import {
	fetchDocument,
	fetchJsonFile,
	type GetPagesResponse,
} from "./apiService";

interface LoadDocumentResult {
	pages: PageSettings[];
	elements: Element[];
	showPageNumbers?: boolean;
	deletedPageNumberIds?: string[];
	role: string;
	allowRelease: boolean;
}

/**
 * Load a document from the API using its ID and version
 * @param id The document ID
 * @param version The document version
 * @returns Promise with the loaded document data
 */
export async function loadDocumentFromApi(
	id: string,
	version: string,
): Promise<LoadDocumentResult> {
	try {
		console.log(`Loading document from API: id=${id}, version=${version}`);
		// Fetch the document data from the API
		const documentData = (await fetchDocument(id, version)) as GetPagesResponse;
		console.log("Document data received:", documentData);

		let resolvedShowPageNumbers: boolean | undefined;
		let resolvedDeletedPageNumberIds: string[] | undefined;
		let pageHashesToLoad: { html: string; json: string }[] = [];

		// Check for the new structure first
		if (
			typeof documentData.pages === "object" &&
			documentData.pages !== null &&
			!Array.isArray(documentData.pages)
		) {
			// New structure: documentData.pages is an object
			resolvedShowPageNumbers = documentData.pages.showPageNumbers;
			resolvedDeletedPageNumberIds = documentData.pages.deletedPageNumberIds;
			pageHashesToLoad = documentData.pages.hashes || [];
			console.log("Detected new document structure.");
		} else if (Array.isArray(documentData.pages)) {
			// Old structure: documentData.pages is an array
			resolvedShowPageNumbers = documentData.showPageNumbers; // showPageNumbers was top-level
			resolvedDeletedPageNumberIds = documentData.deletedPageNumberIds; // deletedPageNumberIds was top-level
			pageHashesToLoad = documentData.pages;
			console.log("Detected old document structure.");
		} else {
			// Fallback or unrecognized structure
			console.warn(
				"Unrecognized document structure for pages/showPageNumbers.",
			);
			pageHashesToLoad = [];
			resolvedShowPageNumbers = false; // Default to false
			resolvedDeletedPageNumberIds = undefined;
		}

		// Initialize the result object
		const result: LoadDocumentResult = {
			pages: [],
			elements: [],
			showPageNumbers: resolvedShowPageNumbers,
			deletedPageNumberIds: resolvedDeletedPageNumberIds,
			role: documentData.role,
			allowRelease: documentData.allowRelease === 1,
		};

		// Load JSON files for each page
		for (let i = 0; i < pageHashesToLoad.length; i++) {
			const pageHashInfo = pageHashesToLoad[i];
			console.log(
				`Loading page ${i + 1}/${pageHashesToLoad.length}, JSON hash: ${pageHashInfo.json}`,
			);

			try {
				// Fetch JSON content for the page
				const jsonData = (await fetchJsonFile(pageHashInfo.json)) as {
					page?: PageSettings;
					elements?: Element[];
				};
				console.log(`Page ${i + 1} JSON data:`, jsonData);

				if (jsonData) {
					if (jsonData.page) {
						// Preserve the original page ID instead of generating a new one
						const pageSettings: PageSettings = {
							...jsonData.page,
							// Keep the original id from jsonData.page
						};
						console.log(`Added page with ID: ${pageSettings.id}`);
						result.pages.push(pageSettings);

						// Add elements from this page
						if (jsonData.elements && Array.isArray(jsonData.elements)) {
							// Preserve original element IDs and their page references
							const pageElements = jsonData.elements.map(
								(element: Element) => ({
									...element,
									// Keep the original element id and currentPageId
								}),
							);

							console.log(
								`Added ${pageElements.length} elements from page ${pageSettings.id}`,
							);
							result.elements = [...result.elements, ...pageElements];
						}
					}
				}
			} catch (error) {
				console.error(`Failed to load JSON for page ${i}:`, error);
			}
		}

		console.log("Document loading completed:", result);
		return result;
	} catch (error) {
		console.error("Failed to load document:", error);
		throw error;
	}
}
