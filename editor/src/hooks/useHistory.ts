import { useCallback, useRef, useState } from "react";

interface HistoryState<T> {
	past: T[];
	present: T;
	future: T[];
}

export function useHistory<T>(initialState: T) {
	const [history, setHistory] = useState<HistoryState<T>>({
		past: [],
		present: initialState,
		future: [],
	});

	// Add a timestamp reference to track the last time a state was added to history
	const lastHistoryUpdateRef = useRef<number>(0);
	// Throttle duration in milliseconds (2 seconds)
	const THROTTLE_DURATION = 2000;

	const setState = useCallback(
		(newPresent: T, addToHistory: boolean = true) => {
			setHistory((prev) => {
				// Skip if no actual change
				if (JSON.stringify(prev.present) === JSON.stringify(newPresent)) {
					return prev;
				}
				// If we shouldn't add to history, just update the present state
				if (!addToHistory) {
					return {
						...prev,
						present: newPresent,
					};
				}

				// Get current timestamp
				const now = Date.now();

				// Check if we should throttle adding to history
				// Only add to history if more than THROTTLE_DURATION has passed since the last update
				const shouldAddToHistory =
					now - lastHistoryUpdateRef.current >= THROTTLE_DURATION;

				// Always update the timestamp reference when addToHistory is true
				lastHistoryUpdateRef.current = now;

				// If we're throttling and not enough time has passed, just update present without adding to history
				if (!shouldAddToHistory) {
					return {
						...prev,
						present: newPresent,
					};
				}

				// Add to history
				return {
					past: [...prev.past, prev.present],
					present: newPresent,
					future: [], // Clear future when adding new history entry
				};
			});
		},
		[],
	);

	const undo = useCallback(() => {
		setHistory((prev) => {
			if (prev.past.length === 0) return prev;

			// Take the last item from past as new present
			const previous = prev.past[prev.past.length - 1];
			const newPast = prev.past.slice(0, -1);

			return {
				past: newPast,
				present: previous,
				future: [prev.present, ...prev.future],
			};
		});
	}, []);

	const redo = useCallback(() => {
		setHistory((prev) => {
			if (prev.future.length === 0) return prev;

			const next = prev.future[0];
			const newFuture = prev.future.slice(1);

			return {
				past: [...prev.past, prev.present],
				present: next,
				future: newFuture,
			};
		});
	}, []);

	return {
		state: history.present,
		setState,
		undo,
		redo,
		canUndo: history.past.length > 0,
		canRedo: history.future.length > 0,
	};
}
