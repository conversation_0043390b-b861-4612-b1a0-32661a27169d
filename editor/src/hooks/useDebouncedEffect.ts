import { useEffect, useRef } from "react";

/**
 * Debounced side-effect that behaves like useEffect but only executes the
 * latest provided callback after the specified delay. The callback itself is
 * kept in a ref so that changes in it do NOT re-arm the debounce timer every
 * render – only changes in the explicit dependency list do.
 */
function useDebouncedEffect(
	callback: () => void,
	delay: number,
	deps: React.DependencyList,
) {
	const callbackRef = useRef(callback);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Always keep the ref up-to-date with the latest callback
	callbackRef.current = callback;

	useEffect(() => {
		if (timeoutRef.current) clearTimeout(timeoutRef.current);

		timeoutRef.current = setTimeout(() => {
			callbackRef.current();
		}, delay);

		return () => {
			if (timeoutRef.current) clearTimeout(timeoutRef.current);
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [...deps, delay]);
}

export default useDebouncedEffect;
