import { act, renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useElementOperations } from "../../hooks/useElementOperations";
import { usePageOperations } from "../../hooks/usePageOperations";
import type { Element } from "../../types/element";
import type { PageSettings } from "../../types/page";
import type { PageFormat } from "../../utils/apiService";
import { ElementFactory, PageFactory } from "../utils/mock-factories";

// Mock uuid to have predictable IDs in tests
vi.mock("uuid", () => ({
	v4: vi.fn(() => `test-uuid-${Math.random().toString(36).substring(2, 9)}`),
}));

describe("Page Management Integration Tests", () => {
	let mockPages: PageSettings[];
	let mockElements: Element[];
	let mockSetEditorState: ReturnType<typeof vi.fn>;
	let mockSetCurrentPage: ReturnType<typeof vi.fn>;
	let mockApiFormats: PageFormat[];

	beforeEach(() => {
		// Create initial test state
		mockPages = [
			PageFactory.createPage({ id: "page-1" }),
			PageFactory.createPage({ id: "page-2" }),
		];
		mockElements = [
			ElementFactory.createTextElement({
				id: "text-1",
				currentPageId: "page-1",
			}),
			ElementFactory.createShapeElement({
				id: "shape-1",
				currentPageId: "page-1",
			}),
			ElementFactory.createTextElement({
				id: "text-2",
				currentPageId: "page-2",
			}),
		];

		// Create mock functions
		mockSetEditorState = vi.fn();
		mockSetCurrentPage = vi.fn();

		// Mock API formats
		mockApiFormats = [
			{
				name: "A4",
				description: "A4 Portrait",
				width: 210, // mm
				height: 297, // mm
				trim_top: 5,
				trim_right: 5,
				trim_bottom: 5,
				trim_left: 5,
				address_x: 20,
				address_y: 50,
				pagenumber_w: 105,
				pagenumber_y: 280,
			},
			{
				name: "A5",
				description: "A5 Portrait",
				width: 148, // mm
				height: 210, // mm
				trim_top: 3,
				trim_right: 3,
				trim_bottom: 3,
				trim_left: 3,
				address_x: 15,
				address_y: 40,
				pagenumber_w: 74,
				pagenumber_y: 195,
			},
		];
	});

	describe("Page Creation → Element Addition → Page Deletion Workflows", () => {
		it("should handle complete page lifecycle with elements", () => {
			const { result: pageOpsResult } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Step 1: Create a new page
			act(() => {
				pageOpsResult.current.handleAddPage("A4");
			});

			// Verify pages were added (front and back)
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: expect.arrayContaining([
						...mockPages,
						expect.objectContaining({
							format: "A4",
							orientation: "portrait",
							width: 21, // cm
							height: 29.7, // cm
						}),
						expect.objectContaining({
							format: "A4",
							orientation: "portrait",
							width: 21, // cm
							height: 29.7, // cm
						}),
					]),
					elements: mockElements,
				}),
			);

			// Verify current page was set to the new front page
			expect(mockSetCurrentPage).toHaveBeenCalled();

			// Step 2: Add elements to the new page
			const newPageId = "test-uuid-new-page"; // Mock UUID
			const newElements = [
				...mockElements,
				ElementFactory.createTextElement({
					id: "new-text",
					currentPageId: newPageId,
					content: "Text on new page",
				}),
				ElementFactory.createShapeElement({
					id: "new-shape",
					currentPageId: newPageId,
				}),
			];

			// Simulate adding elements (this would typically be done through element operations)
			// Update the editor state with new elements
			mockSetEditorState.mockClear();
			const updatedPages = [
				...mockPages,
				PageFactory.createPage({ id: newPageId }),
				PageFactory.createPage({ id: "back-page-id" }),
			];

			// Step 3: Delete the page with elements
			const { result: pageOpsWithNew } = renderHook(() =>
				usePageOperations({
					pages: updatedPages,
					elements: newElements,
					setEditorState: mockSetEditorState,
					currentPage: newPageId,
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			act(() => {
				pageOpsWithNew.current.handleDeletePage(newPageId);
			});

			// Should remove the page and its elements
			expect(mockSetEditorState).toHaveBeenLastCalledWith(
				expect.objectContaining({
					pages: expect.not.arrayContaining([
						expect.objectContaining({ id: newPageId }),
					]),
					elements: expect.not.arrayContaining([
						expect.objectContaining({ currentPageId: newPageId }),
					]),
				}),
			);
		});

		it("should handle page creation with different formats", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Create A5 page
			act(() => {
				result.current.handleAddPage("A5");
			});

			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: expect.arrayContaining([
						expect.objectContaining({
							format: "A5",
							width: 14.8, // cm
							height: 21, // cm
						}),
					]),
				}),
			);
		});

		it("should handle page creation at specific index", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Insert page at index 1
			act(() => {
				result.current.handleAddPage("A4", 1);
			});

			const callArgs = mockSetEditorState.mock.calls[0][0];
			const newPages = callArgs.pages;

			// Should have inserted at index 1 and 2 (front and back)
			expect(newPages.length).toBe(mockPages.length + 2);
			expect(newPages[1]).toMatchObject({ format: "A4" });
			expect(newPages[2]).toMatchObject({ format: "A4" });
		});

		it("should handle page deletion with element migration", () => {
			// Create pages where one has elements and its pair doesn't
			const pagesWithPair = [
				PageFactory.createPage({ id: "front-page" }),
				PageFactory.createPage({ id: "back-page" }),
			];

			const elementsOnFront = [
				ElementFactory.createTextElement({
					id: "text-front",
					currentPageId: "front-page",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					pages: pagesWithPair,
					elements: elementsOnFront,
					setEditorState: mockSetEditorState,
					currentPage: "front-page",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Delete the back page (which has no elements)
			act(() => {
				result.current.handleDeletePage("back-page");
			});

			// Should only clear elements from back page since front page has elements
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: pagesWithPair, // Pages should remain
				}),
			);
			// Elements should still contain the front page element
			expect(mockSetEditorState.mock.calls[0][0].elements).toContainEqual(
				expect.objectContaining({ id: "text-front" }),
			);
		});

		it("should handle page deletion when other page has elements", () => {
			const pagesWithPair = [
				PageFactory.createPage({ id: "front-page" }),
				PageFactory.createPage({ id: "back-page" }),
			];

			const elementsOnBoth = [
				ElementFactory.createTextElement({
					id: "text-front",
					currentPageId: "front-page",
				}),
				ElementFactory.createShapeElement({
					id: "shape-back",
					currentPageId: "back-page",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					pages: pagesWithPair,
					elements: elementsOnBoth,
					setEditorState: mockSetEditorState,
					currentPage: "front-page",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Delete the front page (back page has elements)
			act(() => {
				result.current.handleDeletePage("front-page");
			});

			// Should only remove elements from front page, keep both pages
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: pagesWithPair, // Pages remain unchanged
					elements: [
						expect.objectContaining({
							id: "shape-back",
							currentPageId: "back-page",
						}),
					],
				}),
			);
		});
	});

	describe("Page Reordering and Element Migration", () => {
		it("should handle page reordering with element preservation", () => {
			// Create 4 pages (2 pairs)
			const multiplePages = [
				PageFactory.createPage({ id: "page-1" }),
				PageFactory.createPage({ id: "page-2" }),
				PageFactory.createPage({ id: "page-3" }),
				PageFactory.createPage({ id: "page-4" }),
			];

			const elementsOnPages = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "page-1",
					content: "Page 1 content",
				}),
				ElementFactory.createTextElement({
					id: "text-3",
					currentPageId: "page-3",
					content: "Page 3 content",
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					pages: multiplePages,
					elements: elementsOnPages,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Reorder pages: move first pair (pages 0,1) to position of second pair
			act(() => {
				result.current.handleReorderPages(0, 2);
			});

			const callArgs = mockSetEditorState.mock.calls[0][0];
			const reorderedPages = callArgs.pages;

			// Pages should be reordered but elements should remain with their pages
			expect(reorderedPages.length).toBe(4);
			expect(callArgs.elements).toEqual(elementsOnPages); // Elements unchanged
		});

		it("should handle single page swapping", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Swap pages at index 0 and 1
			act(() => {
				result.current.handleSwapPages(0, 1);
			});

			const callArgs = mockSetEditorState.mock.calls[0][0];
			const swappedPages = callArgs.pages;

			// Pages should be swapped
			expect(swappedPages[0].id).toBe(mockPages[1].id);
			expect(swappedPages[1].id).toBe(mockPages[0].id);
			expect(callArgs.elements).toEqual(mockElements); // Elements unchanged
		});

		it("should handle invalid page reordering gracefully", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Try to swap with invalid indices
			act(() => {
				result.current.handleSwapPages(-1, 5);
			});

			// Should not call setEditorState for invalid operations
			expect(mockSetEditorState).not.toHaveBeenCalled();

			// Try to reorder to same position
			act(() => {
				result.current.handleReorderPages(0, 0);
			});

			// Should not call setEditorState for no-op operations
			expect(mockSetEditorState).not.toHaveBeenCalled();
		});
	});

	describe("Page Management Edge Cases", () => {
		it("should handle locked editor state", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: true, // Editor is locked
					apiFormats: mockApiFormats,
				}),
			);

			// Try to add page when locked
			act(() => {
				result.current.handleAddPage("A4");
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();

			// Try to delete page when locked
			act(() => {
				result.current.handleDeletePage("page-1");
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();

			// Try to reorder pages when locked
			act(() => {
				result.current.handleReorderPages(0, 1);
			});

			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should handle page creation with missing API formats", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: [], // No API formats available
				}),
			);

			// Should fall back to static formats
			act(() => {
				result.current.handleAddPage("A4");
			});

			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: expect.arrayContaining([
						expect.objectContaining({
							format: "A4",
							width: 21, // Fallback A4 width in cm
							height: 29.7, // Fallback A4 height in cm
						}),
					]),
				}),
			);
		});

		it("should handle page creation with unknown format", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Try to create page with unknown format
			act(() => {
				result.current.handleAddPage("UNKNOWN_FORMAT");
			});

			// Should create page with fallback dimensions for unknown format
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: expect.arrayContaining([
						expect.objectContaining({
							format: "UNKNOWN_FORMAT",
							width: 21, // Fallback A4 width in cm
							height: 29.7, // Fallback A4 height in cm
						}),
					]),
				}),
			);
		});

		it("should handle deletion of non-existent page", () => {
			const { result } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Try to delete non-existent page
			act(() => {
				result.current.handleDeletePage("non-existent-page");
			});

			// Should not modify state
			expect(mockSetEditorState).not.toHaveBeenCalled();
		});

		it("should handle page number elements during deletion", () => {
			const pagesWithPair = [
				PageFactory.createPage({ id: "front-page" }),
				PageFactory.createPage({ id: "back-page" }),
			];

			const elementsWithPageNumbers = [
				ElementFactory.createTextElement({
					id: "text-front",
					currentPageId: "front-page",
				}),
				ElementFactory.createTextElement({
					id: "page-num-back",
					currentPageId: "back-page",
					isPageNumber: true, // This is a page number element
				}),
			];

			const { result } = renderHook(() =>
				usePageOperations({
					pages: pagesWithPair,
					elements: elementsWithPageNumbers,
					setEditorState: mockSetEditorState,
					currentPage: "front-page",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			// Delete the front page - back page only has page number (should be ignored)
			act(() => {
				result.current.handleDeletePage("front-page");
			});

			// Should delete both pages since back page only has page number elements
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: [],
					elements: [],
				}),
			);
		});
	});

	describe("Complex Page and Element Integration", () => {
		it("should handle complete workflow: create pages, add elements, reorder, delete", () => {
			let currentPages = [...mockPages];
			let currentElements = [...mockElements];

			// Mock setEditorState to update our local state
			const mockSetEditorStateWithUpdate = vi.fn((newState) => {
				currentPages = newState.pages;
				currentElements = newState.elements;
			});

			// Step 1: Create new pages
			const { result: step1Result } = renderHook(() =>
				usePageOperations({
					pages: currentPages,
					elements: currentElements,
					setEditorState: mockSetEditorStateWithUpdate,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			act(() => {
				step1Result.current.handleAddPage("A5");
			});

			expect(currentPages.length).toBe(4); // Original 2 + new 2

			// Step 2: Add elements to new pages (simulate)
			const newPageId = currentPages[2].id;
			currentElements = [
				...currentElements,
				ElementFactory.createTextElement({
					id: "new-text",
					currentPageId: newPageId,
					content: "New page content",
				}),
			];

			// Step 3: Reorder pages
			const { result: step3Result } = renderHook(() =>
				usePageOperations({
					pages: currentPages,
					elements: currentElements,
					setEditorState: mockSetEditorStateWithUpdate,
					currentPage: newPageId,
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			act(() => {
				step3Result.current.handleReorderPages(2, 0); // Move new pages to front
			});

			// Verify reordering happened
			expect(mockSetEditorStateWithUpdate).toHaveBeenCalled();

			// Step 4: Delete a page
			act(() => {
				step3Result.current.handleDeletePage(currentPages[0].id);
			});

			// Verify deletion happened
			expect(mockSetEditorStateWithUpdate).toHaveBeenCalled();
		});
	});

	describe("Integration with Element Operations", () => {
		it("should handle adding elements to newly created pages", () => {
			// Create a mock for element operations
			const mockSetSelectedElementId = vi.fn();
			const mockSetEditingBlockId = vi.fn();
			const mockSetDeletedPageNumberIds = vi.fn();
			const mockSetClipboard = vi.fn();
			const mockCommitBatch = vi.fn();
			const mockBeginBatch = vi.fn();
			const mockUpdateDraft = vi.fn();
			const mockIsBatching = vi.fn().mockReturnValue(false);

			// Step 1: Create new pages
			const { result: pageOpsResult } = renderHook(() =>
				usePageOperations({
					pages: mockPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			act(() => {
				pageOpsResult.current.handleAddPage("A4");
			});

			// Extract the new pages from the mock call
			const newPagesCall = mockSetEditorState.mock.calls[0][0];
			const updatedPages = newPagesCall.pages;
			const newPageId = updatedPages[2].id; // The newly created front page

			// Step 2: Add elements to the new page using element operations
			const { result: elementOpsResult } = renderHook(() =>
				useElementOperations({
					pages: updatedPages,
					elements: mockElements,
					setEditorState: mockSetEditorState,
					commitBatch: mockCommitBatch,
					beginBatch: mockBeginBatch,
					updateDraft: mockUpdateDraft,
					isBatching: mockIsBatching,
					currentPage: newPageId,
					editingBlockId: null,
					selectedElementId: null,
					setSelectedElementId: mockSetSelectedElementId,
					setEditingBlockId: mockSetEditingBlockId,
					setDeletedPageNumberIds: mockSetDeletedPageNumberIds,
					isEditorLocked: false,
					clipboard: null,
					setClipboard: mockSetClipboard,
					isEditing: false,
				}),
			);

			// Create a new element for the new page
			const newElement = ElementFactory.createTextElement({
				id: "new-page-text",
				currentPageId: newPageId,
				content: "Content on new page",
			});

			// Reset the mock to clear previous calls
			mockSetEditorState.mockClear();

			// Add the element to the new page
			act(() => {
				elementOpsResult.current.handleAddElement(newElement);
			});

			// Verify the element was added to the new page
			expect(mockSetEditorState).toHaveBeenCalledWith(
				expect.objectContaining({
					pages: updatedPages,
					elements: [...mockElements, newElement],
				}),
			);
			expect(mockSetSelectedElementId).toHaveBeenCalledWith("new-page-text");
		});

		it("should handle element migration when reordering pages", () => {
			// Create pages with elements
			const pages = [
				PageFactory.createPage({ id: "page-1" }),
				PageFactory.createPage({ id: "page-2" }),
				PageFactory.createPage({ id: "page-3" }),
				PageFactory.createPage({ id: "page-4" }),
			];

			const elements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "page-1",
				}),
				ElementFactory.createShapeElement({
					id: "shape-2",
					currentPageId: "page-2",
				}),
				ElementFactory.createImageElement({
					id: "image-3",
					currentPageId: "page-3",
				}),
			];

			// Create a mock that captures the updated state
			let updatedPages: PageSettings[] = [];
			let updatedElements: Element[] = [];

			const mockSetEditorStateWithCapture = vi.fn((newState) => {
				updatedPages = newState.pages;
				updatedElements = newState.elements;
			});

			// Step 1: Reorder pages
			const { result } = renderHook(() =>
				usePageOperations({
					pages,
					elements,
					setEditorState: mockSetEditorStateWithCapture,
					currentPage: "page-1",
					setCurrentPage: mockSetCurrentPage,
					isEditorLocked: false,
					apiFormats: mockApiFormats,
				}),
			);

			act(() => {
				result.current.handleReorderPages(0, 2); // Move pages 0,1 to position 2,3
			});

			// Verify pages were reordered - the exact order depends on the implementation
			// Instead of checking exact order, just verify that the pages were reordered
			expect(updatedPages.length).toBe(4);
			expect(updatedPages.map((p) => p.id)).toContain("page-1");
			expect(updatedPages.map((p) => p.id)).toContain("page-2");
			expect(updatedPages.map((p) => p.id)).toContain("page-3");
			expect(updatedPages.map((p) => p.id)).toContain("page-4");

			// Verify elements stayed with their pages
			const text1Element = updatedElements.find((el) => el.id === "text-1");
			const shape2Element = updatedElements.find((el) => el.id === "shape-2");
			const image3Element = updatedElements.find((el) => el.id === "image-3");

			expect(text1Element?.currentPageId).toBe("page-1");
			expect(shape2Element?.currentPageId).toBe("page-2");
			expect(image3Element?.currentPageId).toBe("page-3");
		});
	});
});
