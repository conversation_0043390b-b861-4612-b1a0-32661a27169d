import type { Editor } from "@tiptap/react";
import { useEffect, useRef, useState } from "react";
import type { Element } from "@/types/element";
import type { ColorInfo } from "@/utils/apiService";
import { Input } from "./input";
import { Label } from "./label";

interface ColorPickerProps {
	element?: Element | null;
	onUpdate?: (updatedElement: Element) => void;
	editor?: Editor | null;
	label?: string;
	currentColor?: string;
	onColorChange?: (color: string) => void;
	apiColors: ColorInfo[]; // Now passed as props instead of fetched internally
	isLoadingColors: boolean;
	colorError: string | null;
}

export function ColorPicker({
	element,
	onUpdate,
	editor,
	label = "Farbe",
	currentColor,
	onColorChange,
	apiColors,
	isLoadingColors,
	colorError,
}: ColorPickerProps) {
	const [color, setColor] = useState<string>("#000000");
	const colorInputRef = useRef<HTMLInputElement>(null);

	// Initialize color
	useEffect(() => {
		if (currentColor !== undefined) {
			setColor(currentColor);
		} else if (element?.backgroundColor) {
			setColor(element.backgroundColor);
		} else if (editor) {
			const currentEditorColor = editor.getAttributes("textStyle")?.color;
			if (currentEditorColor) {
				setColor(currentEditorColor);
			} else {
				// Find eprimo Wasserblau in apiColors, or use default black if not found
				const eprimoWasserblau = apiColors.find(
					(c) => c.name === "eprimo Wasserblau",
				);
				setColor(eprimoWasserblau?.hex || "#000000");

				// Apply the default color to the editor if no color is set
				if (editor && eprimoWasserblau) {
					editor.chain().focus().setColor(eprimoWasserblau.hex).run();
				}
			}
		} else {
			// For non-editor contexts, still use eprimo Wasserblau if available
			const eprimoWasserblau = apiColors.find(
				(c) => c.name === "eprimo Wasserblau",
			);
			setColor(eprimoWasserblau?.hex || "#000000");
		}
	}, [element, editor, currentColor, apiColors]);

	const handleColorChangeInternal = (newColor: string) => {
		setColor(newColor);

		if (onColorChange) {
			onColorChange(newColor);
		} else if (element && onUpdate) {
			onUpdate({
				...element,
				backgroundColor: newColor,
			});
		} else if (editor) {
			editor.chain().focus().setColor(newColor).run();
		}
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		handleColorChangeInternal(e.target.value);
	};

	// Determine if the color picker should be disabled
	const isEnabled =
		onColorChange || (element && element.type === "shape") || editor;
	const isDisabled = !isEnabled;

	// Function to open the OS color picker
	const openColorPicker = () => {
		if (!isDisabled && colorInputRef.current) {
			colorInputRef.current.click();
		}
	};

	return (
		<div className="space-y-4">
			<Label className="text-sm font-medium">{label}</Label>

			<div className="grid grid-cols-5 gap-2">
				{isLoadingColors ? (
					<div className="col-span-5 py-2 text-center text-sm text-gray-500">
						Farben werden geladen...
					</div>
				) : colorError ? (
					<div className="col-span-5 py-2 text-center text-sm text-red-500">
						{colorError}
					</div>
				) : apiColors.length === 0 ? (
					<div className="col-span-5 py-2 text-center text-sm text-orange-500">
						Keine Farben verfügbar.
					</div>
				) : (
					apiColors.map((colorInfo) => (
						<button
							key={colorInfo.hex}
							className={`h-8 w-8 rounded-md border ${color === colorInfo.hex ? "border-blue-500 ring-2 ring-blue-300" : "border-gray-300"} ${isDisabled ? "opacity-50 cursor-not-allowed" : ""}`}
							style={{ backgroundColor: colorInfo.hex }}
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								if (!isDisabled) {
									handleColorChangeInternal(colorInfo.hex);
								}
							}}
							type="button"
							disabled={isDisabled}
							title={colorInfo.name}
						/>
					))
				)}
			</div>

			<div className="flex items-center space-x-2">
				<button
					type="button"
					className={`h-8 w-8 rounded-md border border-gray-300 ${isDisabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:ring-2 hover:ring-blue-300"}`}
					style={{ backgroundColor: color }}
					onClick={openColorPicker}
					disabled={isDisabled}
					title="Farbauswahl öffnen"
				/>
				<Input
					type="text"
					value={color}
					onChange={handleInputChange}
					className="h-8"
					placeholder="#RRGGBB"
					disabled={isDisabled}
				/>
				<Input
					ref={colorInputRef}
					type="color"
					value={color}
					onChange={handleInputChange}
					className="h-0 w-0 p-0 border-0 absolute opacity-0"
					disabled={isDisabled}
					aria-hidden="true"
				/>
			</div>
		</div>
	);
}
