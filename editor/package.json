{"name": "ts-editor-integration", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc -b && vite build --base /editor --sourcemap --outDir ./dist --emptyOutDir", "dev-build": "npm run build -- -w", "lint": "biome check --write", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:setup": "vitest run src/__tests__/setup.test.tsx", "test:playwright": "playwright test --reporter list"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-strike": "^2.11.5", "@tiptap/extension-subscript": "^2.11.7", "@tiptap/extension-superscript": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.5", "@types/file-saver": "^2.0.7", "@types/jest": "^30.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "file-saver": "^2.0.5", "jszip": "^3.10.1", "lucide-react": "^0.522.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tiptap": "file:../../../../../extension-color", "uuid": "^11.0.5"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@eslint/js": "^9.17.0", "@playwright/test": "^1.53.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.13.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^16.2.0", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "postcss": "^8.5.1", "react-error-boundary": "^6.0.0", "sass-embedded": "^1.87.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.18.2", "vite": "^6.3.5", "vitest": "^3.2.4"}}