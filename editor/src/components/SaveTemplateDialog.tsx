import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import type { Element } from "@/types/element";
import { saveTemplate } from "@/utils/apiService";

interface SaveTemplateDialogProps {
	isOpen: boolean;
	onClose: () => void;
	blockData: { blockElement: Element; childElements: Element[] } | null;
}

export function SaveTemplateDialog({
	isOpen,
	onClose,
	blockData,
}: SaveTemplateDialogProps) {
	const [templateName, setTemplateName] = useState("");
	const [isSaving, setIsSaving] = useState(false);
	const { toast } = useToast();

	const handleSave = async () => {
		if (!templateName.trim()) {
			toast({
				title: "<PERSON><PERSON>",
				description: "Bitte geben Sie einen Namen für das Template ein.",
				variant: "destructive",
			});
			return;
		}

		setIsSaving(true);
		try {
			if (!blockData) {
				toast({
					title: "Fehler",
					description: "Keine Daten zum Speichern übergeben.",
					variant: "destructive",
				});
				return;
			}
			await saveTemplate(templateName.trim(), blockData);
			toast({
				title: "Erfolgreich",
				description: "Template wurde erfolgreich gespeichert!",
				variant: "default",
			});
			setTemplateName("");
			onClose();
		} catch (error) {
			console.error("Failed to save template:", error);
			toast({
				title: "Fehler",
				description: "Fehler beim Speichern des Templates!",
				variant: "destructive",
			});
		} finally {
			setIsSaving(false);
		}
	};

	const handleClose = () => {
		setTemplateName("");
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent role="dialog" className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Als Template speichern</DialogTitle>
				</DialogHeader>
				<div className="grid gap-4 py-4">
					<div className="grid gap-2">
						<Label htmlFor="template-name">Template Name</Label>
						<Input
							id="template-name"
							value={templateName}
							onChange={(e) => setTemplateName(e.target.value)}
							placeholder="Template Name eingeben..."
							onKeyDown={(e) => {
								if (e.key === "Enter" && !isSaving) {
									handleSave();
								}
							}}
						/>
					</div>
				</div>
				<div className="flex justify-end gap-2">
					<Button variant="outline" onClick={handleClose} disabled={isSaving}>
						Abbrechen
					</Button>
					<Button
						onClick={handleSave}
						disabled={isSaving || !templateName.trim()}
					>
						{isSaving ? "Speichert..." : "Speichern"}
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
}
