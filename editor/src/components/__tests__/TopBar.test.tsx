import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";

import { TopBar } from "../TopBar";

// Mock the toast hook
vi.mock("@/hooks/use-toast", () => ({
	useToast: () => ({
		toast: vi.fn(),
	}),
}));

// Mock the API service functions
vi.mock("@/utils/apiService", () => ({
	saveDocumentToAPI: vi.fn().mockResolvedValue({ success: true }),
	setFrontifyFlag: vi.fn().mockResolvedValue({ success: true }),
	deleteFrontifyFlag: vi.fn().mockResolvedValue({ success: true }),
}));

// Mock the export utils
vi.mock("@/utils/exportUtils", () => ({
	exportToZip: vi.fn().mockResolvedValue(undefined),
	importFromZip: vi.fn().mockResolvedValue({ pages: [], elements: [] }),
}));

// Mock the ExportPreview component
vi.mock("../ExportPreview", () => ({
	ExportPreview: () => (
		<div data-testid="export-preview-mock">Export Preview</div>
	),
}));

describe("TopBar component", () => {
	// Common props for all tests
	const mockOnUndo = vi.fn();
	const mockOnRedo = vi.fn();
	const mockSetHighlightVariables = vi.fn();
	const mockSetSelectedTestDataIndex = vi.fn();
	const mockSetEditorState = vi.fn();
	const mockSetIsEditorLocked = vi.fn();
	const mockSetIsReleased = vi.fn();

	const defaultProps = {
		documentId: "doc-123",
		pages: [],
		elements: [],
		onUndo: mockOnUndo,
		onRedo: mockOnRedo,
		canUndo: true,
		canRedo: true,
		highlightVariables: false,
		setHighlightVariables: mockSetHighlightVariables,
		showPageNumbers: false,
		deletedPageNumberIds: new Set<string>(),
		testData: [],
		selectedTestDataIndex: -1,
		setSelectedTestDataIndex: mockSetSelectedTestDataIndex,
		setEditorState: mockSetEditorState,
		isEditorLocked: false,
		setIsEditorLocked: mockSetIsEditorLocked,
		isReleased: false,
		setIsReleased: mockSetIsReleased,
	};

	it("renders with basic controls", () => {
		render(<TopBar {...defaultProps} />);

		// Check for basic controls
		expect(screen.getByText("Variablen Hervorheben")).toBeInTheDocument();
		expect(screen.getByText("Speichern")).toBeInTheDocument();
		expect(screen.getByText("Vorschau")).toBeInTheDocument();

		// Check for undo/redo buttons
		expect(screen.getByTitle(/Rückgängig/)).toBeInTheDocument();
		expect(screen.getByTitle(/Wiederherstellen/)).toBeInTheDocument();
	});

	it("disables undo/redo buttons when canUndo/canRedo are false", () => {
		render(<TopBar {...defaultProps} canUndo={false} canRedo={false} />);

		const undoButton = screen.getByTitle(/Rückgängig/);
		const redoButton = screen.getByTitle(/Wiederherstellen/);

		expect(undoButton).toBeDisabled();
		expect(redoButton).toBeDisabled();
	});

	it("toggles highlight variables when toggle is clicked", async () => {
		const user = userEvent.setup();
		render(<TopBar {...defaultProps} />);

		const toggle = screen.getByText("Variablen Hervorheben").closest("button");
		expect(toggle).toBeInTheDocument();

		if (toggle) {
			await user.click(toggle);
			expect(mockSetHighlightVariables).toHaveBeenCalledWith(true);
		}
	});

	it("shows test data selector when test data is available", () => {
		const testData = [
			{ id: "1", name: "Test 1" },
			{ id: "2", name: "Test 2" },
		];

		render(<TopBar {...defaultProps} testData={testData} />);

		// The select should be rendered
		expect(screen.getByText("Keine Testdaten")).toBeInTheDocument();
	});

	it("shows preview when preview button is clicked", async () => {
		const user = userEvent.setup();
		render(<TopBar {...defaultProps} />);

		const previewButton = screen.getByText("Vorschau").closest("button");
		expect(previewButton).toBeInTheDocument();

		if (previewButton) {
			await user.click(previewButton);
		}

		// The export preview should be shown
		expect(screen.getByTestId("export-preview-mock")).toBeInTheDocument();
	});

	it("shows back button when backId is in URL", () => {
		// Mock URL search params
		const originalLocation = window.location;
		// Instead of deleting window.location, mock the search property
		Object.defineProperty(window, "location", {
			value: { ...originalLocation, search: "?id=back-123" },
			writable: true,
		});

		render(<TopBar {...defaultProps} />);

		expect(screen.getByText("← Zur Projektübersicht")).toBeInTheDocument();

		// Restore original location
		Object.defineProperty(window, "location", {
			value: originalLocation,
			writable: true,
		});
	});

	it("handles undo/redo clicks", async () => {
		const user = userEvent.setup();
		render(<TopBar {...defaultProps} />);

		const undoButton = screen.getByTitle(/Rückgängig/);
		const redoButton = screen.getByTitle(/Wiederherstellen/);

		await user.click(undoButton);
		expect(mockOnUndo).toHaveBeenCalledTimes(1);

		await user.click(redoButton);
		expect(mockOnRedo).toHaveBeenCalledTimes(1);
	});
});
