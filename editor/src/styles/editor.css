.tiptap {
	padding: 0;
	margin: 0;

	:first-child {
		margin-top: 0;
	}
}

/* Base styles for both edit and preview modes */
.ProseMirror,
[data-element-content] {
	height: 100%;
	outline: none;
	display: flex;
	flex-direction: column;
	width: 100%;
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
	white-space: pre-wrap;
	max-width: 100%;
	font-family: "NeoSansforeprimo-Regular", sans-serif;
	box-sizing: border-box;
	padding: 0;
	margin: 0;
	text-align: left;
	/* Ensure text is left-aligned by default */
}

/* Consistent paragraph styling for both modes */
.ProseMirror p,
[data-element-content] p {
	margin: 0;
	width: 100%;
	max-width: 100%;
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
	line-height: 0.8;
	text-align: left;
	/* Ensure paragraphs are left-aligned by default */
}

.ProseMirror span {
	line-height: 0.8;
	/* Inherit line-height to respect parent settings */
}

/* Empty paragraphs should still be visible in both modes */
.ProseMirror p:empty::before,
[data-element-content] p:empty::before {
	content: "\00a0";
	/* Non-breaking space */
	display: inline-block;
	line-height: 0.8;
	/* Match the line-height of regular paragraphs */
	/* Keep a minimum height for empty paragraphs */
}

/* Handle paragraphs with only a br tag (common in edit mode) */
.ProseMirror p:has(br:only-child),
[data-element-content] p:has(br:only-child) {
	line-height: 0.8;
}

/* Ensure br tags have consistent height */
.ProseMirror br,
[data-element-content] br {
	line-height: 0.8;
}

/* Style for editor paragraphs */
.editor-paragraph,
[data-element-content] p {
	margin: 0;
	width: 100%;
	max-width: 100%;
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
}

[data-active="true"] {
	background-color: #e2e8f0;
}

/* Consistent list styling for both modes */
.ProseMirror ul,
.ProseMirror ol,
[data-element-content] ul,
[data-element-content] ol {
	padding-left: 1.5rem;
	width: calc(100% - 1.5rem);
	max-width: calc(100% - 1.5rem);
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
}

/* Add alignment support for lists - ensure bullets/numbers and text stay inline */
.ProseMirror[style*="text-align: center"] ul,
.ProseMirror[style*="text-align: center"] ol,
[data-element-content][style*="text-align: center"] ul,
[data-element-content][style*="text-align: center"] ol {
	display: table;
	margin: 0 auto;
	text-align: left;
	/* Keep text left-aligned within the centered container */
}

.ProseMirror[style*="text-align: right"] ul,
.ProseMirror[style*="text-align: right"] ol,
[data-element-content][style*="text-align: right"] ul,
[data-element-content][style*="text-align: right"] ol {
	display: table;
	margin-left: auto;
	text-align: left;
	/* Keep text left-aligned within the right-aligned container */
}

/* Ensure list items display properly */
.ProseMirror li,
[data-element-content] li {
	display: list-item;
	font-family: inherit;
	font-size: inherit;
	color: inherit;
	line-height: inherit;
}

/* Style for bullets in unordered lists */
.bullet-list li::marker,
.ProseMirror ul li::marker,
[data-element-content] ul li::marker {
	content: "•";
	/* Bullet character */
	font-size: 1.2em;
	color: inherit;
}

/* Style for customized bullet lists */
.bullet-list.square li::marker,
.ProseMirror ul.square li::marker,
[data-element-content] ul.square li::marker {
	content: "■";
	/* Square bullet */
}

.bullet-list.circle li::marker,
.ProseMirror ul.circle li::marker,
[data-element-content] ul.circle li::marker {
	content: "○";
	/* Circle bullet */
}

/* Support for editable ordered lists - use CSS counters that can be styled */
.editable-ordered-list,
.ProseMirror ol.editable-ordered-list,
[data-element-content] ol.editable-ordered-list {
	counter-reset: item;
	list-style-type: none;
}

.editable-ordered-list > li,
.ProseMirror ol.editable-ordered-list > li,
[data-element-content] ol.editable-ordered-list > li {
	counter-increment: item;
	display: flex;
	align-items: baseline;
}

.editable-ordered-list > li::before,
.ProseMirror ol.editable-ordered-list > li::before,
[data-element-content] ol.editable-ordered-list > li::before {
	content: counter(item) ".";
	display: inline-block;
	margin-right: 0.5em;
	min-width: 1.2em;
	font-family: inherit;
	font-weight: inherit;
	color: inherit;
}

/* Fix for nested lists */
.ProseMirror li ul,
.ProseMirror li ol,
[data-element-content] li ul,
[data-element-content] li ol {
	margin-top: 0.25em;
	margin-bottom: 0.25em;
}

/* Indentation styles */
.ProseMirror li.nested-1,
[data-element-content] li.nested-1 {
	margin-left: 1.5em;
}

.ProseMirror li.nested-2,
[data-element-content] li.nested-2 {
	margin-left: 3em;
}

.ProseMirror li.nested-3,
[data-element-content] li.nested-3 {
	margin-left: 4.5em;
}

/* Support for proper list indentation */
.ProseMirror ul ul,
.ProseMirror ol ol,
.ProseMirror ul ol,
.ProseMirror ol ul,
[data-element-content] ul ul,
[data-element-content] ol ol,
[data-element-content] ul ol,
[data-element-content] ol ul {
	margin-left: 1.5em;
}

/* Vertical alignment for both edit and preview modes */
[data-element-content],
.ProseMirror {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
}

/* Top alignment - content at the top */
[data-element-content][style*="vertical-align: top"],
.ProseMirror[style*="vertical-align: top"],
.vertical-align-top,
[data-element-content] [style*="vertical-align: top"],
.ProseMirror [style*="vertical-align: top"] {
	justify-content: flex-start !important;
}

/* Middle alignment - content in the center */
[data-element-content][style*="vertical-align: middle"],
.ProseMirror[style*="vertical-align: middle"],
.vertical-align-middle,
[data-element-content] [style*="vertical-align: middle"],
.ProseMirror [style*="vertical-align: middle"] {
	justify-content: center !important;
}

/* Bottom alignment - content at the bottom */
[data-element-content][style*="vertical-align: bottom"],
.ProseMirror[style*="vertical-align: bottom"],
.vertical-align-bottom,
[data-element-content] [style*="vertical-align: bottom"],
.ProseMirror [style*="vertical-align: bottom"] {
	justify-content: flex-end !important;
}

/* Styles for subscript and superscript */
.ProseMirror sub,
[data-element-content] sub {
	font-size: 0.65em;
	vertical-align: baseline;
	position: relative;
	bottom: -0.3em;
	display: inline;
	line-height: 0;
}

.ProseMirror sup,
[data-element-content] sup {
	font-size: 0.65em;
	vertical-align: baseline;
	position: relative;
	top: -0.5em;
	display: inline;
	line-height: 0;
}

/* Make sure text styles are applied to the content in both edit and preview modes */
[data-element-content] *,
.ProseMirror * {
	font-family: inherit;
	font-size: inherit;
	color: inherit;
	max-width: 100%;
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
}

/* Ensure text styles are properly displayed in both modes */
.ProseMirror span[style],
[data-element-content] span[style] {
	display: inline;
	max-width: 100%;
}

/* Ensure consistent text alignment in both modes */
.ProseMirror [style*="text-align"],
[data-element-content] [style*="text-align"] {
	width: 100%;
	max-width: 100%;
}

/* Ensure consistent overflow handling */
.ProseMirror,
[data-element-content],
.prose {
	overflow: hidden;
	max-width: 100%;
}

/* Font family styles will be applied automatically through inline styles */

/* Add styles for snap guidelines */
.snap-guide {
	position: absolute;
	pointer-events: none;
	z-index: 9999;
	box-shadow: 0 0 4px rgba(59, 130, 246, 0.8);
}

.snap-guide.horizontal {
	height: 1px;
	background-color: #3b82f6;
	box-shadow: 0 0 4px rgba(59, 130, 246, 0.8);
}

.snap-guide.vertical {
	width: 1px;
	background-color: #3b82f6;
	box-shadow: 0 0 4px rgba(59, 130, 246, 0.8);
}

/* Style for emulated lists using data attributes */
.ProseMirror p[data-list-type],
[data-element-content] p[data-list-type] {
	display: flex;
	align-items: baseline;
	position: relative;
	margin-bottom: 0.25em;
}

/* Style for bullet lists */
.ProseMirror p[data-list-type="bullet"]::before,
[data-element-content] p[data-list-type="bullet"]::before {
	content: attr(data-list-value);
	margin-right: 0.75em;
	font-size: 1.2em;
	min-width: 1em;
	display: inline-block;
	text-align: center;
	flex-shrink: 0;
}

/* Style for numbered lists */
.ProseMirror p[data-list-type="number"]::before,
[data-element-content] p[data-list-type="number"]::before {
	content: attr(data-list-value);
	margin-right: 0.75em;
	min-width: 1.5em;
	display: inline-block;
	flex-shrink: 0;
}

/* Style for text content in list items */
.ProseMirror p[data-list-type] > *:first-child,
[data-element-content] p[data-list-type] > *:first-child {
	flex: 1;
	min-width: 0;
}

/* Make the list markers more visible with a highlight */
.ProseMirror p[data-list-type]::before,
[data-element-content] p[data-list-type]::before {
	color: inherit;
	font-weight: bold;
}

/* Debug styles to see when attributes are applied */
.ProseMirror p[data-list-type],
[data-element-content] p[data-list-type] {
	border-left: 2px solid rgba(59, 130, 246, 0.3);
	padding-left: 4px;
}

/* Delete highlight overlay for table rows/columns */
.delete-highlight-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(239, 68, 68, 0.3);
	pointer-events: none;
	z-index: 10;
}

/* Table cell performance optimization */
table {
	table-layout: fixed;
}

td {
	position: relative;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* Prevent text from overflowing table cells */
td > div {
	max-height: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* Constrain TipTap editor inside table cells */
td .ProseMirror {
	max-height: 100%;
	overflow: hidden;
	overflow-y: auto;
	/* Allow scrolling within the cell if needed */
}

td .ProseMirror::-webkit-scrollbar {
	width: 4px;
}

td .ProseMirror::-webkit-scrollbar-track {
	background: transparent;
}

td .ProseMirror::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 2px;
}

/* Ensure content wrapper respects cell height */
td > div > div {
	max-height: 100%;
	overflow: hidden;
}

/* Table resize performance */
td.resizing {
	transition: none !important;
}

/* Visual feedback for resize handles */
td .resize-handle-col,
td .resize-handle-row {
	opacity: 0;
	transition: opacity 0.2s;
}

td:hover .resize-handle-col,
td:hover .resize-handle-row {
	opacity: 1;
}

/* Ensure text cursor when editing in ProseMirror */
.ProseMirror {
	cursor: text;
}
