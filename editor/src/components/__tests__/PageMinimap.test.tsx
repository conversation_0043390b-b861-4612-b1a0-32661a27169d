import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";

import { PageMinimap } from "../PageMinimap";

// Mock the hooks used by PageMinimap
vi.mock("../hooks/usePageDragAndDrop", () => ({
	usePageDragAndDrop: () => ({
		dragState: {
			isDragging: false,
			draggedPageIndex: null,
			dropTargetIndex: null,
			isDropValid: false,
			pointerX: 0,
			pointerY: 0,
		},
		getDragProps: () => ({}),
	}),
}));

vi.mock("../hooks/usePairDragAndDrop", () => ({
	usePairDragAndDrop: () => ({
		dragState: {
			isDragging: false,
			draggedPairIndex: null,
			dropTargetIndex: null,
			dropPosition: null,
		},
		getDragHandleProps: () => ({}),
		getDropZoneProps: () => ({}),
	}),
}));

describe("PageMinimap component", () => {
	// Common props for all tests
	const mockPages = [
		{
			id: "page-1",
			format: "A4",
			orientation: "portrait" as const,
			width: 210,
			height: 297,
		},
		{
			id: "page-2",
			format: "A4",
			orientation: "portrait" as const,
			width: 210,
			height: 297,
		},
		{
			id: "page-3",
			format: "A4",
			orientation: "landscape" as const,
			width: 297,
			height: 210,
		},
		{
			id: "page-4",
			format: "A4",
			orientation: "portrait" as const,
			width: 210,
			height: 297,
		},
	];

	const mockElements = [
		{
			id: "element-1",
			currentPageId: "page-1",
			type: "text" as const,
			x: 10,
			y: 20,
			width: 100,
			height: 50,
			rotation: 0,
		},
		{
			id: "element-2",
			currentPageId: "page-2",
			type: "image" as const,
			x: 10,
			y: 20,
			width: 100,
			height: 50,
			rotation: 0,
		},
	];

	const mockOnPageSelect = vi.fn();
	const mockOnPageReorder = vi.fn();
	const mockOnPageSwap = vi.fn();
	const scrollContainerRef = { current: document.createElement("div") };

	beforeEach(() => {
		mockOnPageSelect.mockReset();
		mockOnPageReorder.mockReset();
		mockOnPageSwap.mockReset();
	});

	it("renders page thumbnails for all pages", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
			/>,
		);

		// Check that all page numbers are displayed
		expect(screen.getByText("1")).toBeInTheDocument();
		expect(screen.getByText("2")).toBeInTheDocument();
		expect(screen.getByText("3")).toBeInTheDocument();
		expect(screen.getByText("4")).toBeInTheDocument();

		// Check that pages are grouped into pairs
		expect(screen.getByText("Paar 1")).toBeInTheDocument();
		expect(screen.getByText("Paar 2")).toBeInTheDocument();
	});

	it("calls onPageSelect when a page is clicked", async () => {
		const user = userEvent.setup();
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
			/>,
		);

		// Find the button for page 2
		const page2Button = screen.getAllByRole("button")[1]; // Second page button
		await user.click(page2Button);

		expect(mockOnPageSelect).toHaveBeenCalledTimes(1);
		expect(mockOnPageSelect).toHaveBeenCalledWith("page-2");
	});

	it("highlights the current page", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-2"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
			/>,
		);

		// The second page button should have the ring class for highlighting
		const pageButtons = screen.getAllByRole("button");
		expect(pageButtons[1]).toHaveClass("ring-2");
	});

	it("renders element previews on page thumbnails", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
			/>,
		);

		// Check that the element preview divs are rendered
		// We can't easily check for specific elements, but we can check that there are divs inside the page thumbnails
		const pageButtons = screen.getAllByRole("button");
		const firstPageButton = pageButtons[0];

		// The first page should have at least one element preview
		expect(firstPageButton.querySelectorAll("div").length).toBeGreaterThan(1);
	});

	it("disables drag functionality when isEditorLocked is true", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
				isEditorLocked={true}
			/>,
		);

		// When editor is locked, drag handles should not be visible
		expect(screen.queryByTestId("drag-handle")).not.toBeInTheDocument();
	});

	it("shows drag handles when isEditorLocked is false", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
				isEditorLocked={false}
			/>,
		);

		// When editor is not locked, drag handles should be visible
		expect(screen.getAllByTestId("drag-handle").length).toBe(2); // One for each pair
	});

	it("shows page count in the header", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
			/>,
		);

		expect(screen.getByText("Seiten (4)")).toBeInTheDocument();
	});

	it("shows pair information", () => {
		render(
			<PageMinimap
				pages={mockPages}
				elements={mockElements}
				currentPageId="page-1"
				onPageSelect={mockOnPageSelect}
				onPageReorder={mockOnPageReorder}
				onPageSwap={mockOnPageSwap}
				scrollContainerRef={scrollContainerRef}
			/>,
		);

		// First pair has 2 pages
		expect(screen.getAllByText("2 Seiten")[0]).toBeInTheDocument();

		// Second pair has 2 pages
		expect(screen.getAllByText("2 Seiten")[1]).toBeInTheDocument();
	});
});
