import { BookMarked, Co<PERSON>, Scissors, Trash2 } from "lucide-react";
import type React from "react";
import { useMemo, useState } from "react";
import type { ResizeHandleType } from "@/hooks/useElementTransform";
import type { Element } from "../../types/element";
import { SaveTemplateDialog } from "../SaveTemplateDialog";
import { Button } from "../ui/button";

interface ControlElementsProps {
	element: Element;
	isSelected: boolean;
	isExportMode: boolean;
	isLocked: boolean;
	editingBlockId?: string | null;
	onCopy: (elementId: string) => void;
	onCut: (elementId: string) => void;
	onDelete: (elementId: string) => void;
	onSaveAsTemplate?: (blockElement: Element, childElements: Element[]) => void;
	onResizeStart: (e: React.MouseEvent, handleType: ResizeHandleType) => void;
	childElements?: Element[];
}

export function ControlElements({
	element,
	isSelected,
	isExportMode,
	isLocked,
	editingBlockId,
	onCopy,
	onCut,
	onDelete,
	onSaveAsTemplate,
	onResizeStart,
	childElements,
}: ControlElementsProps) {
	const [showSaveTemplateDialog, setShowSaveTemplateDialog] = useState(false);

	// Determine modifier key label depending on platform
	const { modKeyLabel } = useMemo(() => {
		let isMac = false;
		if (typeof navigator !== "undefined") {
			isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
		}
		return { modKeyLabel: isMac ? "⌘" : "Strg" };
	}, []);

	// Determine if controls should be shown
	const showControls =
		isSelected &&
		!isExportMode &&
		(editingBlockId === null ||
			element.id === editingBlockId ||
			element.parentId === editingBlockId);

	if (!showControls) return null;

	// Shared styles for handles
	const handleBaseStyle: React.CSSProperties = {
		position: "absolute",
		width: "12px",
		height: "12px",
		backgroundColor: "rgb(59 130 246)", // blue-500
		border: "1px solid white",
		borderRadius: "50%", // Make handles circular
		boxShadow: "0 0 3px rgba(0,0,0,0.3)",
		zIndex: 10, // Ensure handles are clickable
		touchAction: "none", // Improve touch device compatibility
	};

	// Specific styles for each handle
	const handleStyles: Record<ResizeHandleType, React.CSSProperties> = {
		top: {
			...handleBaseStyle,
			top: "-6px",
			left: "50%",
			transform: "translateX(-50%)",
			cursor: "ns-resize",
		},
		bottom: {
			...handleBaseStyle,
			bottom: "-6px",
			left: "50%",
			transform: "translateX(-50%)",
			cursor: "ns-resize",
		},
		left: {
			...handleBaseStyle,
			left: "-6px",
			top: "50%",
			transform: "translateY(-50%)",
			cursor: "ew-resize",
		},
		right: {
			...handleBaseStyle,
			right: "-6px",
			top: "50%",
			transform: "translateY(-50%)",
			cursor: "ew-resize",
		},
		"top-left": {
			...handleBaseStyle,
			top: "-6px",
			left: "-6px",
			cursor: "nwse-resize",
		},
		"top-right": {
			...handleBaseStyle,
			top: "-6px",
			right: "-6px",
			cursor: "nesw-resize",
		},
		"bottom-left": {
			...handleBaseStyle,
			bottom: "-6px",
			left: "-6px",
			cursor: "nesw-resize",
		},
		"bottom-right": {
			...handleBaseStyle,
			bottom: "-6px",
			right: "-6px",
			cursor: "nwse-resize",
		},
	};

	// For locked elements, just show a selection border without other controls
	if (isLocked) {
		return (
			<div className="absolute inset-0 border border-blue-500 border-dashed pointer-events-none" />
		);
	}

	return (
		<>
			{/* Selection Border */}
			<div className="absolute inset-0 border border-blue-500 border-dashed pointer-events-none" />

			{/* Resize Handles - not for address fields */}
			{!element.isAddressField &&
				Object.entries(handleStyles).map(([type, style]) => (
					<div
						key={type}
						className="handle" // Add handle class for identification
						style={style}
						role="button"
						tabIndex={0}
						onMouseDown={(e) => onResizeStart(e, type as ResizeHandleType)}
						onKeyDown={(e) => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								// For keyboard users, we could trigger a resize mode or provide alternative interaction
							}
						}}
					/>
				))}

			{/* Control buttons - only for non-address elements */}
			{!element.isAddressField && (
				<div className="absolute -top-8 right-0 flex gap-1 bg-white shadow-sm rounded-md p-1">
					<Button
						variant="ghost"
						size="icon"
						className="h-6 w-6"
						onClick={(e) => {
							e.stopPropagation();
							onCopy(element.id);
						}}
						title={`Kopieren (${modKeyLabel} + C)`}
						disabled={isLocked}
					>
						<Copy className="h-4 w-4" />
					</Button>
					<Button
						variant="ghost"
						size="icon"
						className="h-6 w-6"
						onClick={(e) => {
							e.stopPropagation();
							onCut(element.id);
						}}
						title={`Ausschneiden (${modKeyLabel} + X)`}
						disabled={isLocked}
					>
						<Scissors className="h-4 w-4" />
					</Button>
					{/* Save as Template button - only for blocks */}
					{element.type === "block" && onSaveAsTemplate && (
						<Button
							variant="ghost"
							size="icon"
							className="h-6 w-6"
							onClick={(e) => {
								e.stopPropagation();
								setShowSaveTemplateDialog(true);
							}}
							title="Als Template speichern"
							disabled={isLocked}
						>
							<BookMarked className="h-4 w-4" />
						</Button>
					)}
					<Button
						variant="ghost"
						size="icon"
						className="h-6 w-6"
						onClick={(e) => {
							e.stopPropagation();
							onDelete(element.id);
						}}
						title="Löschen (Entf / Backspace)"
						disabled={isLocked}
					>
						<Trash2 className="h-4 w-4" />
					</Button>
				</div>
			)}

			{/* Save Template Dialog */}
			{showSaveTemplateDialog && element.type === "block" && (
				<SaveTemplateDialog
					isOpen={showSaveTemplateDialog}
					onClose={() => setShowSaveTemplateDialog(false)}
					blockData={{
						blockElement: element,
						childElements: childElements || [],
					}}
				/>
			)}
		</>
	);
}
