import { describe, expect, it } from "vitest";
import {
	MM_TO_PX_RATIO,
	mmToPx,
	PT_TO_PX_RATIO,
	PX_PER_MM,
	PX_TO_MM_RATIO,
	ptToPx,
	pxToMm,
	pxToPt,
	roundToTwoDecimals,
} from "../unitConversion";

describe("unitConversion", () => {
	describe("constants", () => {
		it("should have correct conversion ratios", () => {
			expect(MM_TO_PX_RATIO).toBeCloseTo(3.779528, 6);
			expect(PX_TO_MM_RATIO).toBeCloseTo(1 / 3.779528, 6);
			expect(PX_PER_MM).toBeCloseTo(3.7795275591, 6);
			expect(PT_TO_PX_RATIO).toBeCloseTo(4 / 3, 6);
		});

		it("should have inverse relationship between MM_TO_PX_RATIO and PX_TO_MM_RATIO", () => {
			expect(MM_TO_PX_RATIO * PX_TO_MM_RATIO).toBeCloseTo(1, 10);
		});
	});

	describe("mmToPx", () => {
		it("should convert millimeters to pixels correctly", () => {
			expect(mmToPx(0)).toBe(0);
			expect(mmToPx(1)).toBeCloseTo(3.779528, 6);
			expect(mmToPx(10)).toBeCloseTo(37.79528, 6);
			expect(mmToPx(25.4)).toBeCloseTo(96, 1); // 1 inch = 25.4mm = 96px at 96 DPI
		});

		it("should handle negative values", () => {
			expect(mmToPx(-1)).toBeCloseTo(-3.779528, 6);
			expect(mmToPx(-10)).toBeCloseTo(-37.79528, 6);
		});

		it("should handle decimal values", () => {
			expect(mmToPx(0.5)).toBeCloseTo(1.889764, 6);
			expect(mmToPx(2.54)).toBeCloseTo(9.6, 1); // 0.1 inch
		});

		it("should handle very small values", () => {
			expect(mmToPx(0.001)).toBeCloseTo(0.003779528, 9);
		});

		it("should handle very large values", () => {
			expect(mmToPx(1000)).toBeCloseTo(3779.528, 3);
		});
	});

	describe("pxToMm", () => {
		it("should convert pixels to millimeters correctly", () => {
			expect(pxToMm(0)).toBe(0);
			expect(pxToMm(96)).toBeCloseTo(25.4, 1); // 96px = 1 inch = 25.4mm
			expect(pxToMm(3.779528)).toBeCloseTo(1, 6);
		});

		it("should handle negative values", () => {
			expect(pxToMm(-96)).toBeCloseTo(-25.4, 1);
			expect(pxToMm(-3.779528)).toBeCloseTo(-1, 6);
		});

		it("should handle decimal values", () => {
			expect(pxToMm(1.5)).toBeCloseTo(0.396875, 6);
			expect(pxToMm(48)).toBeCloseTo(12.7, 1); // 0.5 inch
		});

		it("should be inverse of mmToPx", () => {
			const testValues = [0, 1, 5, 10, 25.4, 100, 210, 297]; // Common mm values

			testValues.forEach((mm) => {
				const px = mmToPx(mm);
				const backToMm = pxToMm(px);
				expect(backToMm).toBeCloseTo(mm, 10);
			});
		});
	});

	describe("roundToTwoDecimals", () => {
		it("should round numbers to two decimal places", () => {
			expect(roundToTwoDecimals(1.234)).toBe(1.23);
			expect(roundToTwoDecimals(1.235)).toBe(1.24); // Rounds up
			expect(roundToTwoDecimals(1.999)).toBe(2.0);
		});

		it("should handle whole numbers", () => {
			expect(roundToTwoDecimals(5)).toBe(5);
			expect(roundToTwoDecimals(0)).toBe(0);
			expect(roundToTwoDecimals(100)).toBe(100);
		});

		it("should handle negative numbers", () => {
			expect(roundToTwoDecimals(-1.234)).toBe(-1.23);
			expect(roundToTwoDecimals(-1.235)).toBe(-1.24);
			expect(roundToTwoDecimals(-0.001)).toBe(-0.0);
		});

		it("should handle numbers with fewer than two decimal places", () => {
			expect(roundToTwoDecimals(1.2)).toBe(1.2);
			expect(roundToTwoDecimals(1.0)).toBe(1);
		});

		it("should handle very small numbers", () => {
			expect(roundToTwoDecimals(0.001)).toBe(0.0);
			expect(roundToTwoDecimals(0.004)).toBe(0.0);
			expect(roundToTwoDecimals(0.005)).toBe(0.01);
		});

		it("should handle edge cases with floating point precision", () => {
			expect(roundToTwoDecimals(0.1 + 0.2)).toBe(0.3); // Classic floating point issue
			expect(roundToTwoDecimals(1.005)).toBe(1); // JavaScript's Math.round behavior for .5 cases
		});
	});

	describe("ptToPx", () => {
		it("should convert points to pixels correctly", () => {
			expect(ptToPx(0)).toBe(0);
			expect(ptToPx(72)).toBe(96); // 72pt = 1 inch = 96px at 96 DPI
			expect(ptToPx(36)).toBe(48); // 0.5 inch
			expect(ptToPx(12)).toBe(16); // Common font size
		});

		it("should handle decimal values and round to nearest pixel", () => {
			expect(ptToPx(1.5)).toBe(2); // 1.5 * 4/3 = 2
			expect(ptToPx(2.25)).toBe(3); // 2.25 * 4/3 = 3
			expect(ptToPx(0.75)).toBe(1); // 0.75 * 4/3 = 1
		});

		it("should handle negative values", () => {
			expect(ptToPx(-12)).toBe(-16);
			expect(ptToPx(-72)).toBe(-96);
		});

		it("should round to nearest integer", () => {
			expect(ptToPx(1)).toBe(1.33); // 1 * 4/3 = 1.333... rounds to 1.33
			expect(ptToPx(2)).toBe(2.67); // 2 * 4/3 = 2.666... rounds to 3
			expect(ptToPx(3)).toBe(4); // 3 * 4/3 = 4
		});
	});

	describe("pxToPt", () => {
		it("should convert pixels to points correctly", () => {
			expect(pxToPt(0)).toBe(0);
			expect(pxToPt(96)).toBe(72); // 96px = 1 inch = 72pt
			expect(pxToPt(48)).toBe(36); // 0.5 inch
			expect(pxToPt(16)).toBe(12); // Common font size
		});

		it("should handle decimal values", () => {
			expect(pxToPt(1)).toBeCloseTo(0.75, 6); // 1 / (4/3) = 0.75
			expect(pxToPt(4)).toBe(3); // 4 / (4/3) = 3
		});

		it("should handle negative values", () => {
			expect(pxToPt(-16)).toBe(-12);
			expect(pxToPt(-96)).toBe(-72);
		});

		it("should be inverse of ptToPx for exact values", () => {
			const testValues = [0, 12, 24, 36, 48, 72]; // Common point values

			testValues.forEach((pt) => {
				const px = ptToPx(pt);
				const backToPt = pxToPt(px);
				expect(backToPt).toBeCloseTo(pt, 10);
			});
		});
	});

	describe("precision and rounding behavior", () => {
		it("should maintain precision in conversion chains", () => {
			const originalMm = 210; // A4 width
			const px = mmToPx(originalMm);
			const backToMm = pxToMm(px);

			expect(backToMm).toBeCloseTo(originalMm, 10);
		});

		it("should handle edge cases with very small differences", () => {
			const value1 = mmToPx(1);
			const value2 = mmToPx(1.0000001);

			expect(Math.abs(value1 - value2)).toBeLessThan(0.000001);
		});

		it("should maintain consistency across different conversion paths", () => {
			// Test that different ways of calculating the same conversion give consistent results
			const mm = 25.4; // 1 inch

			const directPx = mmToPx(mm);
			const viaPt = ptToPx(pxToPt(mmToPx(mm)));

			expect(Math.abs(directPx - viaPt)).toBeLessThan(0.1); // Allow small rounding differences
		});

		it("should handle boundary values correctly", () => {
			expect(mmToPx(Number.MIN_VALUE)).toBeCloseTo(0, 10); // Very close to zero
			expect(pxToMm(Number.MIN_VALUE)).toBeCloseTo(0, 10); // Very close to zero

			// Test with reasonable large values
			const largeMm = 10000; // 10 meters
			const largePx = mmToPx(largeMm);
			expect(largePx).toBeGreaterThan(0);
			expect(Number.isFinite(largePx)).toBe(true);
		});
	});
});
