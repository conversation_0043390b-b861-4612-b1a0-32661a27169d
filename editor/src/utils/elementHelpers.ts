import { pxToMm, roundToTwoDecimals } from "./unitConversion";

/**
 * Calculates a scroll-aware position for a new element
 * Ensures the element is visible in the viewport
 *
 * @returns {Object} An object with x and y coordinates in mm
 */
export function getScrollAwarePosition(): { x: number; y: number } {
	// Default position
	let x = 20; // Default x position (mm)
	let y = 20; // Default y position (mm)

	// Get scroll container to check viewport bounds
	const scrollContainer = document.querySelector(".overflow-y-auto");

	// If we have a scroll container, ensure the element is visible in the viewport
	if (scrollContainer) {
		const containerRect = scrollContainer.getBoundingClientRect();

		// Find the current visible page - the one with the blue border
		const visiblePage = document.querySelector(".border-2.border-blue-500")
			?.parentElement?.parentElement;

		if (visiblePage) {
			const pageRect = visiblePage.getBoundingClientRect();

			// Calculate the relative position within the page
			// This ensures we're positioning relative to the page's top-left
			x = 20; // Margin from left (mm)

			// Check if the default y position would be visible
			// If not, place it 2mm from the top of the visible viewport
			const containerVisibleTop = Math.max(0, containerRect.top - pageRect.top);
			const visibleTopMm = roundToTwoDecimals(pxToMm(containerVisibleTop));

			// Add 2mm margin to ensure visibility
			y = Math.max(visibleTopMm + 2, y);
		}
	}

	return { x, y };
}
