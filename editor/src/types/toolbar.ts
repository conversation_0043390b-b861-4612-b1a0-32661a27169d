import type { ColorInfo, FontInfo } from "@/utils/apiService";
import type { Element } from "./element";

// Base props that most toolbars need
export interface BaseToolbarProps {
	onAddElement?: (element: Element) => void;
	selectedElement?: Element | null;
	onUpdateElement?: (element: Element) => void;
}

// Props for toolbars that work with colors
export interface ColorToolbarProps {
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
}

// Props for toolbars that work with fonts
export interface FontToolbarProps {
	apiFonts: FontInfo[];
	isLoadingFonts: boolean;
	loadedFonts: Set<string>;
	determineWeight: (style: string | undefined) => string;
}

// Props for toolbars that need page context
export interface PageContextProps {
	currentPageId?: string;
	elements?: Element[];
}

// Props for toolbars that need role-based permissions
export interface RoleBasedProps {
	role?: string;
	isReleased?: boolean;
}

// Combined common props interface
export interface CommonToolbarProps
	extends BaseToolbarProps,
		ColorToolbarProps,
		PageContextProps,
		RoleBasedProps {}

// Utility type for making certain props optional
export type OptionalToolbarProps<
	// biome-ignore lint/suspicious/noExplicitAny: is needed
	T extends Record<string, any>,
	K extends keyof T,
> = Omit<T, K> & Partial<Pick<T, K>>;
