import React from "react";
import ReactDOMServer from "react-dom/server";
import { ExportDocument, ExportPage } from "../components/ExportRenderer";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import type { PageFormat } from "./apiService";

/**
 * Generate CSS for HTML export - collects all styles from the document
 * and adds required static styles for export.
 */
export async function generateCSS(): Promise<string> {
	// Get current styles from document
	const dynamicStyles = Array.from(document.styleSheets)
		.filter(
			(sheet) => !sheet.href || sheet.href.startsWith(window.location.origin),
		)
		.map((sheet) => {
			try {
				return Array.from(sheet.cssRules)
					.map((rule) => rule.cssText)
					.join("\n");
			} catch {
				return ""; // Ignore sheets that can't be accessed (CORS)
			}
		})
		.join("\n");

	// Combine dynamic styles with comprehensive static export styles
	return `
        /* --- Dynamic Styles from Document --- */
        ${dynamicStyles}
        
        /* --- Static Export Styles --- */
        
        /* Reset & Base */
        *, *::before, *::after {
            box-sizing: border-box;
        }
        
        @page {
            margin: 0;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        
        /* Page Layout */
        .export-page {
            position: relative; 
            background: white; 
            margin: 20px auto; /* Default margin for viewing */
            box-shadow: 0 0 10px rgba(0,0,0,0.1); /* Default shadow */
            page-break-after: always; 
            box-sizing: border-box; 
        }
        
        /* Element Styling */
        .element {
            cursor: default !important; 
        }
        
        /* Control Hiding */
        .handle,
        button.absolute,
        .element-controls {
            display: none !important; 
        }
        
        /* Border Styling */
        div[class*="border"] {
            border-color: transparent !important; 
        }
        div[data-address-field="true"] {
            border: none !important; 
        }
        
        /* Print Specific */
        @media print {
            .export-page {
                margin: 0; /* Override default margin */
                box-shadow: none; /* Override default shadow */
            }
            h2, h3 { /* Hide potential page titles */
                display: none;
            }
        }
        /* --- End Static Export Styles --- */
    `;
}

/**
 * Generate HTML for a single page using React rendering
 */
export function generateSinglePageHTML(
	page: PageSettings,
	elements: Element[],
	pageNumber: number,
	documentTitle: string = "Document",
	useExternalCSS: boolean = false,
	inlineCSS: string | null = null, // Added parameter for inline styles
	apiFormats?: PageFormat[], // Added parameter for fold marks
): string {
	// Use ReactDOMServer to render the ExportPage component to HTML
	const pageHtml = ReactDOMServer.renderToStaticMarkup(
		React.createElement(ExportPage, {
			page: page,
			elements: elements,
			previewMode: false,
			apiFormats: apiFormats,
		}),
	);

	// Determine style tag content based on flags
	const styleContent = useExternalCSS
		? '<link rel="stylesheet" href="styles.css">'
		: inlineCSS
			? `<style>\n${inlineCSS}\n</style>`
			: "<!-- No inline styles provided -->";

	// Create the complete HTML document
	return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${documentTitle} - Page ${pageNumber}</title>
    ${styleContent}
</head>
<body>
    ${pageHtml}
</body>
</html>`;
}

/**
 * Generate a complete HTML document with all pages using the ExportDocument component
 */
export function generateFullDocumentHTML(
	pages: PageSettings[],
	elements: Element[],
	documentTitle: string = "Document",
	useExternalCSS: boolean = false,
	inlineCSS: string | null = null, // Added parameter for inline styles
	apiFormats?: PageFormat[], // Added parameter for fold marks
): string {
	// Use ReactDOMServer to render the ExportDocument component to HTML
	const documentHtml = ReactDOMServer.renderToStaticMarkup(
		React.createElement(ExportDocument, {
			pages: pages,
			elements: elements,
			apiFormats: apiFormats,
		}),
	);

	// Determine style tag content based on flags
	const styleContent = useExternalCSS
		? '<link rel="stylesheet" href="styles.css">'
		: inlineCSS
			? `<style>\n${inlineCSS}\n</style>`
			: "<!-- No inline styles provided -->";

	// Create the complete HTML document
	return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${documentTitle}</title>
    ${styleContent}
</head>
<body>
    ${documentHtml}
</body>
</html>`;
}
