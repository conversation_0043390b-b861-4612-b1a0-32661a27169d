import { cva } from "class-variance-authority";

export const toggleVariants = cva(
	"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
	{
		variants: {
			variant: {
				default:
					"bg-white hover:bg-gray-100 data-[state=on]:bg-gray-800 data-[state=on]:text-white",
				outline:
					"border border-input bg-white hover:bg-gray-100 data-[state=on]:bg-gray-800 data-[state=on]:text-white",
			},
			size: {
				default: "h-10 px-3",
				sm: "h-8 px-2",
				lg: "h-11 px-5",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
);
