import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import * as ElementService from "./elementService";

export interface ClipboardData {
	type: "element" | "block";
	element?: Element;
	block?: Element;
	children?: Element[];
}

// Private clipboard state
let clipboard: ClipboardData | null = null;

/**
 * Copy a single element to clipboard
 */
export function copyElement(element: Element): void {
	clipboard = {
		type: "element",
		element: { ...element },
	};
}

/**
 * Copy a block element with its children to clipboard
 */
export function copyBlock(
	blockElement: Element,
	childElements: Element[],
): void {
	clipboard = {
		type: "block",
		block: { ...blockElement },
		children: childElements.map((child) => ({ ...child })),
	};
}

/**
 * Cut an element (copy and mark for deletion)
 */
export function cutElement(element: Element): void {
	copyElement(element);
	// Mark element as cut (could be used for visual feedback)
	if (clipboard?.element) {
		clipboard.element._isCut = true;
	}
}

/**
 * Cut a block with children (copy and mark for deletion)
 */
export function cutBlock(
	blockElement: Element,
	childElements: Element[],
): void {
	copyBlock(blockElement, childElements);
	// Mark as cut
	if (clipboard?.block) {
		clipboard.block._isCut = true;
	}
}

/**
 * Get current clipboard data
 */
export function getClipboard(): ClipboardData | null {
	return clipboard;
}

/**
 * Check if clipboard has data
 */
export function hasClipboard(): boolean {
	return clipboard !== null;
}

/**
 * Clear clipboard
 */
export function clearClipboard(): void {
	clipboard = null;
}

/**
 * Paste element at specified position
 */
export function pasteElement(
	targetPageId: string,
	pasteX: number,
	pasteY: number,
	elements: Element[],
	pages: PageSettings[],
): { newElements: Element[]; pastedElementId: string } | null {
	if (!clipboard) return null;

	const targetPage = pages.find((p) => p.id === targetPageId);
	if (!targetPage) return null;

	let newElements: Element[] = [];
	let pastedElementId: string = "";

	if (clipboard.type === "element" && clipboard.element) {
		// Paste single element
		const clonedElement = ElementService.cloneElement(
			clipboard.element,
			pasteX,
			pasteY,
			targetPageId,
			elements,
		);

		// Validate bounds
		const validatedElement = ElementService.validateElementBounds(
			clonedElement,
			targetPage,
		);
		newElements = [...elements, validatedElement];
		pastedElementId = validatedElement.id;
	} else if (
		clipboard.type === "block" &&
		clipboard.block &&
		clipboard.children
	) {
		// Paste block with children
		const { block: newBlock, children: newChildren } =
			ElementService.cloneBlockWithChildren(
				clipboard.block,
				clipboard.children,
				pasteX,
				pasteY,
				targetPageId,
				elements,
			);

		// Validate block bounds
		const validatedBlock = ElementService.validateElementBounds(
			newBlock,
			targetPage,
		);
		newElements = [...elements, validatedBlock, ...newChildren];
		pastedElementId = validatedBlock.id;
	}

	return { newElements, pastedElementId };
}

/**
 * Calculate smart paste position to avoid overlapping
 */
export function calculateSmartPastePosition(
	originalX: number,
	originalY: number,
	elements: Element[],
	pageId: string,
	elementWidth: number,
	elementHeight: number,
): { x: number; y: number } {
	const offset = 5; // mm offset for each paste
	let x = originalX;
	let y = originalY;
	let attempts = 0;
	const maxAttempts = 10;

	while (attempts < maxAttempts) {
		const overlapping = ElementService.getElementsAtPosition(
			elements,
			x,
			y,
			pageId,
		).some((el) => isOverlapping(x, y, elementWidth, elementHeight, el));

		if (!overlapping) {
			break;
		}

		// Try next position
		x += offset;
		y += offset;
		attempts++;
	}

	return { x, y };
}

/**
 * Check if two rectangles overlap
 */
function isOverlapping(
	x1: number,
	y1: number,
	width1: number,
	height1: number,
	element2: Element,
): boolean {
	const x2 = element2.x;
	const y2 = element2.y;
	const width2 = element2.width;
	const height2 = element2.height;

	return !(
		x1 + width1 <= x2 ||
		x2 + width2 <= x1 ||
		y1 + height1 <= y2 ||
		y2 + height2 <= y1
	);
}

/**
 * Paste with smart positioning
 */
export function pasteElementSmart(
	targetPageId: string,
	preferredX: number,
	preferredY: number,
	elements: Element[],
	pages: PageSettings[],
): { newElements: Element[]; pastedElementId: string } | null {
	if (!clipboard) return null;

	const targetPage = pages.find((p) => p.id === targetPageId);
	if (!targetPage) return null;

	let elementWidth = 50; // Default width
	let elementHeight = 30; // Default height

	if (clipboard.type === "element" && clipboard.element) {
		elementWidth = clipboard.element.width;
		elementHeight = clipboard.element.height;
	} else if (clipboard.type === "block" && clipboard.block) {
		elementWidth = clipboard.block.width;
		elementHeight = clipboard.block.height;
	}

	// Calculate smart position
	const { x, y } = calculateSmartPastePosition(
		preferredX,
		preferredY,
		elements,
		targetPageId,
		elementWidth,
		elementHeight,
	);

	return pasteElement(targetPageId, x, y, elements, pages);
}

/**
 * Create template data from clipboard
 */
export function createTemplateFromClipboard(): {
	element?: Element;
	block?: Element;
	children?: Element[];
} | null {
	if (!clipboard) return null;

	if (clipboard.type === "block" && clipboard.block && clipboard.children) {
		const blockId = clipboard.block.id;
		return {
			block: {
				...clipboard.block,
				currentPageId: "",
				x: 0,
				y: 0,
			},
			children: clipboard.children.map((child) => ({
				...child,
				currentPageId: "",
				parentId: blockId,
				x: child.x,
				y: child.y,
			})),
		};
	}

	return null;
}

/**
 * Get clipboard summary for UI display
 */
export function getClipboardSummary(): string | null {
	if (!clipboard) return null;

	if (clipboard.type === "element" && clipboard.element) {
		const element = clipboard.element;
		const action = element._isCut ? "Cut" : "Copied";
		return `${action}: ${element.type} element`;
	}

	if (clipboard.type === "block" && clipboard.block && clipboard.children) {
		const block = clipboard.block;
		const action = block._isCut ? "Cut" : "Copied";
		const childCount = clipboard.children.length;
		return `${action}: Block with ${childCount} child element${childCount !== 1 ? "s" : ""}`;
	}

	return null;
}

/**
 * Check if clipboard contains a cut operation
 */
export function isClipboardCut(): boolean {
	if (!clipboard) return false;

	if (clipboard.type === "element" && clipboard.element) {
		return !!clipboard.element._isCut;
	}

	if (clipboard.type === "block" && clipboard.block) {
		return !!clipboard.block._isCut;
	}

	return false;
}

// Extend Element interface to support cut marking
declare module "../types/element" {
	interface Element {
		_isCut?: boolean;
	}
}
