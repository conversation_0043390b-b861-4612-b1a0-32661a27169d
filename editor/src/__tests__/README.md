# Test Utilities Documentation

This directory contains comprehensive test utilities and helpers for testing the React document editor application.

## Overview

The test utilities are organized into several key modules:

- **test-utils.tsx** - Custom render function with provider wrappers
- **mock-factories.ts** - Mock data factories for creating test fixtures
- **api-mocks.ts** - API mocks and network simulation utilities
- **custom-matchers.ts** - Custom Jest/Vitest matchers and assertion helpers

## Usage

### Custom Render Function

Use `renderWithProviders` instead of the standard `render` function to automatically wrap components with necessary context providers:

```typescript
import { renderWithProviders } from './utils/test-utils';
import { EditorStateFactory } from './utils/mock-factories';

// Basic usage
const { getByTestId, mockContext } = renderWithProviders(<MyComponent />);

// With initial state
const initialState = EditorStateFactory.createSimpleState();
const { getByTestId, mockContext } = renderWithProviders(<MyComponent />, { 
  initialState 
});

// Access mock context for assertions
expect(mockContext.pages).toHaveLength(1);
expect(mockContext.elements).toHaveLength(2);
```

### Mock Data Factories

Create realistic test data using the factory classes:

```typescript
import { ElementFactory, PageFactory, EditorStateFactory } from './utils/mock-factories';

// Create individual elements
const textElement = ElementFactory.createTextElement();
const shapeElement = ElementFactory.createShapeElement({ x: 100, y: 200 });
const tableElement = ElementFactory.createTableElement();

// Create pages
const page = PageFactory.createPage();
const landscapePage = PageFactory.createLandscapePage();
const customPage = PageFactory.createCustomPage(300, 400);

// Create complete editor states
const emptyState = EditorStateFactory.createEmptyState();
const simpleState = EditorStateFactory.createSimpleState();
const complexState = EditorStateFactory.createComplexState();
```

### API Mocks

Mock API calls and simulate network conditions:

```typescript
import { ApiMocks, NetworkSimulator } from './utils/api-mocks';

// Use default mocks (automatically set up)
const images = await ApiMocks.fetchAvailableImages();

// Simulate network delays
NetworkSimulator.setDelay(1000); // 1 second delay

// Simulate network errors
NetworkSimulator.setNetworkError(true);

// Simulate random failures
NetworkSimulator.setFailureRate(0.3); // 30% failure rate

// Reset to defaults
NetworkSimulator.reset();
ApiMocks.reset();
```

### Custom Matchers

Use custom matchers for domain-specific assertions:

```typescript
import { AssertionHelpers } from './utils/custom-matchers';

// Element validation
expect(element).toBeValidElement();
expect(element).toHaveValidPosition();
expect(element).toBeWithinPageBounds(page);
expect(element).toMatchElementProperties({ x: 100, y: 200 });

// Table-specific validation
expect(tableElement).toBeValidTableElement();

// Block-specific validation
expect(blockElement).toHaveValidBlockStructure();

// Address-specific validation
expect(addressElement).toBeValidAddressElement();

// Collection validation
expect(elements).toHaveUniqueElementIds();
expect(elements).toHaveConsistentZIndices();
expect(elements).toHaveElementsOnPage('page-1', 3);

// Editor state validation
expect(editorState).toHaveValidEditorState();

// Page validation
expect(page).toHaveValidPageSettings();
```

### Assertion Helpers

Use helper functions for complex validations:

```typescript
import { AssertionHelpers } from './utils/custom-matchers';

// Validate complete editor state
AssertionHelpers.assertValidEditorState(editorState);

// Validate element positioning
AssertionHelpers.assertElementsWithinBounds(elements, pages);

// Validate block relationships
AssertionHelpers.assertValidBlockRelationships(elements);

// Validate table structure
AssertionHelpers.assertValidTableStructure(tableElement);

// Validate z-index management
AssertionHelpers.assertValidZIndexManagement(elements);
```

## Test Patterns

### Component Testing

```typescript
describe('MyComponent', () => {
  it('should render with initial state', () => {
    const initialState = EditorStateFactory.createSimpleState();
    const { getByTestId, mockContext } = renderWithProviders(
      <MyComponent />, 
      { initialState }
    );
    
    expect(getByTestId('component')).toBeInTheDocument();
    expect(mockContext.pages).toHaveLength(1);
  });
});
```

### Service Testing

```typescript
describe('ElementService', () => {
  beforeEach(() => {
    ApiMocks.reset();
  });

  it('should create element successfully', async () => {
    const element = ElementFactory.createTextElement();
    const result = await elementService.createElement(element);
    
    expect(result).toBeValidElement();
    expect(ApiMocks.storeJsonFile).toHaveBeenCalled();
  });
});
```

### Integration Testing

```typescript
describe('Element Workflow', () => {
  it('should handle complete element lifecycle', () => {
    const initialState = EditorStateFactory.createSimpleState();
    const { mockContext } = renderWithProviders(<App />, { initialState });
    
    // Test element creation
    const newElement = ElementFactory.createTextElement();
    mockContext.setEditorState({
      ...mockContext.getEditorState(),
      elements: [...mockContext.elements, newElement]
    });
    
    // Validate state consistency
    AssertionHelpers.assertValidEditorState(mockContext.getEditorState());
  });
});
```

## Network Simulation

The `NetworkSimulator` class allows you to test various network conditions:

```typescript
// Test loading states
NetworkSimulator.setDelay(2000);
const promise = ApiMocks.fetchAvailableImages();
// Component should show loading state

// Test error handling
NetworkSimulator.setNetworkError(true);
await expect(ApiMocks.fetchAvailableImages()).rejects.toThrow('Network error');

// Test intermittent failures
NetworkSimulator.setFailureRate(0.5); // 50% failure rate
// Some requests will fail randomly
```

## Best Practices

1. **Always reset mocks** between tests using `ApiMocks.reset()` and `NetworkSimulator.reset()`
2. **Use factory methods** instead of creating test data manually
3. **Leverage custom matchers** for domain-specific validations
4. **Test error scenarios** using network simulation
5. **Validate complete state** using assertion helpers
6. **Use realistic test data** from the factories

## Available Mock Data

The mock factories provide realistic data for:

- **Elements**: Text, shape, image, table, block, and address elements
- **Pages**: Portrait, landscape, and custom-sized pages
- **Editor States**: Empty, simple, and complex document states
- **API Responses**: Images, colors, fonts, templates, and more

All mock data includes proper relationships, valid properties, and realistic content to ensure tests accurately reflect real-world usage.