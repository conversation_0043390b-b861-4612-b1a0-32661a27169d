import { useCallback, useEffect, useRef, useState } from "react";

export interface PairDragState {
	isDragging: boolean;
	draggedPairIndex: number | null;
	dropTargetIndex: number | null;
	dropPosition: "before" | "after" | null;
}

interface UsePairDnDParams {
	pairCount: number;
	onReorder: (fromIndex: number, toIndex: number) => void;
	isDisabled?: boolean;
}

/**
 * Pointer-events based drag-and-drop helper for re-ordering page pairs.
 *
 * Rationale: Native HTML5 DnD behaves inconsistently across browsers. By
 * switching to PointerEvents we get reliable behaviour in Chromium, WebKit and
 * Gecko while retaining mouse & touch support.
 */
export function usePairDragAndDrop({
	pairCount: _pairCount,
	onReorder,
	isDisabled = false,
}: UsePairDnDParams) {
	// Currently not needed but may be helpful for future logic (e.g., bounds checks)
	void _pairCount;

	const [state, setState] = useState<PairDragState>({
		isDragging: false,
		draggedPairIndex: null,
		dropTargetIndex: null,
		dropPosition: null,
	});

	// store latest state in ref so we can read inside listeners
	const stateRef = useRef(state);
	stateRef.current = state;

	// ---- helpers ----------------------------------------------------------
	const finishDrag = useCallback(() => {
		const { draggedPairIndex, dropTargetIndex, dropPosition } =
			stateRef.current;
		if (
			draggedPairIndex !== null &&
			dropTargetIndex !== null &&
			dropPosition !== null
		) {
			let toPairIndex = dropTargetIndex;
			if (dropPosition === "after") toPairIndex += 1;

			// Prevent no-op moves (same or adjacent index resulting in same order)
			if (
				draggedPairIndex !== toPairIndex &&
				draggedPairIndex !== toPairIndex - 1
			) {
				onReorder(draggedPairIndex * 2, toPairIndex * 2);
			}
		}
		// Reset state
		setState({
			isDragging: false,
			draggedPairIndex: null,
			dropTargetIndex: null,
			dropPosition: null,
		});
	}, [onReorder]);

	// ---- global pointer listeners ----------------------------------------
	useEffect(() => {
		if (!state.isDragging) return;

		const handlePointerUp = () => {
			finishDrag();
		};

		// Capture phase to guarantee we receive the event first
		window.addEventListener("pointerup", handlePointerUp, { capture: true });

		return () => {
			window.removeEventListener("pointerup", handlePointerUp, {
				capture: true,
			});
		};
	}, [state.isDragging, finishDrag]);

	// ---- API returned to component ---------------------------------------
	const getDragHandleProps = useCallback(
		(pairIndex: number) => {
			return {
				onPointerDown: (e: React.PointerEvent) => {
					if (isDisabled) return;
					if (e.button !== 0) return; // only left button / primary touch
					e.preventDefault();
					e.stopPropagation();
					setState({
						isDragging: true,
						draggedPairIndex: pairIndex,
						dropTargetIndex: null,
						dropPosition: null,
					});
				},
				// Allow keyboard focus for a11y
				tabIndex: 0,
				onKeyDown: (e: React.KeyboardEvent) => {
					// space / enter start drag, arrow keys move
					if (isDisabled) return;
					if (state.isDragging) return; // already dragging
					if (e.key === " " || e.key === "Enter") {
						e.preventDefault();
						setState({
							isDragging: true,
							draggedPairIndex: pairIndex,
							dropTargetIndex: null,
							dropPosition: null,
						});
					}
				},
			} as const;
		},
		[isDisabled, state.isDragging],
	);

	// drop zone props
	const getDropZoneProps = useCallback(
		(pairIndex: number, position: "before" | "after") => {
			return {
				onPointerEnter: () => {
					if (!stateRef.current.isDragging) return;
					if (stateRef.current.draggedPairIndex === null) return;
					setState((prev) => ({
						...prev,
						dropTargetIndex: pairIndex,
						dropPosition: position,
					}));
				},
				onPointerLeave: () => {
					if (!stateRef.current.isDragging) return;
					// Only clear if leaving the currently highlighted zone
					setState((prev) => {
						if (
							prev.dropTargetIndex === pairIndex &&
							prev.dropPosition === position
						) {
							return { ...prev, dropTargetIndex: null, dropPosition: null };
						}
						return prev;
					});
				},
			} as const;
		},
		[],
	);

	// Ensure global grabbing cursor while dragging
	useEffect(() => {
		if (state.isDragging) {
			document.body.style.cursor = "grabbing";
		} else {
			document.body.style.cursor = "";
		}

		return () => {
			// Cleanup in case component unmounts during drag
			document.body.style.cursor = "";
		};
	}, [state.isDragging]);

	return {
		dragState: state,
		getDragHandleProps,
		getDropZoneProps,
	} as const;
}
