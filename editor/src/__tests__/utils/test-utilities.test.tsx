import { beforeEach, describe, expect, it } from "vitest";
import { ApiMocks, NetworkSimulator } from "./api-mocks";
import { AssertionHelpers } from "./custom-matchers";
import {
	EditorStateFactory,
	ElementFactory,
	PageFactory,
} from "./mock-factories";
import { renderWithProviders } from "./test-utils";

// Simple test component
function TestComponent() {
	return <div data-testid="test-component">Test Component</div>;
}

describe("Test Utilities", () => {
	beforeEach(() => {
		ApiMocks.reset();
		NetworkSimulator.reset();
	});

	describe("renderWithProviders", () => {
		it("should render component with mock context", () => {
			const { getByTestId, mockContext } = renderWithProviders(
				<TestComponent />,
			);

			expect(getByTestId("test-component")).toBeInTheDocument();
			expect(mockContext).toBeDefined();
			expect(mockContext.pages).toEqual([]);
			expect(mockContext.elements).toEqual([]);
		});

		it("should render component with initial state", () => {
			const initialState = EditorStateFactory.createSimpleState();
			const { mockContext } = renderWithProviders(<TestComponent />, {
				initialState,
			});

			expect(mockContext.pages).toHaveLength(1);
			expect(mockContext.elements).toHaveLength(2);
		});
	});

	describe("ElementFactory", () => {
		it("should create valid text element", () => {
			const element = ElementFactory.createTextElement();

			expect(element).toBeValidElement();
			expect(element.type).toBe("text");
			expect(element.content).toBe("Sample text content");
		});

		it("should create valid shape element", () => {
			const element = ElementFactory.createShapeElement();

			expect(element).toBeValidElement();
			expect(element.type).toBe("shape");
			expect(element.shapeType).toBe("rectangle");
		});

		it("should create valid table element", () => {
			const element = ElementFactory.createTableElement();

			expect(element).toBeValidElement();
			expect(element).toBeValidTableElement();
		});

		it("should create valid block element", () => {
			const element = ElementFactory.createBlockElement();

			expect(element).toBeValidElement();
			expect(element).toHaveValidBlockStructure();
		});

		it("should create valid address element", () => {
			const element = ElementFactory.createAddressElement();

			expect(element).toBeValidElement();
			expect(element).toBeValidAddressElement();
		});

		it("should create elements on specific page", () => {
			const pageId = "test-page";
			const elements = ElementFactory.createElementsOnPage(pageId, 3);

			expect(elements).toHaveLength(3);
			expect(elements).toHaveElementsOnPage(pageId, 3);
			expect(elements).toHaveUniqueElementIds();
		});
	});

	describe("PageFactory", () => {
		it("should create valid page", () => {
			const page = PageFactory.createPage();

			expect(page).toHaveValidPageSettings();
			expect(page.orientation).toBe("portrait");
		});

		it("should create landscape page", () => {
			const page = PageFactory.createLandscapePage();

			expect(page).toHaveValidPageSettings();
			expect(page.orientation).toBe("landscape");
		});

		it("should create multiple pages", () => {
			const pages = PageFactory.createMultiplePages(3);

			expect(pages).toHaveLength(3);
			pages.forEach((page) => {
				expect(page).toHaveValidPageSettings();
			});
		});
	});

	describe("EditorStateFactory", () => {
		it("should create empty state", () => {
			const state = EditorStateFactory.createEmptyState();

			expect(state).toHaveValidEditorState();
			expect(state.pages).toHaveLength(0);
			expect(state.elements).toHaveLength(0);
		});

		it("should create simple state", () => {
			const state = EditorStateFactory.createSimpleState();

			expect(state).toHaveValidEditorState();
			expect(state.pages).toHaveLength(1);
			expect(state.elements).toHaveLength(2);

			AssertionHelpers.assertValidEditorState(state);
		});

		it("should create complex state", () => {
			const state = EditorStateFactory.createComplexState();

			expect(state).toHaveValidEditorState();
			expect(state.pages.length).toBeGreaterThan(1);
			expect(state.elements.length).toBeGreaterThan(2);

			AssertionHelpers.assertValidEditorState(state);
		});
	});

	describe("Custom Matchers", () => {
		it("should validate element properties", () => {
			const element = ElementFactory.createTextElement({
				x: 100,
				y: 200,
				content: "Test content",
			});

			expect(element).toMatchElementProperties({
				x: 100,
				y: 200,
				content: "Test content",
			});
		});

		it("should validate element position", () => {
			const element = ElementFactory.createTextElement({ x: 50, y: 100 });

			expect(element).toHaveValidPosition();
		});

		it("should validate element within page bounds", () => {
			const page = PageFactory.createPage();
			const element = ElementFactory.createTextElement({
				x: 50,
				y: 100,
				width: 100,
				height: 50,
			});

			expect(element).toBeWithinPageBounds(page);
		});

		it("should validate z-index consistency", () => {
			const elements = [
				ElementFactory.createTextElement({ zIndex: 1 }),
				ElementFactory.createShapeElement({ zIndex: 2 }),
				ElementFactory.createImageElement({ zIndex: 3 }),
			];

			expect(elements).toHaveConsistentZIndices();
		});
	});

	describe("AssertionHelpers", () => {
		it("should assert valid editor state", () => {
			const state = EditorStateFactory.createSimpleState();

			expect(() => {
				AssertionHelpers.assertValidEditorState(state);
			}).not.toThrow();
		});

		it("should assert elements within bounds", () => {
			const state = EditorStateFactory.createSimpleState();

			expect(() => {
				AssertionHelpers.assertElementsWithinBounds(
					state.elements,
					state.pages,
				);
			}).not.toThrow();
		});

		it("should assert valid z-index management", () => {
			const elements = ElementFactory.createElementsOnPage("page-1", 3);

			expect(() => {
				AssertionHelpers.assertValidZIndexManagement(elements);
			}).not.toThrow();
		});
	});

	describe("ApiMocks", () => {
		it("should provide default mock implementations", async () => {
			const result = await ApiMocks.fetchAvailableImages();

			expect(result).toBeDefined();
			expect(Array.isArray(result)).toBe(true);
			expect(result.length).toBeGreaterThan(0);
		});

		it("should simulate network delays", async () => {
			NetworkSimulator.setDelay(100);

			const startTime = Date.now();
			await ApiMocks.fetchAvailableImages();
			const endTime = Date.now();

			// Allow for some timing variance (±5ms) due to system timing precision
			expect(endTime - startTime).toBeGreaterThanOrEqual(95);
		});

		it("should simulate network errors", async () => {
			NetworkSimulator.setNetworkError(true);

			await expect(ApiMocks.fetchAvailableImages()).rejects.toThrow(
				"Network error",
			);
		});

		it("should simulate failure rates", async () => {
			NetworkSimulator.setFailureRate(1); // 100% failure rate

			await expect(ApiMocks.fetchAvailableImages()).rejects.toThrow(
				"API Error",
			);
		});
	});
});
