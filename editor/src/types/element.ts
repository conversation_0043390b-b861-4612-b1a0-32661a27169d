export type ElementType =
	| "text"
	| "shape"
	| "image"
	| "table"
	| "block"
	| "safeZone"
	| "address";

export type ShapeType = "rectangle" | "circle" | "roundedRectangle";

export interface BatchDeleteOperation {
	_batchDelete: true;
	elementIds: string[];
	currentPageId: string;
}

import type { TableProperties } from "./table";

export interface Element {
	id: string;
	currentPageId: string;
	type: ElementType;
	x: number;
	y: number;
	width: number;
	height: number;
	rotation: number;
	zIndex?: number;
	_delete?: boolean;
	_action?: "moveToFront" | "moveToBack" | "setZIndex"; // Temporary action signal
	content?: string;
	shapeType?: ShapeType;
	borderRadius?: number[]; // For rounded rectangles, array of 4 values for each corner
	color?: string;
	backgroundColor?: string;
	isAbsolutePosition?: boolean;
	aspectRatio?: number;
	tableProperties?: TableProperties;
	verticalAlign?: "top" | "middle" | "bottom"; // Store vertical alignment
	isLocked?: boolean; // Prevents element from being moved or resized
	isAddressField?: boolean; // Identifies element as an address field with special behavior
	isPageNumber?: boolean; // Flag for auto-generated page numbers

	// For address fields - layout configuration from page format
	addressLayout?: {
		sender_y: number;
		recipient_y: number;
		blocker_y?: number;
	};

	// For blocks
	childElementIds?: string[]; // Only for type 'block'
	// For elements within blocks
	parentId?: string; // ID of the parent block
}
