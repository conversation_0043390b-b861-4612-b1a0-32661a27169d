# State Management Documentation

## Overview

The application uses a sophisticated state management system built around custom React hooks. This approach provides type safety, predictable state updates, and powerful undo/redo functionality.

## Core State Structure

### Editor State
The main application state consists of two primary arrays:

```typescript
interface EditorState {
  pages: PageSettings[];
  elements: Element[];
}
```

### State Management Hooks

#### useHistory Hook
Provides undo/redo functionality with complete state snapshots:

```typescript
const {
  state: { pages, elements },
  setState: setEditorState,
  undo,
  redo,
  canUndo,
  canRedo,
} = useHistory<EditorState>({ pages: [], elements: [] });
```

**Features:**
- Unlimited undo/redo history
- State snapshots for complete rollback
- Automatic history management
- Memory-efficient storage

#### useHistoryBatch Hook
Enables batched operations for performance optimization:

```typescript
const { beginBatch, updateDraft, commitBatch, isBatching } = useHistoryBatch<EditorState>(setEditorState);
```

**Use Cases:**
- Dragging elements (multiple position updates)
- Multi-element operations
- Complex transformations
- Performance-critical updates

## Specialized State Hooks

### useDocumentState
Manages document-level properties:

```typescript
const {
  documentId,
  currentVersion,
  role,
  isLoading,
  loadError,
  isEditorLocked,
  isReleased,
  allowRelease,
  rawLoadedData,
} = useDocumentState();
```

### useUIState
Handles user interface state:

```typescript
const {
  currentPage,
  clipboard,
  mousePosition,
  activePageId,
  selectedElementId,
  isEditing,
  zoomLevel,
  activeEditor,
  isTextEditorFocused,
  contextMenu,
  highlightVariables,
  showElementBorders,
  showFoldMarks,
  showPageNumbers,
} = useUIState();
```

### useApiData
Manages data from external APIs:

```typescript
const {
  apiFormats,
  documentVariables,
  testData,
  versionHistory,
  apiFonts,
  apiColors,
  templates,
} = useApiData();
```

## Operation Hooks

### useElementOperations
Handles all element-related operations:

```typescript
const {
  handleAddElement,
  handleAddElements,
  handleUpdateElement,
  handleDeleteElement,
  handleCopyElement,
  handleCutElement,
  handlePasteAction,
} = useElementOperations({
  pages,
  elements,
  setEditorState,
  // ... other dependencies
});
```

**Key Operations:**
- **Add Element**: Creates new elements with proper positioning and z-index
- **Update Element**: Handles position, size, content, and property changes
- **Delete Element**: Removes elements and handles parent-child relationships
- **Copy/Cut/Paste**: Clipboard operations with element duplication

### usePageOperations
Manages page-level operations:

```typescript
const {
  handleAddPage,
  handleDeletePage,
  handleReorderPages,
  handleSwapPages,
} = usePageOperations({
  pages,
  elements,
  setEditorState,
  // ... other dependencies
});
```

**Features:**
- Page creation with format templates
- Page deletion with element cleanup
- Page reordering and swapping
- Format validation and conversion

## State Update Patterns

### Immutable Updates
All state changes create new objects to ensure React can detect changes:

```typescript
const newElements = elements.map(el => 
  el.id === updatedElement.id ? updatedElement : el
);
setEditorState({ pages, elements: newElements });
```

### Batched Operations
For performance-critical operations like dragging:

```typescript
// Start batch
beginBatch();

// Multiple updates
updateDraft({ pages, elements: newElements1 });
updateDraft({ pages, elements: newElements2 });

// Commit all changes
commitBatch({ pages, elements: finalElements });
```

### Conditional Updates
State updates are guarded against invalid operations:

```typescript
if (isEditorLocked && isFinal) return;
if (!element.currentPageId) return;
```

## Element Relationships

### Parent-Child Relationships
Elements can be nested within block elements:

```typescript
// Parent block
const blockElement = {
  type: 'block',
  childElementIds: ['child1', 'child2'],
  // ... other properties
};

// Child elements
const childElement = {
  parentId: 'blockId',
  // Position relative to parent
  x: relativeX,
  y: relativeY,
  // ... other properties
};
```

### Z-Index Management
Elements are layered using z-index values:

```typescript
// Move to front
elementToUpdate.zIndex = highestZIndex + 1;

// Move to back
elementToUpdate.zIndex = 0;
// Reindex other elements
```

## Performance Optimizations

### Memoization
State selectors are memoized to prevent unnecessary re-renders:

```typescript
const selectedElement = useMemo(() => 
  elements.find(el => el.id === selectedElementId),
  [elements, selectedElementId]
);
```

### Debounced Updates
Frequent updates are debounced to reduce state churn:

```typescript
const debouncedUpdate = useCallback(
  debounce((element) => handleUpdateElement(element, true), 300),
  [handleUpdateElement]
);
```

### Selective Re-rendering
Components only re-render when their specific dependencies change:

```typescript
useEffect(() => {
  // Only runs when selectedElementId changes
}, [selectedElementId]);
```

## Error Handling

### State Validation
State updates include validation to prevent invalid states:

```typescript
if (!element.currentPageId) {
  console.warn('Cannot add element without currentPageId');
  return;
}
```

### Rollback Capability
Failed operations can be rolled back using the history system:

```typescript
try {
  // Attempt operation
  setEditorState(newState);
} catch (error) {
  // Rollback on error
  undo();
  console.error('Operation failed:', error);
}
```

### Lock Protection
Editor lock prevents modifications in read-only mode:

```typescript
if (isEditorLocked) {
  console.warn('Editor is locked. State change blocked.');
  return;
}
```