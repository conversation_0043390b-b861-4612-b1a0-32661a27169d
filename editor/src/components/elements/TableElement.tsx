import { TableCreateDialog } from "@/components/table/TableCreateDialog";
import type { TestDataRecord } from "@/utils/apiService";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import type { Element } from "../../types/element";
import { Table } from "../table/Table";

interface TableElementProps {
	element: Element;
	testData: TestDataRecord[];
	selectedTestDataIndex: number;
	highlightVariables: boolean;
	onUpdate: (updatedElement: Element, isFinal: boolean) => void;
	onDelete: (elementId: string) => void;
}

export function TableElement({
	element,
	testData,
	selectedTestDataIndex,
	highlightVariables,
	onUpdate,
	onDelete,
}: TableElementProps) {
	if (!element.tableProperties) {
		return (
			<TableCreateDialog
				onCreateTable={(tableProperties) => {
					// Safely sum up column widths and row heights, defaulting to empty arrays
					const widths = tableProperties.columnWidths ?? [];
					const heights = tableProperties.rowHeights ?? [];
					const exactWidthMm = roundToTwoDecimals(
						widths.reduce((sum, w) => sum + w, 0),
					);
					const exactHeightMm = roundToTwoDecimals(
						heights.reduce((sum, h) => sum + h, 0),
					);

					console.log(
						"[TableElement onCreateTable] Received props:",
						JSON.stringify(tableProperties),
					);
					console.log(
						`[TableElement onCreateTable] Calculated exact initial dims (mm): width=${exactWidthMm}, height=${exactHeightMm}`,
					);

					// Update the element with tableProperties and the EXACT calculated dimensions
					onUpdate(
						{
							...element,
							tableProperties, // Include the properties again
							width: exactWidthMm, // Use exact sum
							height: exactHeightMm, // Use exact sum
						},
						true,
					); // Final update, add to history
				}}
				onClose={() => onDelete(element.id)}
			/>
		);
	}

	return (
		<Table
			tableProperties={element.tableProperties}
			containerWidthMm={element.width}
			containerHeightMm={element.height}
			testData={testData}
			selectedTestDataIndex={selectedTestDataIndex}
			highlightVariables={highlightVariables}
		/>
	);
}
