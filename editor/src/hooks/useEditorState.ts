import { useCallback, useEffect, useRef, useState } from "react";
import type { Editor } from "../components/RichTextEditor";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

interface EditorState {
	pages: PageSettings[];
	elements: Element[];
}

interface ContextMenuState {
	visible: boolean;
	screenX: number;
	screenY: number;
	pasteX?: number;
	pasteY?: number;
	pastePageId?: string;
}

interface UseEditorStateProps {
	initialState?: EditorState;
	onStateChange?: (state: EditorState) => void;
}

export function useEditorState({
	initialState,
	onStateChange,
}: UseEditorStateProps = {}) {
	// Core editor state
	const [pages, setPages] = useState<PageSettings[]>(initialState?.pages || []);
	const [elements, setElements] = useState<Element[]>(
		initialState?.elements || [],
	);

	// UI state
	const [currentPage, setCurrentPage] = useState<string | null>(null);
	const [selectedElementId, setSelectedElementId] = useState<string | null>(
		null,
	);
	const [isEditing, setIsEditing] = useState(false);
	const [editingBlockId, setEditingBlockId] = useState<string | null>(null);
	const [zoomLevel, setZoomLevel] = useState(1);
	const [showElementBorders, setShowElementBorders] = useState(false);
	const [showFoldMarks, setShowFoldMarks] = useState(false);
	const [showPageNumbers, setShowPageNumbers] = useState(false);
	const [deletedPageNumberIds, setDeletedPageNumberIds] = useState<Set<string>>(
		new Set(),
	);
	const [highlightVariables, setHighlightVariables] = useState(false);
	const [isEditorLocked, setIsEditorLocked] = useState(false);

	// Mouse and interaction state
	const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
	const [activePageId, setActivePageId] = useState<string | null>(null);
	const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);

	// Editor references
	const [activeEditor, setActiveEditor] = useState<Editor | null>(null);
	const [isTextEditorFocused, setIsTextEditorFocused] = useState(false);
	const contextMenuRef = useRef<HTMLDivElement>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);
	const pageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

	// Clipboard state
	const [clipboard, setClipboard] = useState<
		| Element
		| { block: Element; children: Element[] }
		| { childElement: Element; originalParentId: string }
		| null
	>(null);

	// Combined state setter
	const setEditorState = useCallback(
		(newState: EditorState, _addToHistory?: boolean) => {
			setPages(newState.pages);
			setElements(newState.elements);

			if (onStateChange) {
				onStateChange(newState);
			}
		},
		[onStateChange],
	);

	// Get current editor state
	const getEditorState = useCallback((): EditorState => {
		return { pages, elements };
	}, [pages, elements]);

	// Set current page when pages are loaded
	useEffect(() => {
		if (pages.length > 0 && !currentPage) {
			setCurrentPage(pages[0].id);
		}
	}, [pages, currentPage]);

	// Clear selection when switching pages
	useEffect(() => {
		if (currentPage) {
			setSelectedElementId(null);
			setIsEditing(false);
		}
	}, [currentPage]);

	// Close context menu when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				contextMenuRef.current &&
				!contextMenuRef.current.contains(event.target as Node)
			) {
				setContextMenu(null);
			}
		};

		if (contextMenu?.visible) {
			document.addEventListener("mousedown", handleClickOutside);
		} else {
			document.removeEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [contextMenu]);

	// Auto-hide borders when document is released
	const handleReleaseStateChange = useCallback((isReleased: boolean) => {
		if (isReleased) {
			setShowElementBorders(false);
			setShowFoldMarks(false);
		}
	}, []);

	// Reset state
	const resetState = useCallback(() => {
		setPages([]);
		setElements([]);
		setCurrentPage(null);
		setSelectedElementId(null);
		setIsEditing(false);
		setEditingBlockId(null);
		setZoomLevel(1);
		setShowElementBorders(false);
		setShowFoldMarks(false);
		setShowPageNumbers(false);
		setDeletedPageNumberIds(new Set());
		setHighlightVariables(false);
		setIsEditorLocked(false);
		setMousePosition({ x: 0, y: 0 });
		setActivePageId(null);
		setContextMenu(null);
		setActiveEditor(null);
		setIsTextEditorFocused(false);
		setClipboard(null);
	}, []);

	// Bulk state update
	const updateState = useCallback(
		(
			updates: Partial<{
				pages: PageSettings[];
				elements: Element[];
				currentPage: string | null;
				selectedElementId: string | null;
				isEditing: boolean;
				editingBlockId: string | null;
				zoomLevel: number;
				showElementBorders: boolean;
				showFoldMarks: boolean;
				showPageNumbers: boolean;
				deletedPageNumberIds: Set<string>;
				highlightVariables: boolean;
				isEditorLocked: boolean;
				mousePosition: { x: number; y: number };
				activePageId: string | null;
				contextMenu: ContextMenuState | null;
				activeEditor: Editor | null;
				isTextEditorFocused: boolean;
				clipboard:
					| Element
					| { block: Element; children: Element[] }
					| { childElement: Element; originalParentId: string }
					| null;
			}>,
		) => {
			if (updates.pages !== undefined) setPages(updates.pages);
			if (updates.elements !== undefined) setElements(updates.elements);
			if (updates.currentPage !== undefined)
				setCurrentPage(updates.currentPage);
			if (updates.selectedElementId !== undefined)
				setSelectedElementId(updates.selectedElementId);
			if (updates.isEditing !== undefined) setIsEditing(updates.isEditing);
			if (updates.editingBlockId !== undefined)
				setEditingBlockId(updates.editingBlockId);
			if (updates.zoomLevel !== undefined) setZoomLevel(updates.zoomLevel);
			if (updates.showElementBorders !== undefined)
				setShowElementBorders(updates.showElementBorders);
			if (updates.showFoldMarks !== undefined)
				setShowFoldMarks(updates.showFoldMarks);
			if (updates.showPageNumbers !== undefined)
				setShowPageNumbers(updates.showPageNumbers);
			if (updates.deletedPageNumberIds !== undefined)
				setDeletedPageNumberIds(updates.deletedPageNumberIds);
			if (updates.highlightVariables !== undefined)
				setHighlightVariables(updates.highlightVariables);
			if (updates.isEditorLocked !== undefined)
				setIsEditorLocked(updates.isEditorLocked);
			if (updates.mousePosition !== undefined)
				setMousePosition(updates.mousePosition);
			if (updates.activePageId !== undefined)
				setActivePageId(updates.activePageId);
			if (updates.contextMenu !== undefined)
				setContextMenu(updates.contextMenu);
			if (updates.activeEditor !== undefined)
				setActiveEditor(updates.activeEditor);
			if (updates.isTextEditorFocused !== undefined)
				setIsTextEditorFocused(updates.isTextEditorFocused);
			if (updates.clipboard !== undefined) setClipboard(updates.clipboard);
		},
		[],
	);

	return {
		// State
		pages,
		elements,
		currentPage,
		selectedElementId,
		isEditing,
		editingBlockId,
		zoomLevel,
		showElementBorders,
		showFoldMarks,
		showPageNumbers,
		deletedPageNumberIds,
		highlightVariables,
		isEditorLocked,
		mousePosition,
		activePageId,
		contextMenu,
		activeEditor,
		isTextEditorFocused,
		clipboard,

		// Setters
		setPages,
		setElements,
		setCurrentPage,
		setSelectedElementId,
		setIsEditing,
		setEditingBlockId,
		setZoomLevel,
		setShowElementBorders,
		setShowFoldMarks,
		setShowPageNumbers,
		setDeletedPageNumberIds,
		setHighlightVariables,
		setIsEditorLocked,
		setMousePosition,
		setActivePageId,
		setContextMenu,
		setActiveEditor,
		setIsTextEditorFocused,
		setClipboard,

		// Refs
		contextMenuRef,
		scrollContainerRef,
		pageRefs,

		// Methods
		setEditorState,
		getEditorState,
		resetState,
		updateState,
		handleReleaseStateChange,
	};
}
