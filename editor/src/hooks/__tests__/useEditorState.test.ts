import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import {
	EditorStateFactory,
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import { useEditorState } from "../useEditorState";

describe("useEditorState", () => {
	// Test state initialization
	describe("initialization", () => {
		it("should initialize with empty state when no initialState is provided", () => {
			const { result } = renderHook(() => useEditorState());

			expect(result.current.pages).toEqual([]);
			expect(result.current.elements).toEqual([]);
			expect(result.current.currentPage).toBeNull();
			expect(result.current.selectedElementId).toBeNull();
			expect(result.current.isEditing).toBe(false);
			expect(result.current.editingBlockId).toBeNull();
			expect(result.current.zoomLevel).toBe(1);
		});

		it("should initialize with provided initialState", () => {
			const mockState = EditorStateFactory.createSimpleState();
			const { result } = renderHook(() =>
				useEditorState({ initialState: mockState }),
			);

			expect(result.current.pages).toEqual(mockState.pages);
			expect(result.current.elements).toEqual(mockState.elements);
			expect(result.current.currentPage).toBe(mockState.pages[0].id);
		});

		it("should call onStateChange callback when state changes", () => {
			const onStateChangeMock = vi.fn();
			const mockState = EditorStateFactory.createSimpleState();
			const { result } = renderHook(() =>
				useEditorState({
					initialState: mockState,
					onStateChange: onStateChangeMock,
				}),
			);

			const newPage = PageFactory.createPage({ id: "new-page" });

			act(() => {
				result.current.setEditorState({
					pages: [...mockState.pages, newPage],
					elements: mockState.elements,
				});
			});

			expect(onStateChangeMock).toHaveBeenCalledWith({
				pages: [...mockState.pages, newPage],
				elements: mockState.elements,
			});
		});
	});

	// Test state updates
	describe("state updates", () => {
		it("should update pages state", () => {
			const { result } = renderHook(() => useEditorState());
			const newPages = [PageFactory.createPage()];

			act(() => {
				result.current.setPages(newPages);
			});

			expect(result.current.pages).toEqual(newPages);
			expect(result.current.currentPage).toBe(newPages[0].id);
		});

		it("should update elements state", () => {
			const { result } = renderHook(() => useEditorState());
			const newElements = [ElementFactory.createTextElement()];

			act(() => {
				result.current.setElements(newElements);
			});

			expect(result.current.elements).toEqual(newElements);
		});

		it("should update selected element ID", () => {
			const { result } = renderHook(() => useEditorState());
			const elementId = "test-element-id";

			act(() => {
				result.current.setSelectedElementId(elementId);
			});

			expect(result.current.selectedElementId).toBe(elementId);
		});

		it("should update current page", () => {
			const mockState = EditorStateFactory.createStateWithConfig({
				pageCount: 2,
			});
			const { result } = renderHook(() =>
				useEditorState({ initialState: mockState }),
			);
			const secondPageId = mockState.pages[1].id;

			act(() => {
				result.current.setCurrentPage(secondPageId);
			});

			expect(result.current.currentPage).toBe(secondPageId);
			expect(result.current.selectedElementId).toBeNull(); // Should clear selection
		});

		it("should update multiple states with updateState", () => {
			const { result } = renderHook(() => useEditorState());

			act(() => {
				result.current.updateState({
					zoomLevel: 1.5,
					showElementBorders: true,
					showFoldMarks: true,
				});
			});

			expect(result.current.zoomLevel).toBe(1.5);
			expect(result.current.showElementBorders).toBe(true);
			expect(result.current.showFoldMarks).toBe(true);
		});
	});

	// Test derived state calculations
	describe("derived state calculations", () => {
		it("should get current editor state", () => {
			const mockState = EditorStateFactory.createSimpleState();
			const { result } = renderHook(() =>
				useEditorState({ initialState: mockState }),
			);

			const currentState = result.current.getEditorState();

			expect(currentState).toEqual({
				pages: mockState.pages,
				elements: mockState.elements,
			});
		});

		it("should automatically set current page when pages are loaded", () => {
			const { result } = renderHook(() => useEditorState());
			const newPages = [PageFactory.createPage({ id: "auto-page" })];

			act(() => {
				result.current.setPages(newPages);
			});

			expect(result.current.currentPage).toBe("auto-page");
		});

		it("should handle release state change", () => {
			const { result } = renderHook(() => useEditorState());

			act(() => {
				result.current.setShowElementBorders(true);
				result.current.setShowFoldMarks(true);
			});

			expect(result.current.showElementBorders).toBe(true);
			expect(result.current.showFoldMarks).toBe(true);

			act(() => {
				result.current.handleReleaseStateChange(true);
			});

			expect(result.current.showElementBorders).toBe(false);
			expect(result.current.showFoldMarks).toBe(false);
		});
	});

	// Test state persistence and restoration
	describe("state persistence and restoration", () => {
		it("should reset state to initial values", () => {
			const mockState = EditorStateFactory.createComplexState();
			const { result } = renderHook(() =>
				useEditorState({ initialState: mockState }),
			);

			act(() => {
				result.current.setZoomLevel(2);
				result.current.setShowElementBorders(true);
				result.current.setSelectedElementId("some-id");
			});

			expect(result.current.zoomLevel).toBe(2);
			expect(result.current.showElementBorders).toBe(true);
			expect(result.current.selectedElementId).toBe("some-id");

			act(() => {
				result.current.resetState();
			});

			expect(result.current.pages).toEqual([]);
			expect(result.current.elements).toEqual([]);
			expect(result.current.currentPage).toBeNull();
			expect(result.current.selectedElementId).toBeNull();
			expect(result.current.zoomLevel).toBe(1);
			expect(result.current.showElementBorders).toBe(false);
		});

		it("should update editor state with setEditorState", () => {
			const { result } = renderHook(() => useEditorState());
			const newState = EditorStateFactory.createSimpleState();

			act(() => {
				result.current.setEditorState(newState);
			});

			expect(result.current.pages).toEqual(newState.pages);
			expect(result.current.elements).toEqual(newState.elements);
		});
	});

	// Test context menu behavior
	describe("context menu behavior", () => {
		it("should set and clear context menu state", () => {
			const { result } = renderHook(() => useEditorState());
			const contextMenuState = {
				visible: true,
				screenX: 100,
				screenY: 200,
				pasteX: 150,
				pasteY: 250,
				pastePageId: "page-1",
			};

			act(() => {
				result.current.setContextMenu(contextMenuState);
			});

			expect(result.current.contextMenu).toEqual(contextMenuState);

			act(() => {
				result.current.setContextMenu(null);
			});

			expect(result.current.contextMenu).toBeNull();
		});
	});

	// Test clipboard operations
	describe("clipboard operations", () => {
		it("should set and retrieve clipboard content", () => {
			const { result } = renderHook(() => useEditorState());
			const element = ElementFactory.createTextElement();

			act(() => {
				result.current.setClipboard(element);
			});

			expect(result.current.clipboard).toEqual(element);
		});

		it("should handle block clipboard content", () => {
			const { result } = renderHook(() => useEditorState());
			const blockElement = ElementFactory.createBlockElement();
			const childElements = [
				ElementFactory.createTextElement(),
				ElementFactory.createShapeElement(),
			];
			const blockClipboard = {
				block: blockElement,
				children: childElements,
			};

			act(() => {
				result.current.setClipboard(blockClipboard);
			});

			expect(result.current.clipboard).toEqual(blockClipboard);
		});
	});
});
