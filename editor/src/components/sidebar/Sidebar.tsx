import type { Editor } from "@tiptap/react";
import {
	FileCode,
	FileText,
	History,
	Image,
	LayoutGrid,
	Square,
	Table,
	Type,
} from "lucide-react";
import { useEffect, useState } from "react";
import { TableFormatToolbar } from "@/components/sidebar/TableFormatToolbar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import type { BatchDeleteOperation, Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import type {
	ColorInfo,
	FontInfo,
	PageFormat,
	TemplateInfo,
	VersionHistoryEntry,
} from "@/utils/apiService";
import { BlockToolbar } from "./BlockToolbar";
import { ElementToolbar } from "./ElementToolbar";
import { PageToolbar } from "./PageToolbar";
import { TemplateToolbar } from "./TemplateToolbar";
import { TextFormatToolbar } from "./TextFormatToolbar";
import { VersionToolbar } from "./VersionToolbar";

// Helper function type for determineWeight
type DetermineWeightFunc = (style: string | undefined) => string;

// Consistent styling for all tab content
const TAB_CONTENT_CLASSES = "m-0 p-4 h-full";

interface SidebarProps {
	pages: PageSettings[];
	elements: Element[];
	onAddPage: (format: string) => void;
	onAddElement: (element: Element | BatchDeleteOperation) => void;
	onAddElements: (elements: Element[]) => void;
	onUpdateElement: (element: Element) => void;
	editor: Editor | null;
	currentPageId?: string;
	selectedElement: Element | null;
	isEditing?: boolean;
	currentPageFormat?: string;
	apiFormats?: PageFormat[];
	documentVariables?: string[];
	apiFonts: FontInfo[];
	isLoadingFonts: boolean;
	loadedFonts: Set<string>;
	determineWeight: DetermineWeightFunc;
	showPageNumbers: boolean;
	onTogglePageNumbers: (show: boolean) => void;
	showElementBorders: boolean;
	setShowElementBorders: (show: boolean) => void;
	showFoldMarks: boolean;
	setShowFoldMarks: (show: boolean) => void;
	versionHistory: VersionHistoryEntry[];
	currentVersion: string;
	onVersionChange: (newVersion: string) => void;
	onRefreshHistory: () => Promise<void>;
	isBlockEditingActive?: boolean;
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
	role?: string;
	isReleased?: boolean;
	templates: TemplateInfo[];
	isLoadingTemplates: boolean;
	templatesError: string | null;
	onInsertTemplate: (
		templateData: { blockElement: Element; childElements: Element[] },
		insertX?: number,
		insertY?: number,
	) => void;
	onRefreshTemplates: () => Promise<void>;
}

export function Sidebar({
	elements,
	onAddPage,
	onAddElement,
	onAddElements,
	onUpdateElement,
	editor,
	currentPageId,
	selectedElement,
	isEditing = false,
	currentPageFormat,
	apiFormats = [],
	documentVariables = [],
	apiFonts,
	isLoadingFonts,
	loadedFonts,
	determineWeight,
	showPageNumbers,
	onTogglePageNumbers,
	showElementBorders,
	setShowElementBorders,
	showFoldMarks,
	setShowFoldMarks,
	pages,
	versionHistory,
	currentVersion,
	onVersionChange,
	onRefreshHistory,
	isBlockEditingActive = false,
	apiColors = [],
	isLoadingColors = false,
	colorError = null,
	role,
	isReleased = false,
	templates,
	isLoadingTemplates,
	templatesError,
	onInsertTemplate,
	onRefreshTemplates,
}: SidebarProps) {
	const [activeTab, setActiveTab] = useState<string>("page");

	// Automatically switch to text tab when editor is active
	useEffect(() => {
		if (editor) {
			setActiveTab("text");
		}
	}, [editor]);

	// Automatically switch to the appropriate tab based on the selected element type
	useEffect(() => {
		if (selectedElement) {
			switch (selectedElement.type) {
				case "text":
					setActiveTab("text");
					break;
				case "shape":
					setActiveTab("form");
					break;
				case "image":
					setActiveTab("image");
					break;
				case "table":
					if (editor) {
						setActiveTab("text");
					} else {
						setActiveTab("table");
					}
					break;
				case "block":
					if (isBlockEditingActive) {
						setActiveTab("text");
					} else {
						setActiveTab("block");
					}
					break;
				case "address":
					setActiveTab("page");
					break;
				default:
					break;
			}
		}
	}, [selectedElement, editor, isBlockEditingActive]);

	return (
		<aside className="w-80 h-[calc(100vh-96px)] bg-white border-r border-gray-200 flex flex-col overflow-hidden sidebar-container">
			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="flex-1 flex h-full"
			>
				<TabsList className="w-16 border-r h-full flex flex-col">
					{!isBlockEditingActive && (
						<TabsTrigger
							value="page"
							className="flex flex-col items-center gap-1 py-3 px-0"
						>
							<FileText className="w-5 h-5" />
							<span className="text-xs">Seite</span>
						</TabsTrigger>
					)}
					<TabsTrigger
						value="text"
						className="flex flex-col items-center gap-1 py-3 px-0"
					>
						<Type className="w-5 h-5" />
						<span className="text-xs">Text</span>
					</TabsTrigger>
					<TabsTrigger
						value="form"
						className="flex flex-col items-center gap-1 py-3 px-0"
					>
						<Square className="w-5 h-5" />
						<span className="text-xs">Form</span>
					</TabsTrigger>
					<TabsTrigger
						value="image"
						className="flex flex-col items-center gap-1 py-3 px-0"
					>
						<Image className="w-5 h-5" />
						<span className="text-xs">Bild</span>
					</TabsTrigger>
					<TabsTrigger
						value="table"
						className="flex flex-col items-center gap-1 py-3 px-0"
					>
						<Table className="w-5 h-5" />
						<span className="text-xs">Tabelle</span>
					</TabsTrigger>
					{!isBlockEditingActive && (
						<TabsTrigger
							value="block"
							className="flex flex-col items-center gap-1 py-3 px-0"
						>
							<LayoutGrid className="w-5 h-5" />
							<span className="text-xs">Baustein</span>
						</TabsTrigger>
					)}
					{!isBlockEditingActive && (
						<TabsTrigger
							value="templates"
							className="flex flex-col items-center gap-1 py-3 px-0"
							onClick={() => {
								if (onRefreshTemplates) {
									onRefreshTemplates();
								}
							}}
						>
							<FileCode className="w-5 h-5" />
							<span className="text-xs">Vorlagen</span>
						</TabsTrigger>
					)}
					{!isBlockEditingActive && (
						<TabsTrigger
							value="version"
							className="flex flex-col items-center gap-1 py-3 px-0"
							onClick={() => {
								if (onRefreshHistory) {
									onRefreshHistory();
								}
							}}
						>
							<History className="w-5 h-5" />
							<span className="text-xs">Historie</span>
						</TabsTrigger>
					)}
				</TabsList>
				<div className="flex-1 overflow-y-auto">
					{!isBlockEditingActive && (
						<TabsContent value="page" className={TAB_CONTENT_CLASSES}>
							<PageToolbar
								onAddPage={onAddPage}
								onAddElement={onAddElement}
								onAddElements={onAddElements}
								currentPageId={currentPageId}
								elements={elements}
								currentPageFormat={currentPageFormat}
								apiFormats={apiFormats}
								showPageNumbers={showPageNumbers}
								onTogglePageNumbers={onTogglePageNumbers}
								showElementBorders={showElementBorders}
								setShowElementBorders={setShowElementBorders}
								showFoldMarks={showFoldMarks}
								setShowFoldMarks={setShowFoldMarks}
								pages={pages}
								isReleased={isReleased}
							/>
						</TabsContent>
					)}
					<TabsContent value="text" className={TAB_CONTENT_CLASSES}>
						<TextFormatToolbar
							editor={editor}
							onAddElement={onAddElement}
							selectedElement={
								selectedElement?.type === "text" ? selectedElement : null
							}
							onUpdateElement={onUpdateElement}
							isEditing={isEditing}
							documentVariables={documentVariables}
							apiFonts={apiFonts}
							isLoadingFonts={isLoadingFonts}
							loadedFonts={loadedFonts}
							determineWeight={determineWeight}
							apiColors={apiColors}
							isLoadingColors={isLoadingColors}
							colorError={colorError}
						/>
					</TabsContent>
					<TabsContent value="form" className={TAB_CONTENT_CLASSES}>
						<ElementToolbar
							onAddElement={onAddElement}
							elementType="shape"
							selectedElement={
								selectedElement?.type === "shape" ? selectedElement : null
							}
							onUpdateElement={onUpdateElement}
							elements={elements}
							apiColors={apiColors}
							isLoadingColors={isLoadingColors}
							colorError={colorError}
							role={role}
						/>
					</TabsContent>
					<TabsContent value="image" className={TAB_CONTENT_CLASSES}>
						<ElementToolbar
							onAddElement={onAddElement}
							elementType="image"
							selectedElement={
								selectedElement?.type === "image" ? selectedElement : null
							}
							onUpdateElement={onUpdateElement}
							elements={elements}
							apiColors={apiColors}
							isLoadingColors={isLoadingColors}
							colorError={colorError}
							role={role}
						/>
					</TabsContent>
					<TabsContent value="table" className={TAB_CONTENT_CLASSES}>
						<TableFormatToolbar
							onAddElement={onAddElement}
							selectedElement={selectedElement}
							onUpdateElement={onUpdateElement}
							apiColors={apiColors}
							isLoadingColors={isLoadingColors}
							colorError={colorError}
						/>
					</TabsContent>
					{!isBlockEditingActive && (
						<TabsContent value="block" className={TAB_CONTENT_CLASSES}>
							<BlockToolbar
								onAddElement={onAddElement}
								selectedElement={
									selectedElement?.type === "block" ? selectedElement : null
								}
								onUpdateElement={onUpdateElement}
								currentPageId={currentPageId}
							/>
						</TabsContent>
					)}
					{!isBlockEditingActive && (
						<TabsContent value="templates" className={TAB_CONTENT_CLASSES}>
							<TemplateToolbar
								templates={templates}
								onInsertTemplate={onInsertTemplate}
								isLoading={isLoadingTemplates}
								error={templatesError}
							/>
						</TabsContent>
					)}
					{!isBlockEditingActive && (
						<TabsContent value="version" className={TAB_CONTENT_CLASSES}>
							<VersionToolbar
								versionHistory={versionHistory}
								currentVersion={currentVersion}
								onVersionChange={onVersionChange}
							/>
						</TabsContent>
					)}
				</div>
			</Tabs>
		</aside>
	);
}
