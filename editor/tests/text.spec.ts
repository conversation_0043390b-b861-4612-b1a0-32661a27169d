import { expect, test } from "@playwright/test";

const host = process.env.EDITOR_TEST_HOST;
if (!host) {
	throw new Error("EDITOR_TEST_HOST is not set");
}

test.beforeEach(async ({ page }) => {
	await page.goto(`${host}/editor?id=105&version=latest`);
});

test("text basic text input", async ({ page }) => {
	const elId = "d92f0777-2a18-480f-8d1a-a4ea6e4c1f90";

	// locator with data-element-id
	const element = await page.locator(`[data-element-id="${elId}"]`);
	await expect(element).toBeVisible();

	await element.click();

	await element.dblclick();

	const editor = await page.locator(".tiptap");
	await expect(editor).toBeVisible();

	await editor.click();

	// ctrl + a
	await page.keyboard.press("ControlOrMeta+a");

	// backspace
	await page.keyboard.press("Backspace");

	// expect the element to be empty after deleting the text
	await expect(editor).toHaveText("");

	await editor.click();

	await page.keyboard.type("Hello World");

	await expect(editor).toHaveText("Hello World");
});
