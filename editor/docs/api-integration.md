# API Integration Documentation

## Overview

The application integrates with several external APIs to provide dynamic content, fonts, colors, and document management capabilities. This document outlines the API integration patterns and available services.

## API Service Layer

### Core Service (`src/utils/apiService.ts`)

The main API service provides functions for interacting with external endpoints:

```typescript
// Font management
export async function fetchAvailableFonts(): Promise<FontData[]>

// Color management  
export async function fetchAvailableColors(): Promise<ColorData[]>

// Template management
export async function fetchTemplates(): Promise<TemplateData[]>
export async function saveTemplate(name: string, data: TemplateData): Promise<void>

// Document management
export async function fetchSessionStatus(): Promise<SessionStatus>

// Page formats
export async function fetchPageFormats(): Promise<PageFormat[]>
```

## Font Integration

### Font Loading System
The application dynamically loads fonts from external sources:

```typescript
interface FontData {
  family: string;
  name: string;
  style?: string;
  path: string;
  weight?: string;
}
```

### Font Loading Process
1. **Fetch Available Fonts**: API call to get font list
2. **Process Font Data**: Validate and normalize font information
3. **Generate CSS**: Create @font-face rules dynamically
4. **Load Fonts**: Force browser to download font files
5. **Track Loading**: Monitor font availability

```typescript
// Example font loading implementation
useEffect(() => {
  const loadFonts = async () => {
    const fonts = await fetchAvailableFonts();
    
    // Create dynamic CSS
    const styleElement = document.createElement('style');
    let fontFaceRules = '';
    
    fonts.forEach(font => {
      fontFaceRules += `
        @font-face {
          font-family: '${font.family}';
          src: url('${font.path}') format('truetype');
          font-weight: ${determineWeight(font.style)};
          font-style: ${font.style?.includes('italic') ? 'italic' : 'normal'};
        }
      `;
    });
    
    styleElement.textContent = fontFaceRules;
    document.head.appendChild(styleElement);
  };
  
  loadFonts();
}, []);
```

## Color Management

### Color API Integration
Colors are fetched from an external API and cached locally:

```typescript
interface ColorData {
  id: string;
  name: string;
  hex: string;
  rgb: string;
  category?: string;
}
```

### Color Loading Process
1. **Fetch Colors**: API call to get available colors
2. **Cache Colors**: Store in application state
3. **Provide Palette**: Make available in color pickers
4. **Handle Updates**: Refresh when needed

## Template System

### Template Data Structure
```typescript
interface TemplateData {
  blockElement: Element;
  childElements: Element[];
}
```

### Template Operations
- **Save Template**: Convert selected elements to template
- **Load Templates**: Fetch available templates from API
- **Insert Template**: Add template elements to document

```typescript
// Save template example
const handleSaveAsTemplate = async (blockElement: Element, childElements: Element[]) => {
  const templateData = {
    blockElement: {
      ...blockElement,
      currentPageId: "", // Will be set on insertion
    },
    childElements: childElements.map(child => ({
      ...child,
      currentPageId: "",
      parentId: blockElement.id,
    }))
  };
  
  const templateName = prompt("Enter template name:");
  if (templateName) {
    await saveTemplate(templateName, templateData);
    await refreshTemplates();
  }
};
```

## Document Management

### Session Management
Track document editing sessions and user permissions:

```typescript
interface SessionStatus {
  documentId: string;
  userId: string;
  role: 'editor' | 'viewer' | 'admin';
  isLocked: boolean;
  lastModified: string;
}
```

### Version Control
Manage document versions through API:

```typescript
interface VersionHistory {
  version: string;
  timestamp: string;
  author: string;
  changes: string[];
}
```

## Page Format Integration

### Format API
Page formats are loaded from API with complete specifications:

```typescript
interface PageFormat {
  name: string;
  description: string;
  width: number;  // in mm
  height: number; // in mm
  trim_top: number;
  trim_right: number;
  trim_bottom: number;
  trim_left: number;
  address_x: number;
  address_y: number;
  pagenumber_w?: number | null;
  pagenumber_y?: number | null;
}
```

### Format Usage
```typescript
// Create page with API format
const handleAddPage = (formatName: string) => {
  const apiFormat = apiFormats.find(f => f.name === formatName);
  if (apiFormat) {
    const newPage: PageSettings = {
      id: uuidv4(),
      format: apiFormat.name,
      width: apiFormat.width / 10, // Convert mm to cm
      height: apiFormat.height / 10,
      trim_top: apiFormat.trim_top / 10,
      // ... other properties
    };
    
    addPage(newPage);
  }
};
```

## Error Handling

### API Error Patterns
```typescript
// Centralized error handling
async function apiCall<T>(endpoint: string): Promise<T> {
  try {
    const response = await fetch(endpoint);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}
```

### Fallback Strategies
- **Font Fallbacks**: Use system fonts when API fonts fail
- **Color Fallbacks**: Provide default color palette
- **Template Fallbacks**: Allow local template creation
- **Format Fallbacks**: Use built-in page formats

## Caching Strategy

### Client-Side Caching
```typescript
// Cache API responses
const useApiCache = <T>(key: string, fetcher: () => Promise<T>) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const cached = localStorage.getItem(key);
    if (cached) {
      setData(JSON.parse(cached));
    } else {
      setLoading(true);
      fetcher()
        .then(result => {
          setData(result);
          localStorage.setItem(key, JSON.stringify(result));
        })
        .catch(err => setError(err.message))
        .finally(() => setLoading(false));
    }
  }, [key]);
  
  return { data, loading, error };
};
```

## Authentication

### API Authentication
```typescript
// Add authentication headers
const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
  const token = getAuthToken();
  
  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};
```

## Real-time Updates

### WebSocket Integration
For real-time collaboration features:

```typescript
// WebSocket connection for live updates
const useWebSocket = (documentId: string) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  
  useEffect(() => {
    const ws = new WebSocket(`ws://api.example.com/documents/${documentId}`);
    
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      handleRemoteUpdate(update);
    };
    
    setSocket(ws);
    
    return () => ws.close();
  }, [documentId]);
  
  return socket;
};
```

## Performance Optimization

### API Call Optimization
- **Debounced Requests**: Prevent excessive API calls
- **Request Batching**: Combine multiple requests
- **Lazy Loading**: Load data when needed
- **Background Sync**: Update data in background

```typescript
// Debounced API calls
const debouncedSave = useCallback(
  debounce(async (data) => {
    await saveDocument(data);
  }, 1000),
  []
);
```

## Testing API Integration

### Mock API Responses
```typescript
// Mock API for testing
const mockApiService = {
  fetchAvailableFonts: jest.fn().mockResolvedValue([
    { family: 'Arial', name: 'Arial', path: '/fonts/arial.ttf' }
  ]),
  fetchAvailableColors: jest.fn().mockResolvedValue([
    { id: '1', name: 'Red', hex: '#FF0000' }
  ]),
};
```

### Integration Testing
- Test API error scenarios
- Verify data transformation
- Test caching behavior
- Validate authentication flows