import { act, renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import type { Element } from "../../types/element";
import {
	calculateSnapGuides,
	type SnapCalculationParams,
	type SnapGuideInfo,
	updateSnapGuides,
	useSnapGuides,
} from "../snapGuides";

// Mock the unitConversion module
vi.mock("../unitConversion", () => ({
	pxToMm: vi.fn((px: number) => px / 3.779528),
}));

describe("snapGuides", () => {
	let mockElements: Element[];

	beforeEach(() => {
		// Create mock elements for testing
		mockElements = [
			{
				id: "element1",
				type: "text",
				x: 10,
				y: 10,
				width: 50,
				height: 20,
				currentPageId: "page1",
				content: "Test Element 1",
			},
			{
				id: "element2",
				type: "shape",
				x: 80,
				y: 30,
				width: 40,
				height: 30,
				currentPageId: "page1",
				content: "",
			},
			{
				id: "element3",
				type: "text",
				x: 20,
				y: 60,
				width: 60,
				height: 25,
				currentPageId: "page1",
				content: "Test Element 3",
			},
			{
				id: "block1",
				type: "block",
				x: 100,
				y: 100,
				width: 200,
				height: 150,
				currentPageId: "page1",
				content: "",
			},
			{
				id: "child1",
				type: "text",
				x: 10,
				y: 10,
				width: 50,
				height: 20,
				currentPageId: "page1",
				parentId: "block1",
				content: "Child Element",
			},
		] as Element[];
	});

	describe("calculateSnapGuides", () => {
		it("should return empty result when target element is not found", () => {
			const params: SnapCalculationParams = {
				elementId: "nonexistent",
				newX: 15,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
			};

			const result = calculateSnapGuides(params);

			expect(result).toEqual({
				snapX: null,
				snapY: null,
				guides: [],
			});
		});

		it("should return empty result when no relevant elements exist", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 15,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: [mockElements[0]], // Only the target element
			};

			const result = calculateSnapGuides(params);

			expect(result).toEqual({
				snapX: null,
				snapY: null,
				guides: [],
			});
		});

		it("should calculate horizontal snap guides for left edge alignment", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 79, // Close to element2's left edge (80)
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			expect(result.snapX).toBe(80); // Should snap to element2's left edge
			expect(result.guides).toHaveLength(1);
			expect(result.guides[0]).toMatchObject({
				type: "vertical",
				position: 80,
			});
		});

		it("should calculate vertical snap guides for top edge alignment", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 15,
				newY: 29, // Close to element2's top edge (30)
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			expect(result.snapY).toBe(30); // Should snap to element2's top edge
			expect(result.guides).toHaveLength(1);
			expect(result.guides[0]).toMatchObject({
				type: "horizontal",
				position: 30,
			});
		});

		it("should calculate center alignment snaps", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 49, // Close to element2's center X (80 + 40/2 = 100, element1 center would be 49 + 50/2 = 74, close to 100)
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				snapThreshold: 5,
			};

			const result = calculateSnapGuides(params);

			// Element2 center X is 100, element1 center would be at newX + width/2 = 49 + 25 = 74
			// This is within threshold of 5, so should snap
			expect(result.snapX).toBe(100); // Should snap to element2's center X
		});

		it("should respect snap threshold", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 85, // 5 units away from element2's left edge (80)
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				snapThreshold: 3, // Threshold is smaller than distance
			};

			const result = calculateSnapGuides(params);

			expect(result.snapX).toBeNull(); // Should not snap due to threshold
			expect(result.guides).toHaveLength(0);
		});

		it("should handle elements within blocks correctly", () => {
			const params: SnapCalculationParams = {
				elementId: "child1",
				newX: 15,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				parentBlockId: "block1",
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			// Should only consider other elements within the same block
			// Since there's only one child element, no snapping should occur
			expect(result.snapX).toBeNull();
			expect(result.snapY).toBeNull();
		});

		it("should filter snap points based on moving handle", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 79,
				newY: 29,
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				snapThreshold: 2,
				movingHandle: "right", // Only right edge moves
			};

			const result = calculateSnapGuides(params);

			// Should only consider right edge and center X for horizontal snapping
			// Should not consider vertical snapping since handle doesn't move vertically
			expect(result.snapY).toBeNull();
		});

		it("should handle table elements with border contributions", () => {
			const tableElement: Element = {
				id: "table1",
				type: "table",
				x: 50,
				y: 50,
				width: 100,
				height: 80,
				currentPageId: "page1",
				content: "",
				tableProperties: {
					rows: 2,
					columns: 2,
					cells: [
						[
							{
								content: "A1",
								borderWidths: { left: 2, top: 2, right: 1, bottom: 1 },
							},
							{
								content: "B1",
								borderWidths: { left: 1, top: 2, right: 2, bottom: 1 },
							},
						],
						[
							{
								content: "A2",
								borderWidths: { left: 2, top: 1, right: 1, bottom: 2 },
							},
							{
								content: "B2",
								borderWidths: { left: 1, top: 1, right: 2, bottom: 2 },
							},
						],
					],
				},
			} as Element;

			const elementsWithTable = [...mockElements, tableElement];

			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 49, // Close to table's adjusted left edge
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: elementsWithTable,
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			// Should account for table border contributions in snap calculations
			expect(result.snapX).toBeDefined();
		});

		it("should prioritize same-type alignments", () => {
			// Add another element with center alignment opportunity
			const additionalElement: Element = {
				id: "element4",
				type: "text",
				x: 75, // Center X = 100, same as element2
				y: 40,
				width: 50,
				height: 20,
				currentPageId: "page1",
				content: "Element 4",
			} as Element;

			const elementsWithAdditional = [...mockElements, additionalElement];

			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 74, // Close to left edge of element4 (75)
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: elementsWithAdditional,
				snapThreshold: 5,
			};

			const result = calculateSnapGuides(params);

			// Should snap to the closest edge (element4's left edge at 75)
			expect(result.snapX).toBe(75);
		});

		it("should handle multiple snap opportunities and choose the best one", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 21, // Close to element3's left edge (20) - distance 1
				newY: 31, // Close to element2's top edge (30) - distance 1
				elementWidth: 50,
				elementHeight: 20,
				elements: mockElements,
				snapThreshold: 3,
			};

			const result = calculateSnapGuides(params);

			expect(result.snapX).toBe(20); // Should snap to element3's left edge
			expect(result.snapY).toBe(30); // Should snap to element2's top edge
			expect(result.guides).toHaveLength(2);
		});
	});

	describe("updateSnapGuides", () => {
		it("should create basic snap guides state", () => {
			const guides: SnapGuideInfo[] = [
				{ type: "vertical", position: 50, start: 0, end: 100 },
			];

			const result = updateSnapGuides(true, guides, mockElements);

			expect(result).toEqual({
				visible: true,
				guides,
				relativeToBlockId: null,
			});
		});

		it("should include block information when relative to block", () => {
			const guides: SnapGuideInfo[] = [
				{ type: "horizontal", position: 25, start: 0, end: 200 },
			];

			const result = updateSnapGuides(true, guides, mockElements, "block1");

			expect(result).toEqual({
				visible: true,
				guides,
				relativeToBlockId: "block1",
				blockX: 100,
				blockY: 100,
				blockWidth: 200,
				blockHeight: 150,
			});
		});

		it("should handle missing block gracefully", () => {
			const guides: SnapGuideInfo[] = [
				{ type: "vertical", position: 75, start: 0, end: 50 },
			];

			const result = updateSnapGuides(
				true,
				guides,
				mockElements,
				"nonexistent",
			);

			expect(result).toEqual({
				visible: true,
				guides,
				relativeToBlockId: null,
			});
		});

		it("should handle invisible guides", () => {
			const guides: SnapGuideInfo[] = [
				{ type: "vertical", position: 50, start: 0, end: 100 },
			];

			const result = updateSnapGuides(false, guides, mockElements);

			expect(result.visible).toBe(false);
			expect(result.guides).toEqual(guides);
		});
	});

	describe("useSnapGuides hook", () => {
		it("should initialize with empty state", () => {
			const { result } = renderHook(() => useSnapGuides(mockElements));

			expect(result.current.snapGuides).toEqual({
				visible: false,
				guides: [],
				relativeToBlockId: null,
			});
		});

		it("should update snap guides state", () => {
			const { result } = renderHook(() => useSnapGuides(mockElements));

			const guides: SnapGuideInfo[] = [
				{ type: "vertical", position: 50, start: 0, end: 100 },
			];

			act(() => {
				result.current.updateSnapGuidesToState(true, guides);
			});

			expect(result.current.snapGuides).toEqual({
				visible: true,
				guides,
				relativeToBlockId: null,
			});
		});

		it("should calculate element snap guides", () => {
			const { result } = renderHook(() => useSnapGuides(mockElements));

			const snapResult = result.current.calculateElementSnapGuides({
				elementId: "element1",
				newX: 79,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				snapThreshold: 2,
			});

			expect(snapResult.snapX).toBe(80);
			expect(snapResult.guides).toHaveLength(1);
		});

		it("should clear snap guides", () => {
			const { result } = renderHook(() => useSnapGuides(mockElements));

			// First set some guides
			const guides: SnapGuideInfo[] = [
				{ type: "vertical", position: 50, start: 0, end: 100 },
			];

			act(() => {
				result.current.updateSnapGuidesToState(true, guides);
			});

			expect(result.current.snapGuides.visible).toBe(true);

			// Then clear them
			act(() => {
				result.current.clearSnapGuides();
			});

			expect(result.current.snapGuides).toEqual({
				visible: false,
				guides: [],
				relativeToBlockId: null,
			});
		});

		it("should update when elements change", () => {
			const { result, rerender } = renderHook(
				({ elements }) => useSnapGuides(elements),
				{ initialProps: { elements: mockElements } },
			);

			// Calculate with initial elements
			const initialResult = result.current.calculateElementSnapGuides({
				elementId: "element1",
				newX: 79,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				snapThreshold: 2,
			});

			expect(initialResult.snapX).toBe(80);

			// Update elements - remove element2 and element3 to ensure no snapping
			const newElements = mockElements.filter(
				(el) =>
					el.id === "element1" || el.id === "block1" || el.id === "child1",
			);
			rerender({ elements: newElements });

			// Calculate with updated elements
			const updatedResult = result.current.calculateElementSnapGuides({
				elementId: "element1",
				newX: 79,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				snapThreshold: 2,
			});

			expect(updatedResult.snapX).toBeNull(); // No elements to snap to
		});

		it("should handle block-relative updates", () => {
			const { result } = renderHook(() => useSnapGuides(mockElements));

			const guides: SnapGuideInfo[] = [
				{ type: "horizontal", position: 25, start: 0, end: 200 },
			];

			act(() => {
				result.current.updateSnapGuidesToState(true, guides, "block1");
			});

			expect(result.current.snapGuides).toEqual({
				visible: true,
				guides,
				relativeToBlockId: "block1",
				blockX: 100,
				blockY: 100,
				blockWidth: 200,
				blockHeight: 150,
			});
		});
	});

	describe("edge cases and error handling", () => {
		it("should handle empty elements array", () => {
			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 15,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: [],
			};

			const result = calculateSnapGuides(params);

			expect(result).toEqual({
				snapX: null,
				snapY: null,
				guides: [],
			});
		});

		it("should handle elements with zero dimensions", () => {
			const zeroElement: Element = {
				id: "zero",
				type: "text",
				x: 50,
				y: 50,
				width: 0,
				height: 0,
				currentPageId: "page1",
				content: "",
			} as Element;

			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 49,
				newY: 49,
				elementWidth: 50,
				elementHeight: 20,
				elements: [mockElements[0], zeroElement],
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			// Should handle zero-dimension elements without crashing
			expect(result).toBeDefined();
		});

		it("should handle negative coordinates", () => {
			const negativeElement: Element = {
				id: "negative",
				type: "text",
				x: -10,
				y: -5,
				width: 20,
				height: 15,
				currentPageId: "page1",
				content: "",
			} as Element;

			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: -11,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: [mockElements[0], negativeElement],
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			expect(result.snapX).toBe(-10); // Should snap to negative coordinate
		});

		it("should handle very large coordinates", () => {
			const largeElement: Element = {
				id: "large",
				type: "text",
				x: 10000,
				y: 10000,
				width: 100,
				height: 50,
				currentPageId: "page1",
				content: "",
			} as Element;

			const params: SnapCalculationParams = {
				elementId: "element1",
				newX: 9999,
				newY: 15,
				elementWidth: 50,
				elementHeight: 20,
				elements: [mockElements[0], largeElement],
				snapThreshold: 2,
			};

			const result = calculateSnapGuides(params);

			expect(result.snapX).toBe(10000); // Should handle large coordinates
		});
	});
});
