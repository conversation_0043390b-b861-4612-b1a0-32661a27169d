import type { Editor } from "@tiptap/react";
import { <PERSON>, <PERSON>, Highlighter, Redo2, Save, Undo2 } from "lucide-react";
import {
	type Dispatch,
	type SetStateAction,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from "react";
import { ExportPreview } from "@/components/ExportPreview";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Toggle } from "@/components/ui/toggle";
import { useToast } from "@/hooks/use-toast";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import {
	deleteFrontifyFlag,
	type PageFormat,
	saveDocumentToAPI,
	setFrontifyFlag,
	type TestDataRecord,
} from "@/utils/apiService";
import { exportToZip, importFromZip } from "@/utils/exportUtils";

interface EditorState {
	pages: PageSettings[];
	elements: Element[];
}

interface TopBarProps {
	documentId: string;
	pages: PageSettings[];
	elements: Element[];
	onUndo: () => void;
	onRedo: () => void;
	canUndo: boolean;
	canRedo: boolean;
	highlightVariables: boolean;
	setHighlightVariables: (highlight: boolean) => void;
	showPageNumbers: boolean;
	deletedPageNumberIds: Set<string>;
	testData: TestDataRecord[];
	selectedTestDataIndex: number;
	setSelectedTestDataIndex: Dispatch<SetStateAction<number>>;
	setEditorState: (state: EditorState, addToHistory?: boolean) => void;
	isEditorLocked: boolean;
	setIsEditorLocked: Dispatch<SetStateAction<boolean>>;
	isReleased: boolean;
	setIsReleased: Dispatch<SetStateAction<boolean>>;
	role?: string;
	allowRelease?: boolean;
	apiFormats?: PageFormat[];
	activeEditor?: Editor | null;
	isTextEditorFocused?: boolean;
}

export function TopBar({
	documentId,
	pages,
	elements,
	onUndo,
	onRedo,
	canUndo,
	canRedo,
	highlightVariables,
	setHighlightVariables,
	showPageNumbers,
	deletedPageNumberIds,
	testData,
	selectedTestDataIndex,
	setSelectedTestDataIndex,
	setEditorState,
	setIsEditorLocked,
	isReleased,
	setIsReleased,
	role,
	allowRelease,
	apiFormats,
	activeEditor,
	isTextEditorFocused,
}: TopBarProps) {
	const fileInputRef = useRef<HTMLInputElement>(null);
	const { toast } = useToast();
	const [showExportPreview, setShowExportPreview] = useState(false);
	const [backId, setBackId] = useState<string | null>(null);

	const { modKeyLabel, shiftLabel } = useMemo(() => {
		let isMac = false;
		if (typeof navigator !== "undefined") {
			isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
		}
		return {
			modKeyLabel: isMac ? "⌘" : "Strg",
			shiftLabel: isMac ? "⇧" : "Shift",
		};
	}, []);

	useEffect(() => {
		const params = new URLSearchParams(window.location.search);
		setBackId(params.get("id"));
	}, []);

	const handleSaveToAPI = async () => {
		try {
			if (!documentId) {
				console.error("Document ID is required for API save");
				toast({
					title: "Fehler",
					description: "Dokument ID ist erforderlich für das Speichern",
					variant: "destructive",
				});
				return;
			}

			const result = await saveDocumentToAPI(
				pages,
				elements,
				documentId,
				showPageNumbers,
				deletedPageNumberIds,
				apiFormats,
			);
			console.log("Document saved to API:", result);
			toast({
				title: "Erfolgreich",
				description: "Dokument erfolgreich gespeichert!",
				variant: "default",
			});

			// Update the URL to set version to 'latest' after successful save
			const url = new URL(window.location.href);
			url.searchParams.set("version", "latest");
			window.history.replaceState({}, "", url.toString());
		} catch (error: unknown) {
			console.error("Failed to save document to API:", error);
			const errorMsg =
				typeof error === "string"
					? error
					: error instanceof Error
						? error.message
						: "";

			if (
				errorMsg.includes("409") ||
				errorMsg.toLowerCase().includes("already released") ||
				errorMsg.toLowerCase().includes("bereits freigegeben")
			) {
				toast({
					title: "Fehler",
					description:
						"Speichern nicht möglich, es besteht bereits eine Freigabe für Frontify.",
					variant: "destructive",
				});
				return;
			}

			toast({
				title: "Fehler",
				description: "Fehler beim Speichern des Dokuments!",
				variant: "destructive",
			});
		}
	};

	const handleExport = async () => {
		try {
			await exportToZip(pages, elements, `dokument-${documentId}`, apiFormats);
			toast({
				title: "Erfolgreich",
				description: "Dokument erfolgreich exportiert!",
				variant: "default",
			});
		} catch (error) {
			console.error("Failed to export document:", error);
			toast({
				title: "Fehler",
				description: "Fehler beim Exportieren des Dokuments!",
				variant: "destructive",
			});
		}
	};

	const handleShowPreview = useCallback(() => {
		setShowExportPreview(true);
	}, []);

	const handleImportClick = () => {
		if (fileInputRef.current) {
			fileInputRef.current.click();
		}
	};

	const handleImportFile = async (
		event: React.ChangeEvent<HTMLInputElement>,
	) => {
		try {
			const files = event.target.files;
			if (!files || files.length === 0) return;

			const file = files[0];
			console.log(
				"Import file selected:",
				file.name,
				"size:",
				file.size,
				"type:",
				file.type,
			);

			// More thorough validation of the file
			if (!file.name.endsWith(".zip")) {
				console.error("File is not a ZIP file based on name:", file.name);
				toast({
					title: "Fehler",
					description: "Bitte wählen Sie eine gültige ZIP-Datei aus!",
					variant: "destructive",
				});
				return;
			}

			// Additional checking of file type if browser provides it
			if (
				file.type &&
				file.type !== "application/zip" &&
				file.type !== "application/x-zip-compressed"
			) {
				console.warn(
					"File MIME type doesn't match expected ZIP type:",
					file.type,
				);
				// We'll continue anyway but log a warning
			}

			console.log("Attempting to import ZIP file...");
			let importedData: { pages: PageSettings[]; elements: Element[] };

			try {
				// Try the primary import method first
				importedData = await importFromZip(file);
				console.log("Primary import method successful");
			} catch (primaryError) {
				console.error("Primary import method failed:", primaryError);

				throw primaryError;
			}

			console.log("Import successful, received data:", {
				pageCount: importedData.pages?.length || 0,
				elementCount: importedData.elements?.length || 0,
			});

			// Debug log the data more extensively before setting state
			console.log(
				"Pages to set:",
				JSON.stringify(
					importedData.pages.map((p) => ({
						id: p.id,
						format: p.format,
						orientation: p.orientation,
					})),
				),
			);
			console.log(
				"Elements to set:",
				JSON.stringify(
					importedData.elements.map((e) => ({
						id: e.id,
						type: e.type,
						currentPageId: e.currentPageId,
					})),
				),
			);

			// Create deep copies to ensure state updates
			const newPages = JSON.parse(JSON.stringify(importedData.pages));
			const newElements = JSON.parse(JSON.stringify(importedData.elements));

			console.log("Setting new state with deep copies");
			// Update both pages and elements together
			setEditorState({ pages: newPages, elements: newElements }, false);

			// Reset the file input
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}

			toast({
				title: "Erfolgreich",
				description: "Dokument erfolgreich importiert!",
				variant: "default",
			});
		} catch (error) {
			console.error("Failed to import document:", error);

			// Provide more specific error messages
			let errorMessage = "Fehler beim Importieren des Dokuments!";
			if (error instanceof Error) {
				errorMessage += ` ${error.message}`;
				console.error("Import error details:", error.message);
			}

			toast({
				title: "Fehler",
				description: errorMessage,
				variant: "destructive",
			});
		}
	};

	const handleToggleRelease = async () => {
		if (!documentId) {
			toast({
				title: "Fehler",
				description: "Dokument ID ist für diese Aktion erforderlich.",
				variant: "destructive",
			});
			return;
		}

		try {
			if (isReleased) {
				// Widerrufen
				await deleteFrontifyFlag(documentId);
				setIsReleased(false);
				setIsEditorLocked(false);
				toast({
					title: "Frontify Freigabe widerrufen",
					description:
						"Die Freigabe wurde widerrufen. Das Dokument kann wieder bearbeitet werden.",
					variant: "default",
				});
			} else {
				// Freigeben
				// Save the document before releasing
				await handleSaveToAPI();
				await setFrontifyFlag(documentId);
				setIsReleased(true);
				setIsEditorLocked(true);
				toast({
					title: "Frontify Freigabe Prozess",
					description: "Das Dokument wurde freigegeben und ist nun gesperrt.",
					variant: "default",
				});
			}
			// Here you might want to refresh version history if the API call modifies it
		} catch (error) {
			console.error("Failed to toggle release status:", error);
			toast({
				title: "Fehler",
				description:
					"Status der Frontify Freigabe konnte nicht geändert werden.",
				variant: "destructive",
			});
		}
	};

	// Keyboard shortcut: open preview with Mod+P
	useEffect(() => {
		const keyListener = (e: KeyboardEvent) => {
			if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === "p") {
				e.preventDefault();
				handleShowPreview();
			}
		};
		window.addEventListener("keydown", keyListener);
		return () => window.removeEventListener("keydown", keyListener);
	}, [handleShowPreview]);

	return (
		<header className="flex items-center justify-between bg-white border-b px-4 h-24 top-bar">
			{/* Back Button */}
			<div className="flex items-center min-w-[220px]">
				{backId && (
					<a
						href={`/freigabe/${backId}`}
						className="border border-gray-300 rounded px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
						style={{ textDecoration: "none" }}
					>
						← Zur Projektübersicht
					</a>
				)}
			</div>

			{/* Centered content (empty for now) */}
			<div className="flex-1" />

			{/* Right side: Other buttons above, Undo/Redo below */}
			<div className="flex flex-col items-end gap-1">
				<div className="flex items-center gap-2">
					{testData.length > 0 && (
						<div className="flex items-center gap-2">
							<Database className="h-4 w-4 text-gray-500" />
							<Select
								value={
									selectedTestDataIndex > -1
										? selectedTestDataIndex.toString()
										: "-1"
								}
								onValueChange={(value) => {
									const newIndex = parseInt(value);
									setSelectedTestDataIndex(newIndex);
								}}
							>
								<SelectTrigger className="w-48">
									<SelectValue placeholder="Testdaten auswählen" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="-1">Keine Testdaten</SelectItem>
									{testData.map((data, index) => (
										<SelectItem
											key={JSON.stringify(data)}
											value={(index + 1).toString()}
										>
											Datensatz {index + 1}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					)}
					<Toggle
						variant="outline"
						size="sm"
						pressed={highlightVariables}
						onPressedChange={setHighlightVariables}
						className="flex gap-1 items-center px-2"
						aria-label="Variablen Hervorheben"
					>
						<Highlighter className="w-4 h-4" />
						<span className="text-sm">Variablen Hervorheben</span>
					</Toggle>

					{/* Hidden file input for import */}
					<input
						type="file"
						ref={fileInputRef}
						onChange={handleImportFile}
						style={{ display: "none" }}
						accept=".zip"
					/>
					{!isReleased && (
						<Button
							variant="outline"
							className="flex items-center justify-center gap-2"
							onClick={handleSaveToAPI}
						>
							<Save className="h-4 w-4" />
							Speichern
						</Button>
					)}
					{role === "customer" && allowRelease && (
						<Button
							variant={isReleased ? "destructive" : "default"}
							className="flex items-center justify-center gap-2"
							onClick={handleToggleRelease}
						>
							{isReleased
								? "Frontify Freigabe widerrufen"
								: "Frontify Freigabe Prozess"}
						</Button>
					)}
					<Button
						variant="outline"
						className="flex items-center justify-center gap-2"
						onClick={handleShowPreview}
						title={`Vorschau (${modKeyLabel} + P)`}
					>
						<Eye className="h-4 w-4" />
						Vorschau
					</Button>
				</div>
				<div className="flex items-center space-x-2 mt-1">
					{!isReleased && (
						<>
							<button
								className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${
									(
										isTextEditorFocused && activeEditor
											? !activeEditor.can().undo()
											: !canUndo
									)
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
								onMouseDown={(e) => {
									if (isTextEditorFocused && activeEditor) {
										e.preventDefault();
										activeEditor.commands.undo();
									} else {
										onUndo();
									}
								}}
								disabled={
									isTextEditorFocused && activeEditor
										? !activeEditor.can().undo()
										: !canUndo
								}
								title={`Rückgängig (${modKeyLabel} + Z)`}
								type="button"
							>
								<Undo2 className="h-4 w-4" />
							</button>
							<button
								className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${
									(
										isTextEditorFocused && activeEditor
											? !activeEditor.can().redo()
											: !canRedo
									)
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
								onMouseDown={(e) => {
									if (isTextEditorFocused && activeEditor) {
										e.preventDefault();
										activeEditor.commands.redo();
									} else {
										onRedo();
									}
								}}
								disabled={
									isTextEditorFocused && activeEditor
										? !activeEditor.can().redo()
										: !canRedo
								}
								title={`Wiederherstellen (${shiftLabel} + ${modKeyLabel} + Z / ${modKeyLabel} + Y)`}
								type="button"
							>
								<Redo2 className="h-4 w-4" />
							</button>
						</>
					)}
				</div>
			</div>

			{/* Export Preview Modal */}
			{showExportPreview && (
				<ExportPreview
					pages={pages}
					elements={elements}
					isOpen={showExportPreview}
					onClose={() => setShowExportPreview(false)}
					onExport={handleExport}
					onImport={handleImportClick}
					role={role}
					documentId={documentId}
					apiFormats={apiFormats}
					testData={testData}
					selectedTestDataIndex={selectedTestDataIndex}
					highlightVariables={highlightVariables}
				/>
			)}
		</header>
	);
}
