# Phase 1: Standalone editor (client side)

## 1. Allgemein

### 1.2 Leere Seiten erstellen

Der Editor soll die Möglichkeit bieten, leere Seiten aus vordefinierten Formaten  hinzuzufügen.


\> A4; Postkarte; -> leer
Übergabe an die Hochzeit erfolgt als HTML Code

Abnahme

- Es können neue Seiten zu einem existierenden Dokument hinzugefügt werden.
- Es kann ein Dokument von vorn (ohne Import) erstellt werden.
- Es können unterschiedliche Formate (Hor./ Vert.) (A4/ A5) im selben Dokument hinzugefügt werden.

# 1.3 PDF Beilagen einfügen

In den neu erstellen Daten sollen auch sog. Beileger (AGB, Stromkennzeichnung) eingefügt werden können, diese 

# a) sollen mit gedruckt werden

# b) sollen nicht mit gedruckt werden

# Bedarf nur für eingeladene PDF 

# Abnahme

# Auswahl ob Druck oder nicht

PDF erscheint in der Ansicht und im Export (PDF)

Formate stimmen... 

### 1.3 Rückgängig/ Vorwärts-Funktion

Der Editor soll eine Rückgängig- und Vorwärts-Funktion bieten, mit einer unbegrenzten Arbeitsschritte (jeder Klick/jede einzelne Eingabe...)

Abnahme:

- Änderungen können alle rückgängig gemacht und wiederhergestellt werden. - bis zum Speicher-Moment
- Die Funktion reagiert zuverlässig ohne Datenverlust.

### 1.4 Seitenzahlen

Es können Seitenzahlen ein und ausgeschaltet werden. Seitenzahlen können für alle Seiten verschoben werden. (Keine Automation - es findet eine manuelle Eingabe statt)

Abnahme:

- auslassen von Seitenzahlen (auf Deckblättern o.ä.)
- verschieben von Seitenzahlen falls mehrere Formate/ Ausrichtungen im selben Dokument existieren
- keine Automation > jede Seite hält eine Position vor, die editiert oder entfernt werden kann
- Seitenzahlen auf eingefügten PDF werden nicht gesetzt

### 1.5 PDF Export - FLORIAN HANSMANN

Das Dokument kann mit Variablen als PDF exportiert werden.

Abnahme:

- Nach einem Export als PDF sieht die Datei identisch zu einem direkten Indesign PDF Export aus.
- Name besteht aus: JOB-ID_DATUM
- Es werden nur die Basis-Seiten exportiert (entspricht dem Druckbild)
- Aus den am Projekt hochgeladenen Testdatensätzen kann ein User-Profil angeklickt werden, welches dann der Personalisierung dient. 
- Wenn keine Perso-Auswahl, dann bleiben die Variablen (Haben wir eh & bleibt auch so >> in der ersten Zeile) in den Testdaten

### 1.6 Farbpalette

Es gibt eine Farbpalette die alle dezidierten Farben (aus dem Import) enthält, außerdem können Farben über einen color picker hinzugefügt werden. Style Editor in Phase 2.

Farbpalette wird immer aus dem HTML-Code übernommen, egal ob im Import aus Indesign oder später beim Kopieren aus dem Quellcode 

Es werden immer nur 100% Werte - keine Transparenzen. - bei abweichenden Farbwerten (z.B. 20 % von dem Vollton-Wert, wir der daraus resultierende RGB-Wert (neu) gesetzt. 

Abnahme:   
User muss bestätigen, dass der neue/weitere Farben über einen color picker hinzufügen möchte (RGB + HEX)

heue Farbe steht in dem Projekt mit in einer "Schnellauswahl" 

Farben werden für Texte und verschieden Flächen hinterlegt und werden gespeichert

## 2. Elemente

Es gibt folgende Arten von Elementen: Texte, Flächen, Bilder, Tabellen, Bausteine, Schutzzonen, Adresse(n).

### 2.1 Elemente hinzufügen

Es können neue Elemente zum Dokument hinzugefügt werden.  
Texte, Flächen (Rechteck, Kreis, abgerundete Ecken + Entscheidung für jede Ecke (Radius) (BSP Response-Karte), , Bilder, Tabellen, Bausteine, Adressen, Schutzzonen

- alle Elemente stehen als Auswahl zur Verfügung und können genutzt werden

# 2.2 Adresse 

Adresse mit Strich (Postkarte)

Adresse ohne Strich (Brief) / 

Paul benötigt einmal die Vorgabe Brief + Poka als leere HTML mit BSP Adressblock 

Adresse darf nur bestimmte Schriften und Größen beinhalten (Vorgabe muss TGD liefern)

Absenderadresse kann von dem Anwender frei bestimmt werden

Adressen haben immer eine fixe Position, die nicht durch den User verändert werden kann

### 2.2 Elemente verschieben/ skalieren /drehen (+Florian)

Existierende Elemente können auf einer Seite frei verschoben werden (drag'n'drop), außerdem  können Elemente frei in ihrer Größe verändert und positioniert werden, hierbei können auch cm-Angaben genutzt werden, Elemente können gedreht werden (frei + Grad-Zahl) >> Prüfung durch Florian, sonst ggf. nur für Text

\> Florian prüft vorab, ob eine cm-Angabe mit Seitenrand-Berücksichtigung (Paul plant HTML Angabe in %) 

\> Abhängigkeit der Position immer nach oben im Abstand orientiert (Y-Pos. immer abhängig zum Vorgänger auf der linken Seite) > Gruppen müssen gebildet werden (damit wir z.B. auch eine Marginalspalte abbilden können ), um Nachfolger bestimmen zu können

> immer Abhängig - kann aber abgewählt werden, um eine absolute Positionierung zu erhalten (BSP. Nutzen für Footer)

### 2.3 Element kopieren/ ausschneiden/ löschen

Ein Element kann kopiert werden (via Strg+C oder Kontextmenü), es kann dann an der aktuellen Position des Mauszeigers wieder eingefügt werden (via Strg+V oder Kontextmenü).

Elemente können nur eingefügt werden falls der Mauszeiger auf ein validen Position ist (innerhalb einer Seite). Falls ein Element zu groß ist um an der aktuellen Stelle eingefügt zu werden wird es runterskaliert bis es auf die Seite passt.

(Bei Ausschneiden bleibt die Leerfläche stehen)

(Man kann nur Bausteine -> mehrere Elemente gemeinsam verschieben/ausschneiden...)  
  
- gilt immer nur für einzelne Elemente - keine Gruppierung (wg. Aufwand)


### 2.4 Tabellen

Der Editor soll die Möglichkeit bieten, Tabellen mit anpassbaren Rahmen und Abständen zu erstellen. Eine Zelle kann sich auch über mehrere Spalten erstrecken (colspan). + rowspan

Abnahme:

- Tabellen mit unterschiedlichen Rahmenstilen erstellbar (auch Rahmenlos + verschiedene Strichstärken innerhalb der selben Tabelle). (gestrichelt ist nicht relevant)
- Abstände innerhalb der Zellen können angepasst werden.
- Hochgestellte Zeichen nur als Unicode (damit die Zelle sich in der Höhe nicht verändert)
- (aus Indesign-Übernahmen ggf. Verläufe möglich - diese sind nicht editierbar)
- Änderungen der Tabelle, wie Löschungen der Spalten / Neuanlage ohne Indesign-Vorlage können nur mit vollflächigen Farben genutzt werden
- Hintergründe (Farben) für Zellen/Zeile(n)/Spalte(n) ermöglichen > (mehrere) Zellen können ausgewählt werden > diese Mehrfach-Auswahl kann auch über ein extra Editor-Feld (?) erfolgen, einzelne Zellen sind direkt befüllbar/Rahmen

### 2.5 Schutzzonen

Es können Schutzzonen definiert werden

Abnahme:

Schutzzonen werden als Empfehlung eingeblendet, aber nicht aktiv gesperrt

Paul benötigt eine HTML für jedes Format mit einer hellgrauen randlosen Box  (Eindruckdatei mit Schnittmarken) zur Orientierung - keine technischen Abhängigkeiten

Schutzzonen werden im PDF-Export nicht berücksichtigt /unsichtbar

Schutzzonen können ausgeblendet werden (Checkbox?)

### 2.6 Bausteine

In Bausteinen können mehrere Elemente zusammengefasst werden, dies können dann gemeinsam im Dokument verschoben werden

Abnahme:

### 2.7 Elemente in Vordergrund/ Hintergrund stellen

Alle Elemente des Dokuments können vor/ hinter anderen Elementen positioniert werden (Überlagerung).

Abnahme:

- Bilder lassen sich hinter Text/Bildern/Flächen positionieren. > Hinweis: in der Anwendung sollte damit gehaushaltet werden, da es hier immer um Speicherungen geht...  Bilder sollten bestmöglich zusammengefasst werden (es werden keine Transparenzen 
- Funktioniert auch mit mehreren Ebenen (man kann ein Element was bereits ein anderes Element verdeckt mit einem weiteren Element überlagern )
- Ebenenauswahl VORDERGRUND/Hintergrund und mit Plus/Minus einzelne Ebenen springen

## 3. Textformatierung

### 3.1 Bullets & Aufzählungen

Der Editor soll die Möglichkeit bieten, Listen sowohl numerisch (1., 2., 3.) als auch als Bulletpoints zu erstellen.

Abnahme:

- Erstellung von nummerierten und bulletpoint Listen möglich.
- Falls nach einer Liste eine neue Zeile eingefügt wird wird in der neuen Zeile ein neuer Bulletpoint/ Zahl Vorangestellt.
- Enter oder Backspace in einer leeren Zeile mit aktivierter Liste deaktiviert die aktuelle Liste wieder.
- bei mehrzeiligen Texten beginnt der eingerückte Text untereinander (ebenfalls eingerückt)

### 3.2 Einrückungen von Text und Bullets

Der Editor soll Textabschnitte sowie Listenpunkte durch Einrücken formatieren können.

Abnahme:

- Einrückung von Texten (auch mehrzeilig bündig) möglich.
- Einrückung einzelner oder mehrerer Listenelemente funktioniert korrekt.
- Mehrstufige Einrückung bleibt nach dem Speichern erhalten.

### 3.3 Textausrichtungen

Der Editor muss verschiedene Textausrichtungen bieten: Blocksatz, linksbündig, rechtsbündig und zentriert.

Abnahme:

- Alle Textausrichtungen (Blocksatz, linksbündig, rechtsbündig, zentriert) sind verfügbar und umsetzbar.
- Änderungen der Ausrichtung werden korrekt übernommen und gespeichert.

### 3.4 Textstile

Es soll möglich sein, Texte zu unterstreichen, fett, kursiv oder durchgestrichen darzustellen.

Abnahme:

- Formatierungen (fett, kursiv, unterstrichen, durchgestrichen) funktionieren einzeln und kombiniert.
- Änderungen bleiben nach dem Speichern bestehen.

### 3.5 Hochstellen von Zeichen

Textelemente sollen hochgestellt/ niedriggestellt formatiert werden können, z. B. für chemische Formeln wie CO₂.

Abnahme:

- Hochgestellte Zeichen werden korrekt dargestellt und es gibt keine Veränderungen der Zeilenhöhe

### 3.6 Zeichen- und Zeilenabstand

Es soll möglich sein, den Zeichen- und Zeilenabstand zu ändern.

Abnahme:

- Zeichenabstände lassen sich anpassen und werden korrekt dargestellt.
- Zeilenabstände lassen sich anpassen und werden korrekt dargestellt.

### 3.7 Hinterlegen von URLs (Links)

Der Editor soll die Anpassung und Bearbeitung von URLs ermöglichen. URLs sollen speziell formatiert werden (farblich hinterlegt und unterstrichen). (Formatvorlage)

Abnahme:

- URLs können angepasst und editiert werden.
- URLs werden speziell hervorgehoben
- Können aber manuell verändert werden 

### 3.8 Trennungen mit Steuerzeichen

Es soll möglich sein, Trennungen mithilfe von Steuerzeichen wie ­ (weiche Trennung) und   (geschütztes Leerzeichen) zu steuern.

Abnahme:

- Steuerzeichen wie &nbsp; und &shy;  werden korrekt erkannt und verarbeitet.

### 3.9 Fonts

Die die Schriftgröße und Art soll angepasst werden können. Erstmal nur rudimentär (Auswahl aus allen importierten Schriftarten), Style Editor/ Beschränkungen in Phase 2 (H1; Fließtext...).

# Phase 2: Integration in Transfersafe

### Dokument erstellen

Es können neue Dokumente in Transfersafe angelegt werden.

### Dokument bearbeiten

Existierende Dokumente können in Transfersafe bearbeitet werden.

### Dokument duplizieren

Existierende Dokumente können  in Transfersafe dupliziert werden

### Speichern des Dokuments

Beim Drücken des “Speichern”-Buttons (bzw. Strg+S) werden alle Änderungen übernommen und gesichert.

Abnahme:

- Änderungen werden nach Betätigung des “Speichern”-Buttons korrekt gespeichert.
- Inhalte sind nach dem Schließen und erneuten Öffnen des Dokuments vollständig.
- Als Zusatz wird eine Abfrage eingebaut, wenn der Tap geschlossen wird, dass eine Abfrage kommt, ob gespeichert werden soll "Diese Seite wirklich verlassen"?

### Variablen 

  
>> werden aus CSV am Projekt ausgelesen  
Variablen werden automatisch erkannt. Transfersafe  kann die Variablen automatisch bespielen.

### Dateiversionierung  (optional)

Es gibt eine Historie und das Dokument kann auf einen beliebigen Stand zurückgesetzt werden.

Abnahme:

- Bei jedem Speichern wird eine neue "Version" angelegt
- Das Dokument kann auf eine ältere Version zurückgesetzt werden.
- Nach zurücksetzen auf eine ältere Version kann trotzdem wieder auf eine neuere Version umgestellt werden (keine Version geht verloren).
- Ältere Versionen können auch nur kurz "angeschaut" werden.

### Hinzufügen von Bildern

Der Editor soll Zugriff auf den Media-Server (tgd) erhalten, sodass alle Bilder im Menü angezeigt werden.

Abnahme:

- auf dem Mediaserver hinterlegte Bilder können in ein Dokument eingefügt werden
- beim Generieren wird immer die Bildverfügbarkeit geprüft
- Der Kunde hat nur Auswahl-Rechte (kein Upload/Download) von Bildern
- nur TGD kann Bilder hinzufügen  (Ordner-Anlage ist möglich)
- Zugriff erfolgt über den Editor - (ggf. Textsuche) ("Logo" /Thumbnail / Tags / Auswahl ALLE / Sortierung nach Name / Datum (auf-absteigend)

### Element Templates

Ein Element eines Dokumentes kann als Template abgespeichert werden, dieses kann dann Dokumentenübergreifend eingesetzt werden. Ein "Element" kann hierbei natürlich auch ein "Baustein" sein der mehrere Elemente enthält.

### Fontmanger (keine Prio)

Bei einem Import von Indesign wird geprüft, ob alle Schriften vorhanden sind - falls nicht, gibt es eine Fehlermeldung und die Schrift muss durch Florian nachgeladen werden.   
Beim Öffnen der Datei muss eine Fehlermeldung kommen, bis die Schrift vorhanden ist. 

Es gibt eine Möglichkeit Fonts hochzuladen und zu verwalten.

## Style Editor

Es kann eine Stylevorgabe für neu erstellte Dokumente definiert werden.

Beschränkung von Schriftgrößen und Schriftart nach art h1 - h5, p, small leichte Abweichungen möglich (wie groß?)

Vorauswahl von Farben.