import { createContext, useContext } from "react";

export type ToastType = {
	id: string;
	title?: string;
	description?: string;
	variant?: "default" | "destructive";
};

export type ToastContextType = {
	toast: (props: {
		title?: string;
		description?: string;
		variant?: "default" | "destructive";
	}) => void;
	toasts: ToastType[];
};

export const ToastContext = createContext<ToastContextType>({
	toast: () => {},
	toasts: [],
});

export const useToast = () => {
	const context = useContext(ToastContext);

	if (context === undefined) {
		throw new Error("useToast must be used within a ToastProvider");
	}

	return context;
};
