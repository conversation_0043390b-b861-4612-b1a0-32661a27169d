import { describe, expect, it } from "vitest";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import type { Element } from "../../types/element";
import type { PageSettings } from "../../types/page";
import * as validationService from "../validationService";

describe("validationService", () => {
	describe("validateElement", () => {
		it("should validate a valid element", () => {
			// Arrange
			const element = ElementFactory.createTextElement();

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should detect missing required properties", () => {
			// Arrange
			const element = {
				...ElementFactory.createTextElement(),
				id: "",
				type: undefined,
			} as unknown as Element;

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Element must have an ID");
			expect(result.errors).toContain("Element must have a type");
		});

		it("should detect invalid position values", () => {
			// Arrange
			const element = {
				...ElementFactory.createTextElement(),
				x: NaN,
				y: "invalid" as unknown as number,
			};

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				"Element x position must be a valid number",
			);
			expect(result.errors).toContain(
				"Element y position must be a valid number",
			);
		});

		it("should detect invalid size values", () => {
			// Arrange
			const element = {
				...ElementFactory.createTextElement(),
				width: -10,
				height: 0,
			};

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				"Element width must be a positive number",
			);
			expect(result.errors).toContain(
				"Element height must be a positive number",
			);
		});

		it("should validate element position within page bounds", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210, // 21cm
				height: 297, // 29.7cm
			});

			const element = {
				...ElementFactory.createTextElement(),
				x: -10,
				y: -5,
			};

			// Act
			const result = validationService.validateElement(element, page);

			// Assert
			expect(result.isValid).toBe(true); // Position issues are warnings, not errors
			expect(result.warnings).toContain("Element x position is negative");
			expect(result.warnings).toContain("Element y position is negative");
			expect(result.correctedElement).toBeDefined();
			expect(result.correctedElement?.x).toBe(0);
			expect(result.correctedElement?.y).toBe(0);
		});

		it("should validate element does not extend beyond page bounds", () => {
			// Arrange
			const page = PageFactory.createPage({
				width: 210, // 21cm
				height: 297, // 29.7cm
			});

			const element = {
				...ElementFactory.createTextElement(),
				x: 2000,
				y: 2900,
				width: 200,
				height: 100,
			};

			// Act
			const result = validationService.validateElement(element, page);

			// Assert
			expect(result.warnings).toContain("Element extends beyond page width");
			expect(result.warnings).toContain("Element extends beyond page height");
			expect(result.correctedElement).toBeDefined();
			expect(result.correctedElement?.x).toBe(1900); // 2100 - 200
			expect(result.correctedElement?.y).toBe(2870); // 2970 - 100
		});

		it("should validate type-specific properties for text elements", () => {
			// Arrange
			const element = {
				...ElementFactory.createTextElement(),
				content: "",
			};

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.warnings).toContain("Text element should have content");
		});

		it("should validate type-specific properties for image elements", () => {
			// Arrange
			const element = {
				...ElementFactory.createImageElement(),
				src: undefined,
			} as unknown as Element;

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Image element must have a source URL");
		});

		it("should validate type-specific properties for block elements", () => {
			// Arrange
			const element = {
				...ElementFactory.createBlockElement(),
				childElementIds: "not-an-array" as unknown as string[],
			};

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				"Block element childElementIds must be an array",
			);
		});

		it("should validate z-index and rotation values", () => {
			// Arrange
			const element = {
				...ElementFactory.createTextElement(),
				zIndex: NaN,
				rotation: "invalid" as unknown as number,
			};

			// Act
			const result = validationService.validateElement(element);

			// Assert
			expect(result.warnings).toContain(
				"Element z-index must be a valid number",
			);
			expect(result.warnings).toContain(
				"Element rotation must be a valid number",
			);
			expect(result.correctedElement).toBeDefined();
			expect(result.correctedElement?.zIndex).toBe(0);
			expect(result.correctedElement?.rotation).toBe(0);
		});
	});

	describe("validatePage", () => {
		it("should validate a valid page", () => {
			// Arrange
			const page = PageFactory.createPage();

			// Act
			const result = validationService.validatePage(page);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should detect missing required properties", () => {
			// Arrange
			const page = {
				...PageFactory.createPage(),
				id: "",
				format: undefined,
			} as unknown as PageSettings;

			// Act
			const result = validationService.validatePage(page);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Page must have an ID");
			expect(result.errors).toContain("Page must have a format");
		});

		it("should detect invalid dimension values", () => {
			// Arrange
			const page = {
				...PageFactory.createPage(),
				width: -10,
				height: 0,
			};

			// Act
			const result = validationService.validatePage(page);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Page width must be a positive number");
			expect(result.errors).toContain("Page height must be a positive number");
		});

		it("should validate trim values", () => {
			// Arrange
			const page = {
				...PageFactory.createPage(),
				trim_left: -5,
				trim_right: NaN,
				trim_top: "invalid" as unknown as number,
				trim_bottom: -10,
			};

			// Act
			const result = validationService.validatePage(page);

			// Assert
			expect(result.warnings).toContain(
				"Page trim_left must be a non-negative number",
			);
			expect(result.warnings).toContain(
				"Page trim_right must be a non-negative number",
			);
			expect(result.warnings).toContain(
				"Page trim_top must be a non-negative number",
			);
			expect(result.warnings).toContain(
				"Page trim_bottom must be a non-negative number",
			);
			expect(result.correctedPage).toBeDefined();
			expect(result.correctedPage?.trim_left).toBe(0);
			expect(result.correctedPage?.trim_right).toBe(0);
			expect(result.correctedPage?.trim_top).toBe(0);
			expect(result.correctedPage?.trim_bottom).toBe(0);
		});

		it("should validate page number coordinates", () => {
			// Arrange
			const page = {
				...PageFactory.createPage(),
				pagenumber_x: NaN,
				pagenumber_y: "invalid" as unknown as number,
			};

			// Act
			const result = validationService.validatePage(page);

			// Assert
			expect(result.warnings).toContain(
				"Page number X coordinate must be a valid number",
			);
			expect(result.warnings).toContain(
				"Page number Y coordinate must be a valid number",
			);
			expect(result.correctedPage).toBeDefined();
			expect(result.correctedPage?.pagenumber_x).toBeUndefined();
			expect(result.correctedPage?.pagenumber_y).toBeUndefined();
		});
	});

	describe("validateElementRelationships", () => {
		it("should validate valid parent-child relationships", () => {
			// Arrange
			const blockId = "block-1";
			const childId = "child-1";

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: [childId],
				},
				{
					...ElementFactory.createTextElement(),
					id: childId,
					parentId: blockId,
				},
			];

			// Act
			const result = validationService.validateElementRelationships(elements);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should detect non-existent parent references", () => {
			// Arrange
			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "text-1",
					parentId: "non-existent",
				},
			];

			// Act
			const result = validationService.validateElementRelationships(elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				"Element text-1 references non-existent parent non-existent",
			);
		});

		it("should detect parent that is not a block", () => {
			// Arrange
			const parentId = "text-1";
			const childId = "text-2";

			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: parentId },
				{ ...ElementFactory.createTextElement(), id: childId, parentId },
			];

			// Act
			const result = validationService.validateElementRelationships(elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				`Element ${childId} has parent ${parentId} which is not a block`,
			);
		});

		it("should detect inconsistent parent-child relationships", () => {
			// Arrange
			const blockId = "block-1";
			const childId = "child-1";

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: [],
				},
				{
					...ElementFactory.createTextElement(),
					id: childId,
					parentId: blockId,
				},
			];

			// Act
			const result = validationService.validateElementRelationships(elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				`Element ${childId} claims parent ${blockId} but parent doesn't list it as child`,
			);
		});

		it("should detect non-existent child references", () => {
			// Arrange
			const blockId = "block-1";

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: ["non-existent"],
				},
			];

			// Act
			const result = validationService.validateElementRelationships(elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				`Block ${blockId} references non-existent child non-existent`,
			);
		});

		it("should detect inconsistent child-parent relationships", () => {
			// Arrange
			const blockId = "block-1";
			const childId = "child-1";

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: [childId],
				},
				{
					...ElementFactory.createTextElement(),
					id: childId,
					parentId: "different-parent",
				},
			];

			// Act
			const result = validationService.validateElementRelationships(elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain(
				`Block ${blockId} lists ${childId} as child but child doesn't reference it as parent`,
			);
		});
	});

	describe("validatePageElementRelationships", () => {
		it("should validate valid page references", () => {
			// Arrange
			const pageId = "page-1";

			const pages: PageSettings[] = [PageFactory.createPage({ id: pageId })];

			const elements: Element[] = [
				ElementFactory.createTextElement({ currentPageId: pageId }),
			];

			// Act
			const result = validationService.validatePageElementRelationships(
				pages,
				elements,
			);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should detect non-existent page references", () => {
			// Arrange
			const pages: PageSettings[] = [PageFactory.createPage({ id: "page-1" })];

			const elements: Element[] = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "non-existent",
				}),
			];

			// Act
			const result = validationService.validatePageElementRelationships(
				pages,
				elements,
			);

			// Assert
			expect(result.isValid).toBe(false);
			// Check that the error message contains the element ID and page ID
			expect(result.errors[0]).toContain("Element");
			expect(result.errors[0]).toContain("text-1");
			expect(result.errors[0]).toContain("non-existent");
		});
	});

	describe("validateDocument", () => {
		it("should validate a valid document", () => {
			// Arrange
			const pageId = "page-1";

			const pages: PageSettings[] = [PageFactory.createPage({ id: pageId })];

			const elements: Element[] = [
				ElementFactory.createTextElement({ currentPageId: pageId }),
			];

			// Act
			const result = validationService.validateDocument(pages, elements);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it("should collect errors from all validation functions", () => {
			// Arrange
			const pages: PageSettings[] = [
				{
					...PageFactory.createPage(),
					id: "",
					width: -10,
				} as unknown as PageSettings,
			];

			const elements: Element[] = [
				{
					...ElementFactory.createTextElement(),
					id: "",
					height: -5,
					currentPageId: "non-existent",
				} as unknown as Element,
			];

			// Act
			const result = validationService.validateDocument(pages, elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
			expect(result.errors).toContain("Page must have an ID");
			expect(result.errors).toContain("Page width must be a positive number");
			expect(result.errors).toContain("Element must have an ID");
			expect(result.errors).toContain(
				"Element height must be a positive number",
			);

			// Check that at least one error contains the page ID
			const pageReferenceError = result.errors.some(
				(error) => error.includes("non-existent") && error.includes("page"),
			);
			expect(pageReferenceError).toBe(true);
		});
	});

	describe("canDeleteElement", () => {
		it("should allow deleting an element with no dependencies", () => {
			// Arrange
			const elementId = "text-1";

			const elements: Element[] = [
				{ ...ElementFactory.createTextElement(), id: elementId },
			];

			// Act
			const result = validationService.canDeleteElement(elementId, elements);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
			expect(result.warnings).toHaveLength(0);
		});

		it("should warn when deleting a block with children", () => {
			// Arrange
			const blockId = "block-1";
			const childId = "child-1";

			const elements: Element[] = [
				{
					...ElementFactory.createBlockElement(),
					id: blockId,
					childElementIds: [childId],
				},
				{
					...ElementFactory.createTextElement(),
					id: childId,
					parentId: blockId,
				},
			];

			// Act
			const result = validationService.canDeleteElement(blockId, elements);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
			expect(result.warnings).toContain(
				"Deleting element block-1 will also delete 1 child elements",
			);
		});

		it("should detect non-existent element", () => {
			// Arrange
			const elements: Element[] = [ElementFactory.createTextElement()];

			// Act
			const result = validationService.canDeleteElement(
				"non-existent",
				elements,
			);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Element non-existent not found");
		});
	});

	describe("canDeletePage", () => {
		it("should allow deleting a page with no elements", () => {
			// Arrange
			const pageId = "page-1";

			const pages: PageSettings[] = [
				PageFactory.createPage({ id: pageId }),
				PageFactory.createPage({ id: "page-2" }),
			];

			const elements: Element[] = [];

			// Act
			const result = validationService.canDeletePage(pageId, pages, elements);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
			expect(result.warnings).toHaveLength(0);
		});

		it("should warn when deleting a page with elements", () => {
			// Arrange
			const pageId = "page-1";

			const pages: PageSettings[] = [
				PageFactory.createPage({ id: pageId }),
				PageFactory.createPage({ id: "page-2" }),
			];

			const elements: Element[] = [
				ElementFactory.createTextElement({ currentPageId: pageId }),
			];

			// Act
			const result = validationService.canDeletePage(pageId, pages, elements);

			// Assert
			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
			expect(result.warnings).toContain(
				"Deleting page page-1 will also delete 1 elements",
			);
		});

		it("should prevent deleting the last page", () => {
			// Arrange
			const pageId = "page-1";

			const pages: PageSettings[] = [PageFactory.createPage({ id: pageId })];

			const elements: Element[] = [];

			// Act
			const result = validationService.canDeletePage(pageId, pages, elements);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Cannot delete the last remaining page");
		});

		it("should detect non-existent page", () => {
			// Arrange
			const pages: PageSettings[] = [PageFactory.createPage({ id: "page-1" })];

			const elements: Element[] = [];

			// Act
			const result = validationService.canDeletePage(
				"non-existent",
				pages,
				elements,
			);

			// Assert
			expect(result.isValid).toBe(false);
			expect(result.errors).toContain("Page non-existent not found");
		});
	});
});
