import { Alert<PERSON>ir<PERSON>, Loader2 } from "lucide-react";

interface LoadingStatesProps {
	isLoading: boolean;
	loadError: string | null;
	onRetry?: () => void;
}

export function LoadingStates({
	isLoading,
	loadError,
	onRetry,
}: LoadingStatesProps) {
	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-screen bg-gray-50">
				<div className="text-center">
					<Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
					<p className="text-gray-600">Loading document...</p>
				</div>
			</div>
		);
	}

	if (loadError) {
		return (
			<div className="flex items-center justify-center min-h-screen bg-gray-50">
				<div className="text-center max-w-md mx-auto p-6">
					<AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
					<h2 className="text-xl font-semibold text-gray-900 mb-2">
						Failed to Load Document
					</h2>
					<p className="text-gray-600 mb-4">{loadError}</p>
					{onRetry && (
						<button
							type="button"
							onClick={onRetry}
							className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
						>
							Try Again
						</button>
					)}
				</div>
			</div>
		);
	}

	return null;
}
