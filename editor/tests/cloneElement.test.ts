import { expect, test } from "vitest";
import { cloneElement } from "../src/services/elementService";
import type { Element } from "../src/types/element";

// Regression test: ensure cloneElement performs a deep clone of nested
// structures (e.g. tableProperties). Mutating the clone must leave the
// source element unchanged.

test("cloneElement deep-clones tableProperties", () => {
	const originalElement: Element = {
		id: "orig-table",
		currentPageId: "page-1",
		type: "table",
		x: 0,
		y: 0,
		width: 100,
		height: 100,
		rotation: 0,
		zIndex: 1,
		tableProperties: {
			rows: 1,
			columns: 1,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				[
					{
						content: "A",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
			],
		},
	};

	// Act: clone and mutate the cloned element.
	const cloned = cloneElement(originalElement, 10, 10);
	if (!cloned.tableProperties) {
		throw new Error("Cloned element has no tableProperties");
	}
	cloned.tableProperties.cells[0][0].content = "B";

	// Assert: original element must not change.
	expect(originalElement.tableProperties?.cells[0][0].content).toBe("A");
});
