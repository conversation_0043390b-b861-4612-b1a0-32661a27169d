import { useState } from "react";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

export interface DocumentState {
	documentId: string;
	currentVersion: string;
	role: string | undefined;
	isLoading: boolean;
	loadError: string | null;
	isEditorLocked: boolean;
	isReleased: boolean;
	allowRelease: boolean;
	rawLoadedData: { pages: PageSettings[]; elements: Element[] } | null;
}

export function useDocumentState() {
	const [documentId, setDocumentId] = useState<string>("");
	const [currentVersion, setCurrentVersion] = useState<string>("");
	const [role, setRole] = useState<string | undefined>(undefined);
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [loadError, setLoadError] = useState<string | null>(null);
	const [isEditorLocked, setIsEditorLocked] = useState<boolean>(false);
	const [isReleased, setIsReleased] = useState<boolean>(false);
	const [allowRelease, setAllowRelease] = useState<boolean>(false);
	const [rawLoadedData, setRawLoadedData] = useState<{
		pages: PageSettings[];
		elements: Element[];
	} | null>(null);

	return {
		// State values
		documentId,
		currentVersion,
		role,
		isLoading,
		loadError,
		isEditorLocked,
		isReleased,
		allowRelease,
		rawLoadedData,

		// Setters
		setDocumentId,
		setCurrentVersion,
		setRole,
		setIsLoading,
		setLoadError,
		setIsEditorLocked,
		setIsReleased,
		setAllowRelease,
		setRawLoadedData,
	};
}
