import { expect } from "vitest";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import type { EditorState } from "./test-utils";

// Extend Vitest's expect interface
interface CustomMatchers<R = unknown> {
	toBeValidElement(): R;
	toHaveValidPosition(): R;
	toBeWithinPageBounds(page: PageSettings): R;
	toHaveValidZIndex(): R;
	toMatchElementProperties(expected: Partial<Element>): R;
	toHaveValidEditorState(): R;
	toContainElementWithId(elementId: string): R;
	toHaveElementsOnPage(pageId: string, count?: number): R;
	toHaveValidPageSettings(): R;
	toBeValidTableElement(): R;
	toHaveValidBlockStructure(): R;
	toBeValidAddressElement(): R;
	toHaveConsistentZIndices(): R;
	toHaveUniqueElementIds(): R;
}

declare module "vitest" {
	interface Assertion<T> extends CustomMatchers<T> {}
	interface AsymmetricMatchersContaining extends CustomMatchers {}
}

// Custom matcher to validate element structure
expect.extend({
	toBeValidElement(received: Element) {
		const { isNot } = this;

		const pass =
			typeof received === "object" &&
			received !== null &&
			typeof received.id === "string" &&
			received.id.length > 0 &&
			typeof received.type === "string" &&
			typeof received.x === "number" &&
			typeof received.y === "number" &&
			typeof received.width === "number" &&
			typeof received.height === "number" &&
			received.width > 0 &&
			received.height > 0 &&
			typeof received.zIndex === "number" &&
			received.zIndex >= 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return `Expected element not to be valid, but it was valid`;
				}

				if (!received) {
					return `Expected element to be valid, but received ${received}`;
				}

				const issues: string[] = [];
				if (typeof received.id !== "string" || received.id.length === 0) {
					issues.push("id must be a non-empty string");
				}
				if (typeof received.type !== "string") {
					issues.push("type must be a string");
				}
				if (typeof received.x !== "number") {
					issues.push("x must be a number");
				}
				if (typeof received.y !== "number") {
					issues.push("y must be a number");
				}
				if (typeof received.width !== "number" || received.width <= 0) {
					issues.push("width must be a positive number");
				}
				if (typeof received.height !== "number" || received.height <= 0) {
					issues.push("height must be a positive number");
				}
				if (typeof received.zIndex !== "number" || received.zIndex < 0) {
					issues.push("zIndex must be a non-negative number");
				}

				return `Expected element to be valid, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toHaveValidPosition(received: Element) {
		const { isNot } = this;

		const pass =
			typeof received.x === "number" &&
			typeof received.y === "number" &&
			received.x >= 0 &&
			received.y >= 0 &&
			!Number.isNaN(received.x) &&
			!Number.isNaN(received.y);

		return {
			pass,
			message: () => {
				if (isNot) {
					return `Expected element not to have valid position, but position was valid (x: ${received.x}, y: ${received.y})`;
				}
				return `Expected element to have valid position, but got x: ${received.x}, y: ${received.y}`;
			},
		};
	},

	toBeWithinPageBounds(received: Element, page: PageSettings) {
		const { isNot } = this;

		// Convert page dimensions from mm to pixels (assuming 96 DPI)
		const pageWidthPx = (page.width / 25.4) * 96;
		const pageHeightPx = (page.height / 25.4) * 96;

		const pass =
			received.x >= 0 &&
			received.y >= 0 &&
			received.x + received.width <= pageWidthPx &&
			received.y + received.height <= pageHeightPx;

		return {
			pass,
			message: () => {
				if (isNot) {
					return `Expected element to be outside page bounds, but it was within bounds`;
				}
				return `Expected element to be within page bounds (${pageWidthPx}x${pageHeightPx}), but element extends from (${received.x}, ${received.y}) to (${received.x + received.width}, ${received.y + received.height})`;
			},
		};
	},

	toHaveValidZIndex(received: Element) {
		const { isNot } = this;

		const pass =
			typeof received.zIndex === "number" &&
			received.zIndex >= 0 &&
			Number.isInteger(received.zIndex);

		return {
			pass,
			message: () => {
				if (isNot) {
					return `Expected element not to have valid zIndex, but zIndex was valid (${received.zIndex})`;
				}
				return `Expected element to have valid zIndex (non-negative integer), but got ${received.zIndex}`;
			},
		};
	},

	toMatchElementProperties(received: Element, expected: Partial<Element>) {
		const { isNot } = this;

		const mismatches: string[] = [];

		for (const [key, value] of Object.entries(expected)) {
			if (received[key as keyof Element] !== value) {
				mismatches.push(
					`${key}: expected ${value}, got ${received[key as keyof Element]}`,
				);
			}
		}

		const pass = mismatches.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return `Expected element not to match properties, but all properties matched`;
				}
				return `Expected element to match properties, but found mismatches: ${mismatches.join(", ")}`;
			},
		};
	},

	toHaveValidEditorState(received: EditorState) {
		const { isNot } = this;

		const issues: string[] = [];

		// Check if it's a valid object
		if (!received || typeof received !== "object") {
			issues.push("must be an object");
		} else {
			// Check pages array
			if (!Array.isArray(received.pages)) {
				issues.push("pages must be an array");
			} else {
				received.pages.forEach((page, index) => {
					if (!page.id || typeof page.id !== "string") {
						issues.push(`page[${index}] must have a valid id`);
					}
					if (typeof page.width !== "number" || page.width <= 0) {
						issues.push(`page[${index}] must have a valid width`);
					}
					if (typeof page.height !== "number" || page.height <= 0) {
						issues.push(`page[${index}] must have a valid height`);
					}
				});
			}

			// Check elements array
			if (!Array.isArray(received.elements)) {
				issues.push("elements must be an array");
			} else {
				received.elements.forEach((element, index) => {
					if (!element.id || typeof element.id !== "string") {
						issues.push(`element[${index}] must have a valid id`);
					}
					if (
						!element.currentPageId ||
						typeof element.currentPageId !== "string"
					) {
						issues.push(`element[${index}] must have a valid currentPageId`);
					}
				});
			}
		}

		const pass = issues.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected editor state not to be valid, but it was valid";
				}
				return `Expected editor state to be valid, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toContainElementWithId(received: Element[], elementId: string) {
		const { isNot } = this;

		const pass =
			Array.isArray(received) && received.some((el) => el.id === elementId);

		return {
			pass,
			message: () => {
				if (isNot) {
					return `Expected elements not to contain element with id "${elementId}", but it was found`;
				}
				return `Expected elements to contain element with id "${elementId}", but it was not found`;
			},
		};
	},

	toHaveElementsOnPage(
		received: Element[],
		pageId: string,
		expectedCount?: number,
	) {
		const { isNot } = this;

		if (!Array.isArray(received)) {
			return {
				pass: false,
				message: () => "Expected an array of elements",
			};
		}

		const elementsOnPage = received.filter((el) => el.currentPageId === pageId);
		const actualCount = elementsOnPage.length;

		const pass =
			expectedCount !== undefined
				? actualCount === expectedCount
				: actualCount > 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return expectedCount !== undefined
						? `Expected not to have ${expectedCount} elements on page "${pageId}", but found ${actualCount}`
						: `Expected not to have elements on page "${pageId}", but found ${actualCount}`;
				}
				return expectedCount !== undefined
					? `Expected ${expectedCount} elements on page "${pageId}", but found ${actualCount}`
					: `Expected to have elements on page "${pageId}", but found none`;
			},
		};
	},

	toHaveValidPageSettings(received: PageSettings) {
		const { isNot } = this;

		const issues: string[] = [];

		if (!received || typeof received !== "object") {
			issues.push("must be an object");
		} else {
			if (!received.id || typeof received.id !== "string") {
				issues.push("id must be a non-empty string");
			}
			if (!received.format || typeof received.format !== "string") {
				issues.push("format must be a non-empty string");
			}
			if (!["portrait", "landscape"].includes(received.orientation)) {
				issues.push('orientation must be "portrait" or "landscape"');
			}
			if (typeof received.width !== "number" || received.width <= 0) {
				issues.push("width must be a positive number");
			}
			if (typeof received.height !== "number" || received.height <= 0) {
				issues.push("height must be a positive number");
			}
		}

		const pass = issues.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected page settings not to be valid, but they were valid";
				}
				return `Expected page settings to be valid, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toBeValidTableElement(received: Element) {
		const { isNot } = this;

		const issues: string[] = [];

		if (received.type !== "table") {
			issues.push('element type must be "table"');
		}

		if (!received.tableProperties) {
			issues.push("table element must have tableProperties");
		} else {
			const { tableProperties } = received;

			if (
				typeof tableProperties.rows !== "number" ||
				tableProperties.rows <= 0
			) {
				issues.push("tableProperties.rows must be a positive number");
			}
			if (
				typeof tableProperties.columns !== "number" ||
				tableProperties.columns <= 0
			) {
				issues.push("tableProperties.columns must be a positive number");
			}
			if (!Array.isArray(tableProperties.cells)) {
				issues.push("tableProperties.cells must be an array");
			} else if (tableProperties.cells.length !== tableProperties.rows) {
				issues.push("tableProperties.cells length must match rows count");
			} else {
				tableProperties.cells.forEach((row, rowIndex) => {
					if (!Array.isArray(row)) {
						issues.push(`tableProperties.cells[${rowIndex}] must be an array`);
					} else if (row.length !== tableProperties.columns) {
						issues.push(
							`tableProperties.cells[${rowIndex}] length must match columns count`,
						);
					}
				});
			}
		}

		const pass = issues.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected element not to be a valid table element, but it was valid";
				}
				return `Expected element to be a valid table element, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toHaveValidBlockStructure(received: Element) {
		const { isNot } = this;

		const issues: string[] = [];

		if (received.type !== "block") {
			issues.push('element type must be "block"');
		}

		if (!Array.isArray(received.childElementIds)) {
			issues.push("block element must have childElementIds array");
		} else if (received.childElementIds.length === 0) {
			issues.push("block element should have at least one child element");
		} else {
			received.childElementIds.forEach((childId, index) => {
				if (typeof childId !== "string" || childId.length === 0) {
					issues.push(`childElementIds[${index}] must be a non-empty string`);
				}
			});
		}

		const pass = issues.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected element not to have valid block structure, but it was valid";
				}
				return `Expected element to have valid block structure, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toBeValidAddressElement(received: Element) {
		const { isNot } = this;

		const issues: string[] = [];

		if (received.type !== "address") {
			issues.push('element type must be "address"');
		}

		if (!received.isAddressField) {
			issues.push("address element must have isAddressField set to true");
		}

		if (received.addressLayout) {
			const { addressLayout } = received;
			if (typeof addressLayout.sender_y !== "number") {
				issues.push("addressLayout.sender_y must be a number");
			}
			if (typeof addressLayout.recipient_y !== "number") {
				issues.push("addressLayout.recipient_y must be a number");
			}
		}

		const pass = issues.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected element not to be a valid address element, but it was valid";
				}
				return `Expected element to be a valid address element, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toHaveConsistentZIndices(received: Element[]) {
		const { isNot } = this;

		if (!Array.isArray(received)) {
			return {
				pass: false,
				message: () => "Expected an array of elements",
			};
		}

		const issues: string[] = [];
		const zIndices = received.map((el) => el.zIndex || 0);
		const duplicates = zIndices.filter(
			(z, index) => zIndices.indexOf(z) !== index,
		);

		if (duplicates.length > 0) {
			issues.push(
				`duplicate z-indices found: ${[...new Set(duplicates)].join(", ")}`,
			);
		}

		// Check for negative z-indices
		const negativeZIndices = zIndices.filter((z) => z < 0);
		if (negativeZIndices.length > 0) {
			issues.push(`negative z-indices found: ${negativeZIndices.join(", ")}`);
		}

		const pass = issues.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected elements not to have consistent z-indices, but they were consistent";
				}
				return `Expected elements to have consistent z-indices, but found issues: ${issues.join(", ")}`;
			},
		};
	},

	toHaveUniqueElementIds(received: Element[]) {
		const { isNot } = this;

		if (!Array.isArray(received)) {
			return {
				pass: false,
				message: () => "Expected an array of elements",
			};
		}

		const ids = received.map((el) => el.id);
		const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
		const pass = duplicates.length === 0;

		return {
			pass,
			message: () => {
				if (isNot) {
					return "Expected elements not to have unique IDs, but all IDs were unique";
				}
				return `Expected elements to have unique IDs, but found duplicates: ${[...new Set(duplicates)].join(", ")}`;
			},
		};
	},
});

// Helper functions for complex assertions
export namespace AssertionHelpers {
	/**
	 * Assert that an editor state is valid and consistent
	 */
	export function assertEditorStateValid(state: EditorState): void {
		expect(state).toHaveValidEditorState();

		// Check that all elements reference valid pages
		const pageIds = new Set(state.pages.map((p) => p.id));
		state.elements.forEach((element) => {
			if (!pageIds.has(element.currentPageId)) {
				throw new Error(
					`Element ${element.id} references non-existent page ${element.currentPageId}`,
				);
			}
		});

		// Check for unique element IDs
		expect(state.elements).toHaveUniqueElementIds();
	}

	// Alias for compatibility with tests
	export const assertValidEditorState = assertEditorStateValid;

	/**
	 * Assert that elements are properly positioned within page bounds
	 */
	export function assertElementsWithinBounds(
		elements: Element[],
		pages: PageSettings[],
	): void {
		const pageMap = new Map(pages.map((p) => [p.id, p]));

		elements.forEach((element) => {
			const page = pageMap.get(element.currentPageId);
			if (page) {
				expect(element).toBeWithinPageBounds(page);
			}
		});
	}

	/**
	 * Assert that block elements have valid child relationships
	 */
	export function assertValidBlockRelationships(elements: Element[]): void {
		const elementMap = new Map(elements.map((e) => [e.id, e]));

		elements.forEach((element) => {
			if (element.type === "block") {
				expect(element).toHaveValidBlockStructure();

				// Check that all child elements exist and reference this block as parent
				element.childElementIds?.forEach((childId) => {
					const childElement = elementMap.get(childId);
					if (!childElement) {
						throw new Error(
							`Block ${element.id} references non-existent child ${childId}`,
						);
					}
					if (childElement.parentId !== element.id) {
						throw new Error(
							`Child element ${childId} does not reference parent block ${element.id}`,
						);
					}
				});
			}

			// Check parent-child consistency
			if (element.parentId) {
				const parentElement = elementMap.get(element.parentId);
				if (!parentElement) {
					throw new Error(
						`Element ${element.id} references non-existent parent ${element.parentId}`,
					);
				}
				if (parentElement.type !== "block") {
					throw new Error(
						`Element ${element.id} references non-block parent ${element.parentId}`,
					);
				}
				if (!parentElement.childElementIds?.includes(element.id)) {
					throw new Error(
						`Parent block ${element.parentId} does not include child ${element.id} in childElementIds`,
					);
				}
			}
		});
	}

	/**
	 * Assert that table elements have valid structure
	 */
	export function assertValidTableStructure(element: Element): void {
		expect(element).toBeValidTableElement();

		if (element.tableProperties) {
			const { cells } = element.tableProperties;

			// Verify cell structure
			cells.forEach((row, rowIndex) => {
				row.forEach((cell, colIndex) => {
					if (!cell || typeof cell !== "object") {
						throw new Error(`Invalid cell at row ${rowIndex}, col ${colIndex}`);
					}
				});
			});
		}
	}

	/**
	 * Assert that z-indices are properly managed
	 */
	export function assertValidZIndexManagement(elements: Element[]): void {
		expect(elements).toHaveConsistentZIndices();

		// Group elements by page and check z-index ordering
		const elementsByPage = new Map<string, Element[]>();
		elements.forEach((element) => {
			if (!elementsByPage.has(element.currentPageId)) {
				elementsByPage.set(element.currentPageId, []);
			}
			elementsByPage.get(element.currentPageId)?.push(element);
		});

		elementsByPage.forEach((pageElements, pageId) => {
			const sortedByZIndex = [...pageElements].sort(
				(a, b) => (a.zIndex || 0) - (b.zIndex || 0),
			);

			// Check for gaps in z-index sequence (optional - depends on requirements)
			for (let i = 1; i < sortedByZIndex.length; i++) {
				const current = sortedByZIndex[i].zIndex || 0;
				const previous = sortedByZIndex[i - 1].zIndex || 0;

				if (current === previous) {
					throw new Error(
						`Duplicate z-index ${current} found on page ${pageId}`,
					);
				}
			}
		});
	}
}
