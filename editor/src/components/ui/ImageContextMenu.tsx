import { Clipboard<PERSON>ast<PERSON>, Co<PERSON>, Scissors } from "lucide-react";
import { useEffect, useRef } from "react";
import type { ContextMenuItem } from "../../types/image";

interface ImageContextMenuProps {
	isOpen: boolean;
	position: { x: number; y: number };
	onClose: () => void;
	selectedCount: number;
	canPaste: boolean;
	onCopy: () => void;
	onCut: () => void;
	onPaste: () => void;
	onSelect: () => void;
	role?: string;
}

export function ImageContextMenu({
	isOpen,
	position,
	onClose,
	selectedCount,
	canPaste,
	onCopy,
	onCut,
	onPaste,
	onSelect,
	role,
}: ImageContextMenuProps) {
	const menuRef = useRef<HTMLDivElement>(null);

	// Close menu when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener("mousedown", handleClickOutside);
			document.addEventListener("contextmenu", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
			document.removeEventListener("contextmenu", handleClickOutside);
		};
	}, [isOpen, onClose]);

	// Close menu on escape key
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "Escape") {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener("keydown", handleKeyDown);
		}

		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [isOpen, onClose]);

	const handleMenuAction = (action: () => void) => {
		action();
		onClose();
	};

	if (!isOpen) return null;

	// Role-based permissions
	const isAgency = role === "agency";

	// If no images are selected (empty space context menu), show only paste option
	// Note: selectedCount reflects the actual selection, not whether a specific image was right-clicked
	const isEmptySpaceMenu = selectedCount === 0;

	const menuItems: ContextMenuItem[] = isEmptySpaceMenu
		? // Empty space menu - only show paste if user is agency
			isAgency
			? [
					{
						id: "paste",
						label: "Einfügen",
						icon: "paste",
						disabled: !canPaste,
						onClick: () => handleMenuAction(onPaste),
					},
				]
			: []
		: // Image context menu
			[
				{
					id: "select",
					label: "Auswählen",
					icon: "select",
					onClick: () => handleMenuAction(onSelect),
				},
				// Only show copy/cut/paste/move operations for agency users
				...(isAgency
					? [
							{
								id: "separator1",
								separator: true,
							},
							{
								id: "copy",
								label: `Kopieren${selectedCount > 1 ? ` (${selectedCount})` : ""}`,
								icon: "copy",
								disabled: selectedCount === 0,
								onClick: () => handleMenuAction(onCopy),
							},
							{
								id: "cut",
								label: `Ausschneiden${selectedCount > 1 ? ` (${selectedCount})` : ""}`,
								icon: "cut",
								disabled: selectedCount === 0,
								onClick: () => handleMenuAction(onCut),
							},
							{
								id: "paste",
								label: "Einfügen",
								icon: "paste",
								disabled: !canPaste,
								onClick: () => handleMenuAction(onPaste),
							},
						]
					: []),
			];

	const getIcon = (iconName: string) => {
		switch (iconName) {
			case "copy":
				return <Copy className="h-4 w-4" />;
			case "cut":
				return <Scissors className="h-4 w-4" />;
			case "paste":
				return <ClipboardPaste className="h-4 w-4" />;
			default:
				return null;
		}
	};

	return (
		<div
			ref={menuRef}
			className="fixed z-[60] bg-white border border-border rounded-md shadow-lg py-1 min-w-[180px]"
			style={{
				left: position.x,
				top: position.y,
			}}
		>
			{menuItems.map((item) => {
				if (item.separator) {
					return <div key={item.id} className="h-px bg-border my-1" />;
				}

				return (
					<button
						key={item.id}
						type="button"
						className={`px-3 py-2 text-sm cursor-pointer hover:bg-muted flex items-center border-none bg-transparent w-full text-left ${
							item.disabled ? "opacity-50 cursor-not-allowed" : ""
						}`}
						onClick={!item.disabled ? item.onClick : undefined}
						disabled={item.disabled}
					>
						{getIcon(item.icon || "")}
						<span className="ml-2">{item.label}</span>
					</button>
				);
			})}
		</div>
	);
}
