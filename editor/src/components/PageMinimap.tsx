import { GripVertical } from "lucide-react";
import { useCallback, useRef, useState } from "react";
import { usePageDragAndDrop } from "../hooks/usePageDragAndDrop";
import { usePairDragAndDrop } from "../hooks/usePairDragAndDrop";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

interface PageMinimapProps {
	pages: PageSettings[];
	elements: Element[];
	currentPageId: string | null;
	onPageSelect: (pageId: string) => void;
	onPageReorder: (fromIndex: number, toIndex: number) => void;
	onPageSwap: (fromIndex: number, toIndex: number) => void;
	scrollContainerRef: React.RefObject<HTMLDivElement | null>;
	isEditorLocked?: boolean;
}

export function PageMinimap({
	pages,
	elements,
	currentPageId,
	onPageSelect,
	onPageReorder,
	onPageSwap,
	scrollContainerRef: _scrollContainerRef,
	isEditorLocked = false,
}: PageMinimapProps) {
	const pairCount = Math.ceil(pages.length / 2);

	const { dragState, getDragHandleProps, getDropZoneProps } =
		usePairDragAndDrop({
			pairCount,
			onReorder: onPageReorder,
			isDisabled: isEditorLocked,
		});

	// single-page drag and drop
	const { dragState: pageDragState, getDragProps: getPageDragProps } =
		usePageDragAndDrop({
			pages,
			onSwap: onPageSwap,
			isDisabled: isEditorLocked,
		});

	// Determine the page currently being dragged (if any) for the ghost preview
	const draggedPage =
		pageDragState.draggedPageIndex !== null
			? pages[pageDragState.draggedPageIndex]
			: undefined;

	const [scrollingToPageId, setScrollingToPageId] = useState<string | null>(
		null,
	);
	const minimapRef = useRef<HTMLDivElement>(null);

	// Handle page selection with scroll to view
	const handlePageSelectWithScroll = useCallback(
		(pageId: string) => {
			// Set the current page
			onPageSelect(pageId);

			// Set scrolling state for visual feedback
			setScrollingToPageId(pageId);

			// Scroll the page into view with smooth animation
			if (_scrollContainerRef?.current) {
				const pageElement = _scrollContainerRef.current.querySelector(
					`[data-page-id="${pageId}"]`,
				);
				if (pageElement) {
					// Add a small delay to ensure the page selection state has updated
					setTimeout(() => {
						pageElement.scrollIntoView({
							behavior: "smooth",
							block: "center",
							inline: "nearest",
						});

						// Clear scrolling state after animation completes
						setTimeout(() => {
							setScrollingToPageId(null);
						}, 1000);
					}, 50);
				}
			}
		},
		[onPageSelect, _scrollContainerRef],
	);

	// Group pages into pairs (front and back)
	const pagePairs: {
		front: PageSettings;
		back?: PageSettings;
		pairIndex: number;
	}[] = [];
	for (let i = 0; i < pages.length; i += 2) {
		const frontPage = pages[i];
		const backPage = pages[i + 1];
		pagePairs.push({
			front: frontPage,
			back: backPage,
			pairIndex: Math.floor(i / 2),
		});
	}

	// Calculate thumbnail dimensions based on page aspect ratio
	const getThumbnailDimensions = (page: PageSettings) => {
		const aspectRatio = page.width / page.height;
		const maxWidth = 60;
		const maxHeight = 80;

		let width = maxWidth;
		let height = maxWidth / aspectRatio;

		if (height > maxHeight) {
			height = maxHeight;
			width = maxHeight * aspectRatio;
		}

		return { width, height };
	};

	// Get element color based on type
	const getElementColor = (element: Element) => {
		switch (element.type) {
			case "text":
				return "bg-blue-400";
			case "image":
				return "bg-green-400";
			case "table":
				return "bg-purple-400";
			case "block":
				return "bg-orange-400";
			case "address":
				return "bg-red-400";
			default:
				return "bg-gray-400";
		}
	};

	// Render a miniature version of a page
	const renderPageThumbnail = (
		page: PageSettings,
		isBack = false,
		isGhost = false,
	) => {
		const { width, height } = getThumbnailDimensions(page);
		const pageElements = elements.filter(
			(el) => el.currentPageId === page.id && !el.parentId,
		);

		const pageIdxForState = pages.indexOf(page);
		const isDragged =
			pageDragState.isDragging &&
			pageDragState.draggedPageIndex === pageIdxForState;
		const isDropTarget =
			pageDragState.isDragging &&
			pageDragState.dropTargetIndex === pageIdxForState;
		const dropValid = isDropTarget && pageDragState.isDropValid;

		return (
			<button
				key={page.id}
				type="button"
				className={`
					relative bg-white border border-gray-200 shadow-sm ${pageDragState.isDragging ? "cursor-grabbing" : "cursor-pointer"}
					transition-all duration-200 hover:shadow-md hover:border-gray-300
					focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500
					${currentPageId === page.id ? "ring-2 ring-blue-500 border-blue-300" : ""}
					${scrollingToPageId === page.id ? "ring-2 ring-blue-500" : ""}
					${isBack ? "ml-1" : ""}
					${!isGhost && isDragged ? "opacity-0" : ""}
				`}
				style={{
					width: `${width}px`,
					height: `${height}px`,
				}}
				onClick={() => handlePageSelectWithScroll(page.id)}
				{...(!isGhost ? getPageDragProps(pageIdxForState) : {})}
				title={`Seite ${pages.indexOf(page) + 1} - ${page.format || "Unbekanntes Format"}${scrollingToPageId === page.id ? " (Scrolle zur Ansicht...)" : ""}`}
				aria-label={`Zu Seite ${pages.indexOf(page) + 1} gehen${scrollingToPageId === page.id ? " (Scrolle zur Ansicht...)" : ""}`}
			>
				{/* Drop target overlay (skip in ghost) */}
				{!isGhost && isDropTarget && (
					<div
						className={`absolute inset-0 rounded-md pointer-events-none border-2 ${dropValid ? "border-blue-500" : "border-red-500"} border-dashed animate-pulse`}
					/>
				)}
				{/* Page content preview */}
				<div className="absolute inset-0 overflow-hidden p-0.5">
					{pageElements.map((element) => {
						// Page dimensions are stored in centimeters whereas element
						// coordinates/sizes are in millimeters. Convert the page
						// dimensions to millimeters first so the scaling takes the
						// whole page into account (not just the top-left 10%).
						const pageWidthMm = page.width * 10;
						const pageHeightMm = page.height * 10;

						// Calculate scaled position and size based on millimeter units
						const scaledX = (element.x / pageWidthMm) * 100;
						const scaledY = (element.y / pageHeightMm) * 100;
						const scaledWidth = (element.width / pageWidthMm) * 100;
						const scaledHeight = (element.height / pageHeightMm) * 100;

						// Skip elements that are too small to be visible
						if (scaledWidth < 2 || scaledHeight < 2) return null;

						return (
							<div
								key={element.id}
								className={`absolute ${getElementColor(element)} opacity-70 rounded-sm`}
								style={{
									left: `${scaledX}%`,
									top: `${scaledY}%`,
									width: `${scaledWidth}%`,
									height: `${scaledHeight}%`,
									transform: `rotate(${element.rotation || 0}deg)`,
									transformOrigin: "center",
									minWidth: "2px",
									minHeight: "2px",
								}}
							/>
						);
					})}
				</div>

				{/* Page number indicator */}
				<div className="absolute bottom-0 right-0 bg-gray-800 text-white text-xs px-1 py-0.5 rounded-tl text-center leading-none">
					{pages.indexOf(page) + 1}
				</div>

				{/* Current page indicator */}
				{!isGhost && currentPageId === page.id && (
					<div className="absolute top-0 left-0 w-2 h-2 bg-blue-500 rounded-br"></div>
				)}
			</button>
		);
	};

	if (pages.length === 0) {
		return null;
	}

	return (
		<>
			<div
				ref={minimapRef}
				className="fixed right-4 top-1/2 transform -translate-y-1/2 z-50 bg-white border border-gray-300 rounded-lg shadow-xl p-3 max-h-[70vh] overflow-y-auto backdrop-blur-sm"
				style={{ width: "150px" }}
			>
				<div className="text-xs font-semibold text-gray-700 mb-3 text-center flex items-center justify-center gap-1">
					<div className="w-2 h-2 bg-blue-500 rounded-full"></div>
					Seiten ({pages.length})
				</div>

				{/* Drag instruction */}
				{dragState.isDragging && (
					<div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700 text-center">
						In den hervorgehobenen Bereichen ablegen zum Neuordnen
					</div>
				)}

				{/* Scroll indicator */}
				{currentPageId && (
					<div className="mb-3 px-1">
						<div className="h-1 bg-gray-200 rounded-full overflow-hidden">
							<div
								className="h-full bg-blue-500 rounded-full transition-all duration-300"
								style={{
									width: `${((pages.findIndex((p) => p.id === currentPageId) + 1) / pages.length) * 100}%`,
								}}
							/>
						</div>
					</div>
				)}

				<div className="space-y-1">
					{pagePairs.map((pair, pairIndex) => (
						<div key={`pair-${pair.front.id}-${pair.back?.id || "single"}`}>
							{/* Drop zone before this pair */}
							{dragState.isDragging &&
								dragState.draggedPairIndex !== null &&
								dragState.draggedPairIndex !== pairIndex &&
								dragState.draggedPairIndex !== pairIndex - 1 && (
									<div
										className={`
									h-3 mx-2 rounded-lg border-2 border-dashed transition-all duration-200 flex items-center justify-center
									border-blue-300 hover:border-blue-500 hover:bg-blue-50
									${dragState.dropTargetIndex === pairIndex && dragState.dropPosition === "before" ? "border-blue-500 bg-blue-100 h-4" : ""}
								`}
										role="application"
										data-testid={`drop-zone-before-${pairIndex}`}
										{...getDropZoneProps(pairIndex, "before")}
									>
										{dragState.dropTargetIndex === pairIndex &&
											dragState.dropPosition === "before" && (
												<div className="text-xs text-blue-600 font-medium">
													Hier ablegen
												</div>
											)}
									</div>
								)}

							{/* Page pair */}
							<div
								className={`
									relative p-2 rounded-lg border-2 border-dashed border-transparent
									transition-all duration-200 hover:bg-gray-50
									${dragState.draggedPairIndex === pairIndex ? "opacity-50 scale-95 border-blue-300 bg-blue-50" : ""}
									${(currentPageId === pair.front.id || currentPageId === pair.back?.id) && dragState.draggedPairIndex !== pairIndex ? "bg-blue-50 border-blue-200" : ""}
								`}
								role="application"
								aria-label={`Seitenpaar ${pairIndex + 1}`}
							>
								{/* Drag handle */}
								{!isEditorLocked && (
									<div
										draggable
										className={`
									absolute -left-2 top-1/2 transform -translate-y-1/2 z-10 p-1 rounded
									transition-all duration-200
									${dragState.draggedPairIndex === pairIndex ? "cursor-grabbing bg-blue-200" : "cursor-grab hover:bg-gray-100"}
								`}
										{...getDragHandleProps(pairIndex)}
										data-testid="drag-handle"
										title={`Ziehen zum Neuordnen von Seitenpaar ${pairIndex + 1}`}
									>
										<GripVertical
											className={`
									w-3 h-3 transition-colors duration-200
									${dragState.draggedPairIndex === pairIndex ? "text-blue-600" : "text-gray-400 hover:text-gray-600"}
								`}
										/>
									</div>
								)}

								{/* Page pair */}
								<div className="flex items-start space-x-1 mb-2">
									{/* Front page */}
									{renderPageThumbnail(pair.front, false)}

									{/* Back page */}
									{pair.back && renderPageThumbnail(pair.back, true)}
								</div>

								{/* Pair info */}
								<div className="flex items-center justify-between text-xs">
									<span className="text-gray-500 font-medium">
										Paar {pairIndex + 1}
									</span>
									<span className="text-gray-400">
										{pair.back ? "2 Seiten" : "1 Seite"}
									</span>
								</div>
							</div>

							{/* Drop zone after the last pair */}
							{pairIndex === pagePairs.length - 1 &&
								dragState.isDragging &&
								dragState.draggedPairIndex !== null &&
								dragState.draggedPairIndex !== pairIndex && (
									<div
										className={`
									h-3 mx-2 rounded-lg border-2 border-dashed transition-all duration-200 flex items-center justify-center
									border-blue-300 hover:border-blue-500 hover:bg-blue-50
									${dragState.dropTargetIndex === pairIndex && dragState.dropPosition === "after" ? "border-blue-500 bg-blue-100 h-4" : ""}
								`}
										role="application"
										data-testid={`drop-zone-after-${pairIndex}`}
										{...getDropZoneProps(pairIndex, "after")}
									>
										{dragState.dropTargetIndex === pairIndex &&
											dragState.dropPosition === "after" && (
												<div className="text-xs text-blue-600 font-medium">
													Hier ablegen
												</div>
											)}
									</div>
								)}
						</div>
					))}
				</div>
			</div>
			{/* Ghost thumbnail while dragging a single page */}
			{pageDragState.isDragging && draggedPage && (
				<div
					style={{
						position: "fixed",
						left: pageDragState.pointerX,
						top: pageDragState.pointerY,
						transform: "translate(-50%, -50%) scale(1.05)",
						zIndex: 999999,
						pointerEvents: "none",
					}}
				>
					{renderPageThumbnail(draggedPage, false, true)}
				</div>
			)}
		</>
	);
}
