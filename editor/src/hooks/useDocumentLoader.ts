import { useCallback, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import {
	fetchDocumentVariables,
	fetchPageFormats,
	fetchTestData,
	fetchVersionHistory,
	getFrontifyFlag,
	type PageFormat,
	type TestDataRecord,
	type VersionHistoryEntry,
} from "@/utils/apiService";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import { loadDocumentFromApi } from "../utils/documentLoader";

interface DocumentLoaderProps {
	// Document state
	setDocumentId: (id: string) => void;
	setCurrentVersion: (version: string) => void;
	setRole: (role: string | undefined) => void;
	setIsLoading: (loading: boolean) => void;
	setLoadError: (error: string | null) => void;
	setIsEditorLocked: (locked: boolean) => void;
	setIsReleased: (released: boolean) => void;
	setAllowRelease: (allow: boolean) => void;
	setRawLoadedData: (
		data: { pages: PageSettings[]; elements: Element[] } | null,
	) => void;
	setShowPageNumbers: (show: boolean) => void;
	setDeletedPageNumberIds: (ids: Set<string>) => void;

	// API data state
	setApiFormats: (formats: PageFormat[]) => void;
	setDocumentVariables: (variables: string[]) => void;
	setTestData: (data: TestDataRecord[]) => void;
	setVersionHistory: (history: VersionHistoryEntry[]) => void;

	// Editor state
	setEditorState: (
		state: { pages: PageSettings[]; elements: Element[] },
		addToHistory?: boolean,
	) => void;

	// Current state values needed for processing
	rawLoadedData: { pages: PageSettings[]; elements: Element[] } | null;
	apiFormats: PageFormat[];
	documentId: string;
}

export function useDocumentLoader({
	setDocumentId,
	setCurrentVersion,
	setRole,
	setIsLoading,
	setLoadError,
	setIsEditorLocked,
	setIsReleased,
	setAllowRelease,
	setRawLoadedData,
	setShowPageNumbers,
	setDeletedPageNumberIds,
	setApiFormats,
	setDocumentVariables,
	setTestData,
	setVersionHistory,
	setEditorState,
	rawLoadedData,
	apiFormats,
	documentId,
}: DocumentLoaderProps) {
	// Effect to load document from API based on URL parameters
	useEffect(() => {
		const loadDocumentFromUrl = async () => {
			setIsLoading(true);
			setLoadError(null);
			try {
				// Parse URL parameters
				const params = new URLSearchParams(window.location.search);
				const id = params.get("id");
				const version = params.get("version");

				// Require both id and version parameters
				if (!id || !version) {
					setLoadError(
						'Required URL parameters "id" and "version" must be provided',
					);
					setIsLoading(false);
					return;
				}
				setCurrentVersion(version); // Set current version from URL

				setDocumentId(id);
				console.log(`Starting to load document: id=${id}, version=${version}`);

				const result = await loadDocumentFromApi(id, version);
				console.log("Document loaded from API:", result);

				if (result.showPageNumbers !== undefined) {
					setShowPageNumbers(result.showPageNumbers);
				} else {
					setShowPageNumbers(false);
				}

				// Set deleted page number IDs if available
				if (result.deletedPageNumberIds !== undefined) {
					setDeletedPageNumberIds(new Set(result.deletedPageNumberIds));
				} else {
					setDeletedPageNumberIds(new Set());
				}

				setRole(result.role);
				setAllowRelease(result.allowRelease);

				// Fetch frontify status
				try {
					const frontifyStatus = await getFrontifyFlag(id);
					if (frontifyStatus && frontifyStatus.frontify === 1) {
						setIsReleased(true);
						setIsEditorLocked(true);
						console.log("Document is initially set to frontify.");
					} else {
						setIsReleased(false);
						setIsEditorLocked(false);
						console.log(
							"Document is NOT initially set to frontify or status not found.",
						);
					}
				} catch (frontifyError) {
					console.error("Failed to load frontify status:", frontifyError);
					// Default to not released/locked if there's an error fetching status
					setIsReleased(false);
					setIsEditorLocked(false);
				}

				try {
					const variables = await fetchDocumentVariables(id);
					setDocumentVariables(variables);
					console.log("Document variables loaded:", variables);
				} catch (variableError) {
					console.error("Failed to load document variables:", variableError);
				}

				try {
					const testDataResult = await fetchTestData(id);
					setTestData(testDataResult);
					console.log("Test data loaded:", testDataResult);
				} catch (testDataError) {
					console.error("Failed to load test data:", testDataError);
				}

				// Fetch version history for this job
				try {
					const history = await fetchVersionHistory(id);
					setVersionHistory(history);
					console.log("Version history loaded:", history);
				} catch (historyError) {
					console.error("Failed to load version history:", historyError);
				}

				setRawLoadedData({ pages: result.pages, elements: result.elements });
				console.log("Stored raw loaded data, waiting for formats...");
			} catch (error) {
				console.error("Error loading document:", error);
				setLoadError(
					error instanceof Error ? error.message : "Failed to load document",
				);
				setIsLoading(false);
			}
		};

		loadDocumentFromUrl();
	}, [
		setAllowRelease,
		setCurrentVersion,
		setDocumentId,
		setDocumentVariables,
		setIsEditorLocked,
		setIsLoading, // Default to not released/locked if there's an error fetching status
		setIsReleased,
		setLoadError,
		setRawLoadedData,
		setRole,
		setShowPageNumbers,
		setDeletedPageNumberIds,
		setTestData,
		setVersionHistory,
	]); // Empty dependency array for initial load from URL params

	// Fetch page formats from API
	useEffect(() => {
		const getPageFormats = async () => {
			try {
				const formats = await fetchPageFormats();
				setApiFormats(formats);
			} catch (err) {
				console.error("Failed to fetch page formats:", err);
				// Continue using static formats as fallback
			}
		};

		getPageFormats();
	}, [setApiFormats]);

	// New effect to process loaded data once formats are available
	useEffect(() => {
		// Make sure apiFormats is not empty and rawLoadedData is present
		if (rawLoadedData && apiFormats.length > 0) {
			console.log(
				"Processing loaded document data now that formats are available.",
			);

			const normalizedPages = rawLoadedData.pages.map((page): PageSettings => {
				const format = typeof page.format === "string" ? page.format : "A4";
				const apiFormat = apiFormats.find((f) => f.name === format);

				// Calculate trim values in cm
				const trimCm = apiFormat
					? {
							trim_top: (apiFormat.trim_top ?? 0) / 10,
							trim_right: (apiFormat.trim_right ?? 0) / 10,
							trim_bottom: (apiFormat.trim_bottom ?? 0) / 10,
							trim_left: (apiFormat.trim_left ?? 0) / 10,
						}
					: {}; // Default to empty object if format not found

				// Calculate page number coords in cm
				const pageNumCm = apiFormat
					? {
							pagenumber_x:
								apiFormat.pagenumber_w !== null &&
								apiFormat.pagenumber_w !== undefined
									? apiFormat.pagenumber_w / 10
									: null,
							pagenumber_y:
								apiFormat.pagenumber_y !== null &&
								apiFormat.pagenumber_y !== undefined
									? apiFormat.pagenumber_y / 10
									: null,
						}
					: { pagenumber_x: null, pagenumber_y: null }; // Default to null if format not found

				// Calculate address coords in cm
				const addressCm = apiFormat
					? {
							address_x:
								apiFormat.address_x !== null &&
								apiFormat.address_x !== undefined
									? apiFormat.address_x / 10
									: null,
							address_y:
								apiFormat.address_y !== null &&
								apiFormat.address_y !== undefined
									? apiFormat.address_y / 10
									: null,
						}
					: { address_x: null, address_y: null }; // Default to null if format not found

				// Determine width/height, preferring loaded data, then format, then default
				const width =
					typeof page.width === "number"
						? page.width
						: apiFormat
							? apiFormat.width / 10
							: 21;
				const height =
					typeof page.height === "number"
						? page.height
						: apiFormat
							? apiFormat.height / 10
							: 29.7;
				const orientation = width > height ? "landscape" : "portrait"; // Determine orientation

				return {
					...page, // Spread original page data first
					id: page.id || uuidv4(), // Ensure ID exists
					format,
					width,
					height,
					orientation,
					...trimCm, // Add/overwrite with trim values
					...pageNumCm, // Add/overwrite with page number coords
					...addressCm, // Add/overwrite with address coords
				};
			});

			console.log(
				"Normalized pages with trim and page numbers:",
				normalizedPages,
			);

			// Update the state
			setEditorState(
				{
					pages: normalizedPages,
					elements: rawLoadedData.elements,
				},
				false,
			); // Bypass history throttling for initial load

			// Set the first page as current if available
			if (normalizedPages.length > 0) {
				// This would need to be handled by the parent component
				console.log(
					"Document loaded successfully with",
					normalizedPages.length,
					"pages",
				);
			}

			setIsLoading(false);
		}
	}, [rawLoadedData, apiFormats, setEditorState, setIsLoading]);

	const handleRefreshVersionHistory = useCallback(async () => {
		if (documentId) {
			try {
				console.log(`Refreshing version history for document: ${documentId}`);
				const history = await fetchVersionHistory(documentId);
				setVersionHistory(history);
				console.log("Version history refreshed:", history);
			} catch (error) {
				console.error("Failed to refresh version history:", error);
				// Optionally, display a toast message to the user about the error
			}
		} else {
			console.warn("Cannot refresh version history: documentId is not set.");
		}
	}, [documentId, setVersionHistory]);

	return {
		handleRefreshVersionHistory,
	};
}
