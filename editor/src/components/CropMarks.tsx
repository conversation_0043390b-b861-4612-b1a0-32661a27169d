import type React from "react";

interface CropMarksProps {
	/** Page width in cm */
	pageWidth: number;
	/** Page height in cm */
	pageHeight: number;
	/** Trim margins in cm */
	trimLeft: number;
	trimRight: number;
	trimTop: number;
	trimBottom: number;
}

export const CropMarks: React.FC<CropMarksProps> = ({
	pageWidth,
	pageHeight,
	trimLeft,
	trimRight,
	trimTop,
	trimBottom,
}) => {
	// Distance from content edge to crop mark in cm
	const markOffset = 0.2; // cm

	// Content area boundaries (all in cm)
	const contentLeft = trimLeft;
	const contentRight = trimLeft + pageWidth;
	const contentTop = trimTop;
	const contentBottom = trimTop + pageHeight;

	// Calculate available space for marks in each direction
	const leftMarkLength = trimLeft - markOffset;
	const rightMarkLength = trimRight - markOffset;
	const topMarkLength = trimTop - markOffset;
	const bottomMarkLength = trimBottom - markOffset;

	const markThickness = 0.5;

	const baseMarkStyle = {
		position: "absolute" as const,
		borderColor: "#000000",
		borderStyle: "solid",
		borderWidth: 0,
	};

	return (
		<div
			className="absolute inset-0 pointer-events-none"
			style={{ zIndex: 10000 }}
		>
			{/* Top-left corner marks */}
			{/* Horizontal mark extending left */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentLeft - markOffset - leftMarkLength}cm`,
					top: `${contentTop}cm`,
					width: `${leftMarkLength}cm`,
					height: 0,
					borderTopWidth: `${markThickness}px`,
					transform: "translateY(-50%)",
				}}
			/>
			{/* Vertical mark extending up */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentLeft}cm`,
					top: `${contentTop - markOffset - topMarkLength}cm`,
					width: 0,
					height: `${topMarkLength}cm`,
					borderLeftWidth: `${markThickness}px`,
					transform: "translateX(-50%)",
				}}
			/>

			{/* Top-right corner marks */}
			{/* Horizontal mark extending right */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentRight + markOffset}cm`,
					top: `${contentTop}cm`,
					width: `${rightMarkLength}cm`,
					height: 0,
					borderTopWidth: `${markThickness}px`,
					transform: "translateY(-50%)",
				}}
			/>
			{/* Vertical mark extending up */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentRight}cm`,
					top: `${contentTop - markOffset - topMarkLength}cm`,
					width: 0,
					height: `${topMarkLength}cm`,
					borderLeftWidth: `${markThickness}px`,
					transform: "translateX(-50%)",
				}}
			/>

			{/* Bottom-left corner marks */}
			{/* Horizontal mark extending left */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentLeft - markOffset - leftMarkLength}cm`,
					top: `${contentBottom}cm`,
					width: `${leftMarkLength}cm`,
					height: 0,
					borderTopWidth: `${markThickness}px`,
					transform: "translateY(-50%)",
				}}
			/>
			{/* Vertical mark extending down */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentLeft}cm`,
					top: `${contentBottom + markOffset}cm`,
					width: 0,
					height: `${bottomMarkLength}cm`,
					borderLeftWidth: `${markThickness}px`,
					transform: "translateX(-50%)",
				}}
			/>

			{/* Bottom-right corner marks */}
			{/* Horizontal mark extending right */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentRight + markOffset}cm`,
					top: `${contentBottom}cm`,
					width: `${rightMarkLength}cm`,
					height: 0,
					borderTopWidth: `${markThickness}px`,
					transform: "translateY(-50%)",
				}}
			/>
			{/* Vertical mark extending down */}
			<div
				style={{
					...baseMarkStyle,
					left: `${contentRight}cm`,
					top: `${contentBottom + markOffset}cm`,
					width: 0,
					height: `${bottomMarkLength}cm`,
					borderLeftWidth: `${markThickness}px`,
					transform: "translateX(-50%)",
				}}
			/>
		</div>
	);
};
