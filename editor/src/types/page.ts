export type PageFormat = string;
export type PageOrientation = "portrait" | "landscape";

export interface PageDimensions {
	width: number;
	height: number;
}

export interface Page {
	id: string;
	format: PageFormat;
	orientation?: PageOrientation;
	dimensions: PageDimensions;
}

export interface PageSettings {
	id: string;
	format: string;
	orientation: "portrait" | "landscape";
	width: number;
	height: number;
	trim_top?: number;
	trim_right?: number;
	trim_bottom?: number;
	trim_left?: number;
	pagenumber_x?: number | null;
	pagenumber_y?: number | null;
	address_x?: number | null;
	address_y?: number | null;
}
