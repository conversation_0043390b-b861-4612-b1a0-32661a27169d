import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import type { Element } from "@/types/element";
import type { TableProperties } from "@/types/table";
import { TableFormatToolbar } from "../TableFormatToolbar";

// Mock the dependencies
vi.mock("@/utils/elementHelpers", () => ({
	getScrollAwarePosition: vi.fn(() => ({ x: 0, y: 0 })),
}));

vi.mock("@/utils/unitConversion", () => ({
	ptToPx: vi.fn((pt) => pt),
	roundToTwoDecimals: vi.fn((num) => Math.round(num * 100) / 100),
}));

describe("TableFormatToolbar", () => {
	const mockApiColors = [
		{ name: "Red", hex: "#FF0000", rgb: "255,0,0", cmyk: "0,100,100,0" },
		{ name: "<PERSON>", hex: "#0000FF", rgb: "0,0,255", cmyk: "100,100,0,0" },
	];

	const createTableElement = (tableProperties: TableProperties): Element => ({
		id: "test-table",
		currentPageId: "page-1",
		type: "table",
		x: 0,
		y: 0,
		width: 200,
		height: 100,
		rotation: 0,
		zIndex: 1,
		tableProperties,
	});

	it("should not crash when selecting a cell after a colspan cell", () => {
		// Create a table with a colspan cell that could cause the crash
		const tableProperties: TableProperties = {
			rows: 2,
			columns: 4,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				// Row 0: [A1, B1-C1 (colspan=2), D1]
				[
					{
						content: "A1",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B1-C1",
						colspan: 2,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "D1",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
				// Row 1: [A2, B2, C2, D2]
				[
					{
						content: "A2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "C2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "D2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
			],
			// Simulate selecting cell at logical position (1, 2) which is C2
			// This would previously crash because logical position (1, 2) doesn't directly
			// correspond to cells[1][2] due to the colspan in row 0
			selection: {
				start: { row: 1, col: 2 },
				end: { row: 1, col: 2 },
			},
		};

		const tableElement = createTableElement(tableProperties);

		// This should not crash
		expect(() => {
			render(
				<TableFormatToolbar
					selectedElement={tableElement}
					onUpdateElement={vi.fn()}
					onAddElement={vi.fn()}
					apiColors={mockApiColors}
					isLoadingColors={false}
					colorError={null}
				/>,
			);
		}).not.toThrow();

		// Verify that the component renders without errors
		expect(screen.getByText("Tabelle hinzufügen")).toBeInTheDocument();
	});

	it("should handle single cell selection correctly", () => {
		const tableProperties: TableProperties = {
			rows: 2,
			columns: 2,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				[
					{
						content: "A1",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B1",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
				[
					{
						content: "A2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
			],
			selection: {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			},
		};

		const tableElement = createTableElement(tableProperties);

		render(
			<TableFormatToolbar
				selectedElement={tableElement}
				onUpdateElement={vi.fn()}
				onAddElement={vi.fn()}
				apiColors={mockApiColors}
				isLoadingColors={false}
				colorError={null}
			/>,
		);

		// Should render without crashing
		expect(screen.getByText("Tabelle hinzufügen")).toBeInTheDocument();
	});

	it("should apply background color only to selected cells in tables with colspan/rowspan", () => {
		// This test verifies that the isCellSelectedInLogicalGrid function is used correctly
		// in the background color application logic within BorderSelectionControl callback
		const mockOnUpdateElement = vi.fn();

		// Create a table with colspan cells that could cause incorrect background color application
		const tableProperties: TableProperties = {
			rows: 3,
			columns: 4,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				// Row 0: [A1, B1-C1 (colspan=2), D1]
				[
					{
						content: "A1",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B1-C1",
						colspan: 2,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "D1",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
				// Row 1: [A2, B2, C2, D2]
				[
					{
						content: "A2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "C2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "D2",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
				// Row 2: [A3, B3, C3, D3]
				[
					{
						content: "A3",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "B3",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "C3",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
					{
						content: "D3",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
					},
				],
			],
			// Select logical cells B2 and C2 (which should be cells[1][1] and cells[1][2])
			selection: {
				start: { row: 1, col: 1 },
				end: { row: 1, col: 2 },
			},
			selectedCellsBackgroundColor: "#FF0000", // Red background
		};

		const tableElement = createTableElement(tableProperties);

		render(
			<TableFormatToolbar
				selectedElement={tableElement}
				onUpdateElement={mockOnUpdateElement}
				onAddElement={vi.fn()}
				apiColors={mockApiColors}
				isLoadingColors={false}
				colorError={null}
			/>,
		);

		// The component should render without crashing, which validates that
		// the isCellSelectedInLogicalGrid function is being used correctly
		expect(
			screen.getByText("Hintergrund (Ausgewählte Zellen)"),
		).toBeInTheDocument();
	});
});
