import { v4 as uuidv4 } from "uuid";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

/**
 * Sort elements by z-index while preserving original order for elements with same z-index
 */
export function sortElementsByZIndex(elements: Element[]): Element[] {
	const indexedElements = elements.map((element, index) => ({
		element,
		originalIndex: index,
	}));

	indexedElements.sort((a, b) => {
		const zIndexA = a.element.zIndex ?? 0;
		const zIndexB = b.element.zIndex ?? 0;
		if (zIndexA !== zIndexB) {
			return zIndexA - zIndexB;
		}
		return a.originalIndex - b.originalIndex;
	});

	return indexedElements.map(({ element }) => element);
}

/**
 * Get top-level elements for a specific page (elements that are not children of a block)
 */
export function getTopLevelElementsForPage(
	elements: Element[],
	pageId: string,
): Element[] {
	return elements.filter(
		(element) => element.currentPageId === pageId && !element.parentId,
	);
}

/**
 * Get child elements for a block element
 */
export function getChildElements(
	elements: Element[],
	blockElement: Element,
): Element[] {
	if (blockElement.type !== "block" || !blockElement.childElementIds) {
		return [];
	}
	return elements.filter((el) => blockElement.childElementIds?.includes(el.id));
}

/**
 * Calculate the highest z-index for elements on a specific page
 */
export function getHighestZIndexOnPage(
	elements: Element[],
	pageId: string,
): number {
	const elementsOnPage = elements.filter(
		(el) => el.currentPageId === pageId && !el.parentId,
	);
	return elementsOnPage.reduce((max, el) => {
		return Math.max(max, el.zIndex !== undefined ? el.zIndex : 0);
	}, 1);
}

/**
 * Create a new element with proper z-index
 */
export function createElement(
	type: Element["type"],
	pageId: string,
	x: number,
	y: number,
	width: number,
	height: number,
	elements: Element[],
	additionalProps: Partial<Element> = {},
): Element {
	const highestZIndex = getHighestZIndexOnPage(elements, pageId);

	return {
		id: uuidv4(),
		type,
		x,
		y,
		width,
		height,
		rotation: 0,
		currentPageId: pageId,
		zIndex: highestZIndex + 1,
		...additionalProps,
	};
}

/**
 * Clone an element with a new ID and position
 */
export function cloneElement(
	originalElement: Element,
	newX: number,
	newY: number,
	newPageId?: string,
	elements?: Element[],
): Element {
	const targetPageId = newPageId || originalElement.currentPageId;
	let newZIndex = originalElement.zIndex;

	if (elements) {
		const highestZIndex = getHighestZIndexOnPage(elements, targetPageId);
		newZIndex = highestZIndex + 1;
	}

	// Deep-clone nested structures (e.g. tableProperties) so that the cloned
	// element is completely independent from the original. JSON stringify is
	// sufficient here because the element object only contains serialisable
	// data (plain objects, arrays, numbers, strings, booleans).
	const clonedTableProperties = originalElement.tableProperties
		? (JSON.parse(
				JSON.stringify(originalElement.tableProperties),
			) as Element["tableProperties"])
		: undefined;

	return {
		...originalElement,
		id: uuidv4(),
		x: newX,
		y: newY,
		currentPageId: targetPageId,
		zIndex: newZIndex,
		parentId: undefined, // Remove parent relationship when cloning
		childElementIds: undefined, // Remove child relationships when cloning
		tableProperties: clonedTableProperties,
	};
}

/**
 * Clone a block element with all its children
 */
export function cloneBlockWithChildren(
	blockElement: Element,
	childElements: Element[],
	newX: number,
	newY: number,
	newPageId: string,
	allElements: Element[],
): { block: Element; children: Element[] } {
	const newBlockId = uuidv4();
	const highestZIndex = getHighestZIndexOnPage(allElements, newPageId);

	const newBlock: Element = {
		...blockElement,
		id: newBlockId,
		x: newX,
		y: newY,
		currentPageId: newPageId,
		zIndex: highestZIndex + 1,
		childElementIds: [],
	};

	const newChildren: Element[] = [];
	const newChildIds: string[] = [];

	childElements.forEach((child) => {
		const newChildId = uuidv4();
		newChildren.push({
			...child,
			id: newChildId,
			parentId: newBlockId,
			currentPageId: newPageId,
		});
		newChildIds.push(newChildId);
	});

	newBlock.childElementIds = newChildIds;

	return { block: newBlock, children: newChildren };
}

/**
 * Check if a point is inside an element's bounds
 */
export function isPointInElement(
	x: number,
	y: number,
	element: Element,
): boolean {
	return (
		x >= element.x &&
		x <= element.x + element.width &&
		y >= element.y &&
		y <= element.y + element.height
	);
}

/**
 * Find elements at a specific position
 */
export function getElementsAtPosition(
	elements: Element[],
	x: number,
	y: number,
	pageId: string,
): Element[] {
	const elementsOnPage = elements.filter((el) => el.currentPageId === pageId);
	return elementsOnPage.filter((element) => isPointInElement(x, y, element));
}

/**
 * Update an element in the elements array
 */
export function updateElement(
	elements: Element[],
	updatedElement: Element,
): Element[] {
	return elements.map((el) =>
		el.id === updatedElement.id ? updatedElement : el,
	);
}

/**
 * Remove an element and its children from the elements array
 */
export function removeElement(
	elements: Element[],
	elementId: string,
): Element[] {
	const elementToRemove = elements.find((el) => el.id === elementId);
	if (!elementToRemove) return elements;

	let elementsToRemove = [elementId];

	// If it's a block, also remove its children
	if (elementToRemove.type === "block" && elementToRemove.childElementIds) {
		elementsToRemove = [
			...elementsToRemove,
			...elementToRemove.childElementIds,
		];
	}

	return elements.filter((el) => !elementsToRemove.includes(el.id));
}

/**
 * Move element to a different page
 */
export function moveElementToPage(
	elements: Element[],
	elementId: string,
	targetPageId: string,
): Element[] {
	return elements.map((el) => {
		if (el.id === elementId) {
			return { ...el, currentPageId: targetPageId };
		}
		// Also move children if it's a block
		if (el.parentId === elementId) {
			return { ...el, currentPageId: targetPageId };
		}
		return el;
	});
}

/**
 * Get all elements that would be affected by moving/deleting an element
 */
export function getAffectedElements(
	elements: Element[],
	elementId: string,
): Element[] {
	const element = elements.find((el) => el.id === elementId);
	if (!element) return [];

	let affectedIds = [elementId];

	// If it's a block, include its children
	if (element.type === "block" && element.childElementIds) {
		affectedIds = [...affectedIds, ...element.childElementIds];
	}

	return elements.filter((el) => affectedIds.includes(el.id));
}

/**
 * Validate element bounds within page dimensions
 */
export function validateElementBounds(
	element: Element,
	page: PageSettings,
): Element {
	const maxX = page.width * 10 - element.width; // Convert cm to mm
	const maxY = page.height * 10 - element.height;

	return {
		...element,
		x: Math.max(0, Math.min(element.x, maxX)),
		y: Math.max(0, Math.min(element.y, maxY)),
	};
}

/**
 * Get element hierarchy (parent and children)
 */
export function getElementHierarchy(
	elements: Element[],
	elementId: string,
): {
	element: Element | null;
	parent: Element | null;
	children: Element[];
} {
	const element = elements.find((el) => el.id === elementId) || null;
	const parent = element?.parentId
		? elements.find((el) => el.id === element.parentId) || null
		: null;
	const children = element?.childElementIds
		? elements.filter((el) => element.childElementIds?.includes(el.id))
		: [];

	return { element, parent, children };
}
