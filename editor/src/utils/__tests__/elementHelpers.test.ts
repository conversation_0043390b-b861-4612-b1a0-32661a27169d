import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { getScrollAwarePosition } from "../elementHelpers";

// Mock the unitConversion module
vi.mock("../unitConversion", () => ({
	pxToMm: vi.fn((px: number) => px / 3.779528),
	roundToTwoDecimals: vi.fn((num: number) => Math.round(num * 100) / 100),
}));

describe("elementHelpers", () => {
	beforeEach(() => {
		// Clear DOM before each test
		document.body.innerHTML = "";
	});

	afterEach(() => {
		// Clean up DOM after each test
		document.body.innerHTML = "";
		vi.clearAllMocks();
	});

	describe("getScrollAwarePosition", () => {
		it("should return default position when no scroll container exists", () => {
			const position = getScrollAwarePosition();

			expect(position).toEqual({
				x: 20,
				y: 20,
			});
		});

		it("should return default position when scroll container exists but no visible page", () => {
			// Create scroll container without visible page
			const scrollContainer = document.createElement("div");
			scrollContainer.className = "overflow-y-auto";
			document.body.appendChild(scrollContainer);

			const position = getScrollAwarePosition();

			expect(position).toEqual({
				x: 20,
				y: 20,
			});
		});

		it("should calculate position based on visible page when both scroll container and visible page exist", () => {
			// Create scroll container
			const scrollContainer = document.createElement("div");
			scrollContainer.className = "overflow-y-auto";
			scrollContainer.style.position = "fixed";
			scrollContainer.style.top = "100px";
			scrollContainer.style.left = "50px";
			scrollContainer.style.width = "800px";
			scrollContainer.style.height = "600px";
			document.body.appendChild(scrollContainer);

			// Create page structure with visible page (blue border)
			const pageWrapper = document.createElement("div");
			const pageContainer = document.createElement("div");
			const visiblePage = document.createElement("div");
			visiblePage.className = "border-2 border-blue-500";
			visiblePage.style.position = "absolute";
			visiblePage.style.top = "200px";
			visiblePage.style.left = "100px";
			visiblePage.style.width = "600px";
			visiblePage.style.height = "800px";

			pageContainer.appendChild(visiblePage);
			pageWrapper.appendChild(pageContainer);
			scrollContainer.appendChild(pageWrapper);

			const position = getScrollAwarePosition();

			expect(position.x).toBe(20); // Default x margin
			expect(position.y).toBeGreaterThanOrEqual(20); // Should be at least default y
		});

		it("should handle edge case when visible page is at viewport top", () => {
			// Create scroll container at top of viewport
			const scrollContainer = document.createElement("div");
			scrollContainer.className = "overflow-y-auto";
			scrollContainer.style.position = "fixed";
			scrollContainer.style.top = "0px";
			scrollContainer.style.left = "0px";
			scrollContainer.style.width = "800px";
			scrollContainer.style.height = "600px";
			document.body.appendChild(scrollContainer);

			// Create page at same position as container
			const pageWrapper = document.createElement("div");
			const pageContainer = document.createElement("div");
			const visiblePage = document.createElement("div");
			visiblePage.className = "border-2 border-blue-500";
			visiblePage.style.position = "absolute";
			visiblePage.style.top = "0px";
			visiblePage.style.left = "0px";

			pageContainer.appendChild(visiblePage);
			pageWrapper.appendChild(pageContainer);
			scrollContainer.appendChild(pageWrapper);

			const position = getScrollAwarePosition();

			expect(position.x).toBe(20);
			expect(position.y).toBe(20); // Should use default when at top
		});

		it("should handle multiple elements with border-2 border-blue-500 class", () => {
			// Create scroll container
			const scrollContainer = document.createElement("div");
			scrollContainer.className = "overflow-y-auto";
			document.body.appendChild(scrollContainer);

			// Create multiple elements with the same class
			const firstElement = document.createElement("div");
			firstElement.className = "border-2 border-blue-500";

			const secondElement = document.createElement("div");
			secondElement.className = "border-2 border-blue-500";

			// Only the first one should be used (querySelector returns first match)
			const pageWrapper = document.createElement("div");
			const pageContainer = document.createElement("div");
			pageContainer.appendChild(firstElement);
			pageWrapper.appendChild(pageContainer);
			scrollContainer.appendChild(pageWrapper);
			scrollContainer.appendChild(secondElement);

			const position = getScrollAwarePosition();

			expect(position.x).toBe(20);
			expect(position.y).toBeGreaterThanOrEqual(20);
		});

		it("should handle null getBoundingClientRect values gracefully", () => {
			// Create scroll container
			const scrollContainer = document.createElement("div");
			scrollContainer.className = "overflow-y-auto";
			document.body.appendChild(scrollContainer);

			// Create visible page with mocked getBoundingClientRect
			const pageWrapper = document.createElement("div");
			const pageContainer = document.createElement("div");
			const visiblePage = document.createElement("div");
			visiblePage.className = "border-2 border-blue-500";

			// Mock getBoundingClientRect to return edge case values
			vi.spyOn(visiblePage, "getBoundingClientRect").mockReturnValue({
				top: 0,
				left: 0,
				right: 0,
				bottom: 0,
				width: 0,
				height: 0,
				x: 0,
				y: 0,
				toJSON: () => ({}),
			});

			pageContainer.appendChild(visiblePage);
			pageWrapper.appendChild(pageContainer);
			scrollContainer.appendChild(pageWrapper);

			const position = getScrollAwarePosition();

			expect(position.x).toBe(20);
			expect(position.y).toBeGreaterThanOrEqual(20);
		});

		it("should handle missing parent elements in DOM structure", () => {
			// Create scroll container
			const scrollContainer = document.createElement("div");
			scrollContainer.className = "overflow-y-auto";
			document.body.appendChild(scrollContainer);

			// Create visible page without proper parent structure
			const visiblePage = document.createElement("div");
			visiblePage.className = "border-2 border-blue-500";
			scrollContainer.appendChild(visiblePage);

			const position = getScrollAwarePosition();

			// Should still return valid position even with incomplete DOM structure
			expect(position.x).toBe(20);
			expect(position.y).toBe(20);
		});
	});
});
