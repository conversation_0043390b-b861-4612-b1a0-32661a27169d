import { act, fireEvent, screen } from "@testing-library/react";
import * as React from "react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { renderWithProviders } from "../utils/test-utils";

describe("Concurrent Operations and Race Conditions", () => {
	beforeEach(() => {
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.restoreAllMocks();
		vi.useRealTimers();
	});

	it("should handle state updates correctly", () => {
		// Create a simple counter component
		const CounterComponent = () => {
			const [count, setCount] = React.useState(0);

			const increment = () => {
				setCount((c) => c + 1);
			};

			return (
				<div>
					<div data-testid="count">Count: {count}</div>
					<button type="button" onClick={increment}>
						Increment
					</button>
				</div>
			);
		};

		renderWithProviders(<CounterComponent />);

		// Initial count should be 0
		expect(screen.getByTestId("count").textContent).toBe("Count: 0");

		// Click increment button
		const incrementButton = screen.getByRole("button", { name: /increment/i });
		fireEvent.click(incrementButton);

		// Count should be 1
		expect(screen.getByTestId("count").textContent).toBe("Count: 1");

		// Click increment button again
		fireEvent.click(incrementButton);

		// Count should be 2
		expect(screen.getByTestId("count").textContent).toBe("Count: 2");
	});

	it("should clean up async operations when component unmounts", () => {
		// Create a component with async operations that need cleanup
		const AsyncCleanupComponent = () => {
			const [data, setData] = React.useState(null);
			const isMounted = React.useRef(true);

			React.useEffect(() => {
				// Simulate async data fetching
				const fetchData = async () => {
					// Simulate API call
					setTimeout(() => {
						// Only update state if component is still mounted
						if (isMounted.current) {
							setData(null);
						}
					}, 100);
				};

				fetchData();

				// Cleanup function
				return () => {
					isMounted.current = false;
				};
			}, []);

			return (
				<div>
					{data ? (
						<div data-testid="data">Data: {data}</div>
					) : (
						<div>Loading...</div>
					)}
				</div>
			);
		};

		const { unmount } = renderWithProviders(<AsyncCleanupComponent />);

		// Component should show loading initially
		expect(screen.getByText("Loading...")).toBeInTheDocument();

		// Unmount before data loads
		unmount();

		// Advance timers to trigger the async resolution
		act(() => {
			vi.advanceTimersByTime(200);
		});

		// No errors should occur when the setState is called after unmount
		// This is a negative test - we're verifying that no errors are thrown
	});
});
