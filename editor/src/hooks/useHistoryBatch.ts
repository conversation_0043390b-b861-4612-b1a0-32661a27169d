import { useCallback, useRef } from "react";

export function useHistoryBatch<T>(
	setHistoryState: (state: T, addToHistory?: boolean) => void,
) {
	const batchRef = useRef(false);

	const beginBatch = useCallback(() => {
		if (!batchRef.current) {
			batchRef.current = true;
		}
	}, []);

	const updateDraft = useCallback(
		(newState: T) => {
			// Update state without adding to history
			setHistoryState(newState, false);
		},
		[setHistoryState],
	);

	const commitBatch = useCallback(
		(finalState: T) => {
			if (batchRef.current) {
				// Add the final state to history when committing batch
				setHistoryState(finalState, true);
				batchRef.current = false;
			} else {
				// When not in batch mode, add to history normally
				setHistoryState(finalState, true);
			}
		},
		[setHistoryState],
	);

	return {
		beginBatch,
		updateDraft,
		commitBatch,
		isBatching: () => batchRef.current,
	};
}
