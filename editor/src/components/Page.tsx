import { Fragment, useCallback, useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { KeyboardShortcutsDialog } from "@/components/KeyboardShortcutsDialog";
import type { ResizeHandleType } from "@/hooks/useElementTransform";
import {
	fetchAvailableColors,
	fetchAvailableFonts,
	fetchSessionStatus,
	fetchTemplates,
	saveTemplate,
} from "@/utils/apiService";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import { useApiData } from "../hooks/useApiData";
import { useDocumentLoader } from "../hooks/useDocumentLoader";
// Custom hooks for state management
import { useDocumentState } from "../hooks/useDocumentState";
import { useElementOperations } from "../hooks/useElementOperations";
import { useEventHandlers } from "../hooks/useEventHandlers";
import { useHistory } from "../hooks/useHistory";
import { useHistoryBatch } from "../hooks/useHistoryBatch";
import { usePageOperations } from "../hooks/usePageOperations";
import { useUIState } from "../hooks/useUIState";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import { useSnapGuides } from "../utils/snapGuides";
import { ContextMenu } from "./ContextMenu";
import { CropMarks } from "./CropMarks";
import { EditableElement } from "./elements/EditableElement";
import { FoldMarks } from "./FoldMarks";
import { LoadingStates } from "./LoadingStates";
import { PageMinimap } from "./PageMinimap";
import { PageInsertToolbar } from "./sidebar/PageInsertToolbar";
import { Sidebar } from "./sidebar/Sidebar";
import { TopBar } from "./TopBar";
import { ZoomControls } from "./ZoomControls";

interface EditorState {
	pages: PageSettings[];
	elements: Element[];
}

export function Page() {
	const {
		state: { pages, elements },
		setState: setEditorState,
		undo,
		redo,
		canUndo,
		canRedo,
	} = useHistory<EditorState>({ pages: [], elements: [] });

	const { beginBatch, updateDraft, commitBatch, isBatching } =
		useHistoryBatch<EditorState>(setEditorState);

	// Use custom hooks for state management
	const documentState = useDocumentState();
	const uiState = useUIState();
	const apiData = useApiData();

	// New consolidated state management

	// Page state management

	// Destructure commonly used values for easier access
	const {
		documentId,
		setDocumentId,
		currentVersion,
		setCurrentVersion,
		role,
		setRole,
		isLoading,
		setIsLoading,
		loadError,
		setLoadError,
		isEditorLocked,
		setIsEditorLocked,
		isReleased,
		setIsReleased,
		allowRelease,
		setAllowRelease,
		rawLoadedData,
		setRawLoadedData,
	} = documentState;

	const {
		currentPage,
		setCurrentPage,
		clipboard,
		setClipboard,
		mousePosition,
		setMousePosition,
		activePageId,
		setActivePageId,
		selectedElementId,
		setSelectedElementId,
		isEditing,
		setIsEditing,
		zoomLevel,
		setZoomLevel,
		activeEditor,
		setActiveEditor,
		isTextEditorFocused,
		setIsTextEditorFocused,
		deletedPageNumberIds,
		setDeletedPageNumberIds,
		editingBlockId,
		setEditingBlockId,
		contextMenu,
		setContextMenu,
		highlightVariables,
		setHighlightVariables,
		showElementBorders,
		setShowElementBorders,
		showFoldMarks,
		setShowFoldMarks,
		showPageNumbers,
		setShowPageNumbers,
		selectedTestDataIndex,
		setSelectedTestDataIndex,
		pageRefs,
		scrollContainerRef,
		originalOverflowRef,
		contextMenuRef,
	} = uiState;

	const {
		apiFormats,
		setApiFormats,
		documentVariables,
		setDocumentVariables,
		testData,
		setTestData,
		versionHistory,
		setVersionHistory,
		apiFonts,
		setApiFontsState,
		isLoadingFonts,
		setIsLoadingFonts,
		loadedFonts,
		setLoadedFonts,
		apiColors,
		setApiColors,
		isLoadingColors,
		setIsLoadingColors,
		colorError,
		setColorError,
		templates,
		setTemplates,
		isLoadingTemplates,
		setIsLoadingTemplates,
		templatesError,
		setTemplatesError,
	} = apiData;

	// Ref to store the original right padding so we can restore it later
	const scrollbarPaddingRef = useRef<string | null>(null);

	const {
		snapGuides,
		updateSnapGuidesToState: handleUpdateSnapGuides,
		calculateElementSnapGuides,
	} = useSnapGuides(elements);

	// Element operations hook
	const {
		handleAddElement,
		handleAddElements,
		handleUpdateElement,
		handleDeleteElement,
		handleCopyElement,
		handleCutElement,
		handlePasteAction,
	} = useElementOperations({
		pages,
		elements,
		setEditorState,
		commitBatch,
		beginBatch,
		updateDraft,
		isBatching,
		currentPage,
		editingBlockId,
		selectedElementId,
		setSelectedElementId,
		setEditingBlockId,
		setDeletedPageNumberIds,
		isEditorLocked,
		clipboard,
		setClipboard,
		isEditing,
	});

	// Stable callback for sidebar updates
	const handleSidebarUpdateElement = useCallback(
		(element: Element) => {
			console.log("[Page] handleSidebarUpdateElement called with:", element);
			handleUpdateElement(element, true);
		},
		[handleUpdateElement],
	);

	// Page operations hook
	const {
		handleAddPage,
		handleDeletePage,
		handleReorderPages,
		handleSwapPages,
	} = usePageOperations({
		pages,
		elements,
		setEditorState,
		currentPage,
		setCurrentPage,
		isEditorLocked,
		apiFormats,
	});

	// Document loader hook
	const { handleRefreshVersionHistory } = useDocumentLoader({
		setDocumentId,
		setCurrentVersion,
		setRole,
		setIsLoading,
		setLoadError,
		setIsEditorLocked,
		setIsReleased,
		setAllowRelease,
		setRawLoadedData,
		setShowPageNumbers,
		setDeletedPageNumberIds,
		setApiFormats,
		setDocumentVariables,
		setTestData,
		setVersionHistory,
		setEditorState,
		rawLoadedData,
		apiFormats,
		documentId,
	});

	// Set current page when pages are loaded
	useEffect(() => {
		if (pages.length > 0 && !currentPage) {
			setCurrentPage(pages[0].id);
		}
	}, [pages, currentPage, setCurrentPage]);

	// Event handlers hook
	const {
		handlePageClick,
		handlePageContextMenu,
		handlePageMouseMove,
		handlePageMouseLeave,
		handleContextMenuPaste: contextMenuPasteHandler,
	} = useEventHandlers({
		currentPage,
		clipboard,
		isEditorLocked,
		activePageId,
		mousePosition,
		isEditing,
		isTextEditorFocused,
		activeEditor,
		elements,
		editingBlockId,
		setMousePosition,
		setActivePageId,
		setSelectedElementId,
		setContextMenu,
		setEditingBlockId,
		handlePasteAction,
		undo,
		redo,
	});

	const updateCurrentPageBasedOnVisibility = useCallback(() => {
		let maxVisibility = 0;
		let mostVisiblePageId: string | null = null;
		const scrollContainer = scrollContainerRef.current;

		if (!scrollContainer || pages.length === 0) return;

		const containerRect = scrollContainer.getBoundingClientRect();
		const containerTop = containerRect.top;
		const containerHeight = containerRect.height;

		const debug = false;
		if (debug) {
			console.log(
				"Container top:",
				containerTop,
				"Container height:",
				containerHeight,
			);
		}

		const lastPage = pages[pages.length - 1];
		const lastPageElement = pageRefs.current[lastPage.id];

		if (lastPageElement) {
			const rect = lastPageElement.getBoundingClientRect();
			const visibleTop = Math.max(containerTop, rect.top);
			const visibleBottom = Math.min(
				containerTop + containerHeight,
				rect.bottom,
			);
			const visibleHeight = Math.max(0, visibleBottom - visibleTop);

			if (rect.height > 0 && Math.abs(visibleHeight - rect.height) < 1) {
				if (lastPage.id !== currentPage) {
					setCurrentPage(lastPage.id);
				}
				return;
			}
		}

		pages.forEach((page) => {
			const pageElement = pageRefs.current[page.id];
			if (!pageElement) return;

			const rect = pageElement.getBoundingClientRect();

			const visibleTop = Math.max(containerTop, rect.top);
			const visibleBottom = Math.min(
				containerTop + containerHeight,
				rect.bottom,
			);
			const visibleHeight = Math.max(0, visibleBottom - visibleTop);

			const visibility = rect.height > 0 ? visibleHeight / rect.height : 0;

			if (visibility > maxVisibility) {
				maxVisibility = visibility;
				mostVisiblePageId = page.id;
			}
		});

		if (mostVisiblePageId && mostVisiblePageId !== currentPage) {
			setCurrentPage(mostVisiblePageId);
		}
	}, [scrollContainerRef, pages, pageRefs, currentPage, setCurrentPage]);

	// set the current page based on the scroll position
	useEffect(() => {
		const scrollContainer = scrollContainerRef.current;

		const handleScroll = () => {
			window.requestAnimationFrame(() => {
				updateCurrentPageBasedOnVisibility();
			});
		};

		if (scrollContainer) {
			scrollContainer.addEventListener("scroll", handleScroll);
			updateCurrentPageBasedOnVisibility();

			return () => scrollContainer.removeEventListener("scroll", handleScroll);
		}
	}, [scrollContainerRef, updateCurrentPageBasedOnVisibility]);

	// Recalculate current page when zoom level changes
	useEffect(() => {
		// Wait for the DOM to update after zoom change
		const timeoutId = setTimeout(() => {
			updateCurrentPageBasedOnVisibility();
		}, 100);

		return () => clearTimeout(timeoutId);
	}, [updateCurrentPageBasedOnVisibility]);

	// Keyboard navigation with PageUp / PageDown
	useEffect(() => {
		const handlePgNavigation = (e: KeyboardEvent) => {
			if (isEditing || isTextEditorFocused) return;
			if (!currentPage) return;
			let direction: "prev" | "next" | null = null;
			if (e.key === "PageDown") direction = "next";
			if (e.key === "PageUp") direction = "prev";
			if (!direction) return;

			e.preventDefault();
			const currentIndex = pages.findIndex((p) => p.id === currentPage);
			if (currentIndex === -1) return;

			const targetIndex =
				direction === "next" ? currentIndex + 1 : currentIndex - 1;
			if (targetIndex < 0 || targetIndex >= pages.length) return;

			const targetPage = pages[targetIndex];
			setCurrentPage(targetPage.id);

			// Scroll target page into view
			const el = pageRefs.current[targetPage.id];
			if (el) {
				el.scrollIntoView({ behavior: "smooth", block: "start" });
			}
		};

		window.addEventListener("keydown", handlePgNavigation);
		return () => window.removeEventListener("keydown", handlePgNavigation);
	}, [
		isEditing,
		isTextEditorFocused,
		currentPage,
		pages,
		setCurrentPage,
		pageRefs,
	]);

	// Guarded setEditorState for TopBar (e.g. for import)
	const guardedSetEditorState = (
		newState: EditorState,
		addToHistory?: boolean,
	) => {
		if (isEditorLocked) {
			console.warn("Editor is locked. State change blocked.");
			// Optionally, show a toast message to the user
			return;
		}
		setEditorState(newState, addToHistory);
	};

	// handle saving a block as a template - inspired by copy logic
	const handleSaveAsTemplate = async (
		blockElement: Element,
		childElements: Element[],
	) => {
		try {
			// Create template data in the format expected by saveTemplate
			const templateData = {
				blockElement: {
					...blockElement,
					// Use empty string for currentPageId as it's required but will be set on insertion
					currentPageId: "",
					// Preserve original position for potential exact placement
					x: blockElement.x,
					y: blockElement.y,
				},
				childElements: childElements.map((child) => ({
					...child,
					// Use empty string for currentPageId as it's required but will be set on insertion
					currentPageId: "",
					parentId: blockElement.id,
					// Keep relative positions within the block
					x: child.x,
					y: child.y,
				})),
			};

			// For now, prompt for template name (in a real app, this would be a proper dialog)
			const templateName = prompt("Enter template name:");
			if (!templateName) {
				console.log("Template save cancelled - no name provided");
				return;
			}

			// Save the template using the existing API function
			await saveTemplate(templateName, templateData);
			console.log("Template saved successfully:", templateName);

			// Refresh templates to show the newly saved template
			await handleRefreshTemplates();
		} catch (error) {
			console.error("Failed to save template:", error);
			alert("Failed to save template. Please try again.");
		}
	};

	// Context Menu Paste Handler
	const handleContextMenuPaste = () => {
		contextMenuPasteHandler(contextMenu);
	};

	// Effect to close context menu on outside click
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				contextMenuRef.current &&
				!contextMenuRef.current.contains(event.target as Node)
			) {
				setContextMenu(null);
			}
		};

		if (contextMenu?.visible) {
			document.addEventListener("mousedown", handleClickOutside);
		} else {
			document.removeEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [contextMenu, contextMenuRef, setContextMenu]);

	// Add unsaved changes tracking
	useEffect(() => {
		const handleBeforeUnload = (e: BeforeUnloadEvent) => {
			e.preventDefault();
			// This message might not be shown in modern browsers, they use their own messages
			e.returnValue =
				"You have unsaved changes. Are you sure you want to leave?";
		};

		// Add the event listener if there are elements or more than one page
		if (elements.length > 0 || pages.length > 1) {
			window.addEventListener("beforeunload", handleBeforeUnload);
		}

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
		};
	}, [elements.length, pages.length]);

	// This effect reselects the element when any property changes, including z-index
	// This ensures the sidebar always has the freshest element data
	useEffect(() => {
		if (selectedElementId) {
			// Find the selected element
			const selectedElement = elements.find(
				(el) => el.id === selectedElementId,
			);

			// If found, force the sidebar to use the updated element by reselecting it
			if (selectedElement) {
				// No need to actually change anything - React will detect the
				// dependency array change and update components using selectedElementId
				setSelectedElementId(selectedElementId);
			}
		}
	}, [elements, selectedElementId, setSelectedElementId]);

	const determineWeight = useCallback((style: string | undefined): string => {
		if (!style) return "normal";

		const styleLower = style.toLowerCase();

		if (styleLower.includes("bold")) {
			if (styleLower.includes("semi")) return "600";
			if (styleLower.includes("extra") || styleLower.includes("ultra"))
				return "800";
			if (styleLower.includes("black")) return "900";
			if (styleLower.includes("light")) return "300";
			return "700"; // Regular Bold
		}

		if (styleLower.includes("light")) return "300";
		if (styleLower.includes("medium")) return "500";
		if (styleLower.includes("black")) return "900";
		if (styleLower.includes("thin") || styleLower.includes("hairline"))
			return "100";

		return "normal"; // Default weight
	}, []);

	useEffect(() => {
		let cleanupFunction: (() => void) | undefined;

		const getFonts = async () => {
			setIsLoadingFonts(true);
			try {
				console.log("Fetching fonts from API...");
				const fontsFromApi = await fetchAvailableFonts();
				console.log("API response:", fontsFromApi);

				// Process API response to ensure all fields are valid
				const processedFonts = fontsFromApi.map((font) => {
					const family =
						font.family ||
						font.name ||
						`Font${Math.random().toString(36).substr(2, 5)}`;
					return {
						...font,
						family: family.replace(/\s+/g, ""), // Remove spaces from family name for CSS
					};
				});

				setApiFontsState(processedFonts);

				// Pre-load fonts with valid paths
				const fontsToLoad = processedFonts.filter((font) => font.path);
				console.log(
					`Found ${fontsToLoad.length} fonts with paths to load:`,
					fontsToLoad,
				);

				if (fontsToLoad.length > 0) {
					const styleElement = document.createElement("style");
					styleElement.id = "dynamic-font-styles"; // Add an ID for potential cleanup
					document.head.appendChild(styleElement);

					let fontFaceRules = "";
					const newLoadedFonts = new Set<string>();

					fontsToLoad.forEach((font) => {
						if (!font.path) return;

						let fontUrl = font.path;
						if (!fontUrl.startsWith("http") && !fontUrl.startsWith("/")) {
							fontUrl = `/${fontUrl}`;
						}

						const fontStyle = font.style?.toLowerCase().includes("italic")
							? "italic"
							: "normal";
						const fontWeight = determineWeight(font.style);

						console.log(
							`Creating @font-face for: ${font.name}, family: ${font.family}, style: ${fontStyle}, weight: ${fontWeight}`,
						);

						fontFaceRules += `
                        @font-face {
                            font-family: '${font.family}';
                            src: url('${fontUrl}') format('truetype');
                            font-weight: ${fontWeight};
                            font-style: ${fontStyle};
                            font-display: swap;
                        }`;

						newLoadedFonts.add(font.family);
					});

					styleElement.textContent = fontFaceRules;
					console.log("Added @font-face rules:", fontFaceRules);
					setLoadedFonts((prev) => new Set([...prev, ...newLoadedFonts]));

					// Create sample elements to force browser to load each font
					const div = document.createElement("div");
					div.style.position = "absolute";
					div.style.visibility = "hidden";
					div.style.pointerEvents = "none";
					div.style.height = "0";
					div.style.overflow = "hidden";
					document.body.appendChild(div);

					fontsToLoad.forEach((font) => {
						const span = document.createElement("span");
						span.style.fontFamily = `'${font.family}', sans-serif`;
						span.textContent = "abc"; // Shorter text is sufficient
						div.appendChild(span);
					});

					cleanupFunction = () => {
						if (div?.parentNode) {
							div.parentNode.removeChild(div);
						}
						const existingStyleElement = document.getElementById(
							"dynamic-font-styles",
						);
						if (existingStyleElement?.parentNode) {
							existingStyleElement.parentNode.removeChild(existingStyleElement);
						}
					};
				}
			} catch (error) {
				console.error("Failed to fetch or process fonts:", error);
			} finally {
				setIsLoadingFonts(false);
			}
		};

		getFonts();

		return () => {
			if (cleanupFunction) {
				cleanupFunction();
			}
		};
	}, [determineWeight, setApiFontsState, setIsLoadingFonts, setLoadedFonts]);

	// Handler for changing document version
	const handleVersionChange = (newVersion: string) => {
		if (documentId) {
			const params = new URLSearchParams(window.location.search);
			params.set("version", newVersion);
			window.location.search = params.toString(); // This will trigger a page reload
		}
	};

	// Get the readable format name from the format ID
	const getFormatDescription = (formatId: string): string => {
		if (apiFormats && apiFormats.length > 0) {
			const format = apiFormats.find((f) => f.name === formatId);
			if (format) {
				return format.description;
			}
		}
		// If format not found in API formats, create a readable name
		return formatId.replace("_", " ").replace(/\b\w/g, (c) => c.toUpperCase());
	};

	// Use the extracted snap guides calculation function
	const calculateSnapGuidesForElement = (
		elementId: string,
		newX: number,
		newY: number,
		elementWidth: number,
		elementHeight: number,
		parentBlockId?: string | null,
		movingHandle?: ResizeHandleType,
	) => {
		return calculateElementSnapGuides({
			elementId,
			newX,
			newY,
			elementWidth,
			elementHeight,
			parentBlockId,
			movingHandle,
			snapThreshold: 1,
		});
	};

	// Update the renderElements function to pass calculateSnapGuides to EditableElement
	const renderElements = (pageId: string) => {
		// Get top-level elements for the given page (elements that are not children of a block)
		const topLevelElementsOnPage = elements.filter(
			(element) => element.currentPageId === pageId && !element.parentId,
		);

		// Sort elements by z-index and preserve original order for elements with same z-index
		const indexedElements = topLevelElementsOnPage.map((element, index) => ({
			element,
			originalIndex: index,
		}));

		indexedElements.sort((a, b) => {
			const zIndexA = a.element.zIndex ?? 0;
			const zIndexB = b.element.zIndex ?? 0;
			if (zIndexA !== zIndexB) {
				return zIndexA - zIndexB;
			}
			return a.originalIndex - b.originalIndex;
		});

		return indexedElements.map(({ element }) => {
			// If the element is a block, it will be responsible for rendering its children.
			// We'll pass all elements to EditableElement and let it handle child rendering if it's a block.
			const childElements =
				element.type === "block" && element.childElementIds
					? elements.filter((el) => element.childElementIds?.includes(el.id))
					: [];

			return (
				<EditableElement
					key={element.id}
					element={element}
					onUpdate={handleUpdateElement}
					onDelete={handleDeleteElement}
					onCopy={handleCopyElement}
					onCut={handleCutElement}
					onSaveAsTemplate={handleSaveAsTemplate}
					isSelected={element.id === selectedElementId}
					onSelect={(id) => {
						setSelectedElementId(id);
						setIsEditing(false);
					}}
					setActiveEditor={setActiveEditor}
					onEditingChange={setIsEditing}
					testData={testData}
					selectedTestDataIndex={selectedTestDataIndex}
					highlightVariables={highlightVariables}
					showElementBorders={showElementBorders}
					updateSnapGuides={handleUpdateSnapGuides}
					isLocked={isEditorLocked}
					childElements={childElements}
					editingBlockId={editingBlockId}
					onEnterBlockEditMode={setEditingBlockId}
					globalSelectedElementId={selectedElementId}
					calculateSnapGuides={calculateSnapGuidesForElement}
					setIsTextEditorFocused={setIsTextEditorFocused}
				/>
			);
		});
	};

	// Function to toggle page numbers
	const handleTogglePageNumbers = (show: boolean) => {
		if (show) {
			// When turning page numbers on globally, reset individual deletions
			setDeletedPageNumberIds(new Set());
		}
		setShowPageNumbers(show);
	};

	// Effect to automatically update page numbers when pages change or toggle is active
	useEffect(() => {
		if (!showPageNumbers) {
			// If page numbers are turned off, ensure all are removed
			const nonPageNumberElements = elements.filter((el) => !el.isPageNumber);
			if (nonPageNumberElements.length !== elements.length) {
				setEditorState({ pages, elements: nonPageNumberElements }, true);
			}
			return;
		}

		// If page numbers are on, update/create them

		let actualPageNumber = 0;
		const workingElements = [...elements]; // Create a mutable copy to work with
		const requiredPageNumberElementIds = new Set<string>();

		pages.forEach((page) => {
			const pageId = page.id;
			const pageNumberElementId = `page-number-${pageId}`;
			requiredPageNumberElementIds.add(pageNumberElementId);

			// Check if this specific page number has been individually deleted
			if (deletedPageNumberIds.has(pageId)) {
				// Ensure it's removed if it exists (e.g., if it was re-added by a different logic path)
				const existingElementIndex = workingElements.findIndex(
					(el) => el.id === pageNumberElementId,
				);
				if (existingElementIndex !== -1) {
					workingElements.splice(existingElementIndex, 1);
				}
				return; // Skip creating/updating this page number
			}

			const hasCoords =
				typeof page.pagenumber_x === "number" &&
				typeof page.pagenumber_y === "number";
			if (!hasCoords) {
				// This page should not have a page number, remove if exists
				const existingElementIndex = workingElements.findIndex(
					(el) => el.id === pageNumberElementId,
				);
				if (existingElementIndex !== -1) {
					workingElements.splice(existingElementIndex, 1);
				}
				return; // Skip to next page
			}

			actualPageNumber++;
			const newContent = `<p style="font-size: 10pt; text-align: right;">${actualPageNumber}</p>`;
			const existingElementIndex = workingElements.findIndex(
				(el) => el.id === pageNumberElementId,
			);

			if (existingElementIndex !== -1) {
				// Element exists, update its content if different
				const currentElement = workingElements[existingElementIndex];
				if (
					currentElement.content !== newContent ||
					!currentElement.isPageNumber
				) {
					// also ensure isPageNumber flag is set
					workingElements[existingElementIndex] = {
						...currentElement,
						// Content is no longer forced here to allow user edits
						// content: newContent,
						isPageNumber: true, // Ensure flag is true
						// Position (x, y), dimensions (width, height), rotation, etc., are preserved
					};
				}
			} else {
				// Element doesn't exist, create it with default properties
				const xPos = (page.pagenumber_x ?? 0) * 10; // cm to mm
				const yPos = (page.pagenumber_y ?? 0) * 10; // cm to mm
				const elementWidthMm = 20;
				const elementHeightMm = 5;

				// Determine the highest z-index on the current page for the new element
				const elementsOnThisPage = workingElements.filter(
					(el) => el.currentPageId === pageId,
				);
				const highestZIndexOnPage = elementsOnThisPage.reduce((maxZ, el) => {
					return Math.max(maxZ, el.zIndex !== undefined ? el.zIndex : 0); // Default to 0 if undefined
				}, 0);

				const newElement: Element = {
					id: pageNumberElementId,
					type: "text",
					content: newContent,
					x: roundToTwoDecimals(xPos),
					y: roundToTwoDecimals(yPos),
					width: elementWidthMm,
					height: elementHeightMm,
					rotation: 0,
					color: "#071E7F",
					backgroundColor: "transparent",
					zIndex: highestZIndexOnPage + 1,
					currentPageId: pageId,
					isPageNumber: true,
					verticalAlign: "middle",
				};
				workingElements.push(newElement);
			}
		});

		// Filter out any stale page number elements (e.g., from deleted pages or pages that no longer need numbers)
		const finalElements = workingElements.filter((el) => {
			if (el.isPageNumber) {
				return requiredPageNumberElementIds.has(el.id);
			}
			return true; // Keep all non-page-number elements
		});

		// Only update state if elements actually changed to avoid unnecessary history entries/re-renders
		if (JSON.stringify(elements) !== JSON.stringify(finalElements)) {
			console.log("Updating state with new page numbers.");
			setEditorState({ pages, elements: finalElements }, true);
		}
	}, [showPageNumbers, elements, setEditorState, deletedPageNumberIds, pages]);

	// Effect to disable/enable document scrolling when editing a block
	useEffect(() => {
		const scrollContainer = scrollContainerRef.current;
		if (scrollContainer) {
			if (editingBlockId !== null) {
				// Store original styles if not already stored
				if (!originalOverflowRef.current) {
					originalOverflowRef.current = {
						x: scrollContainer.style.overflowX || "auto", // Default to auto if not set inline
						y: scrollContainer.style.overflowY || "auto",
					};
				}

				// Store original padding-right so we can restore it later
				if (scrollbarPaddingRef.current === null) {
					const computedStyle = window.getComputedStyle(scrollContainer);
					scrollbarPaddingRef.current = computedStyle.paddingRight;

					// Calculate the scrollbar width (before hiding it)
					const scrollbarWidth =
						scrollContainer.offsetWidth - scrollContainer.clientWidth;

					// Apply compensation padding to prevent layout shift when scrollbar disappears
					if (scrollbarWidth > 0) {
						// Preserve any existing right padding (e.g., space reserved for minimap) and add the scrollbar width
						const existingPadding = parseFloat(computedStyle.paddingRight) || 0;
						scrollContainer.style.paddingRight = `${existingPadding + scrollbarWidth}px`;
					}
				}
				// Disable scrolling
				scrollContainer.style.overflowX = "hidden";
				scrollContainer.style.overflowY = "hidden";
			} else {
				// Re-enable scrolling by restoring original styles or defaulting to auto
				scrollContainer.style.overflowX =
					originalOverflowRef.current?.x || "auto";
				scrollContainer.style.overflowY =
					originalOverflowRef.current?.y || "auto";

				// Restore original padding-right
				if (scrollbarPaddingRef.current !== null) {
					scrollContainer.style.paddingRight = scrollbarPaddingRef.current;
					scrollbarPaddingRef.current = null;
				}
				// Clear stored original styles once restored
				originalOverflowRef.current = null;
			}
		}
	}, [editingBlockId, originalOverflowRef, scrollContainerRef]);

	// Fetch colors from API
	useEffect(() => {
		const getColors = async () => {
			setIsLoadingColors(true);
			setColorError(null);
			try {
				console.log("Fetching colors from API...");
				const colors = await fetchAvailableColors();
				setApiColors(colors);
				console.log("Colors loaded:", colors.length);
			} catch (error) {
				console.error("Failed to fetch colors:", error);
				setColorError(
					"Farben konnten nicht geladen werden. Bitte versuchen Sie es später erneut.",
				);
			} finally {
				setIsLoadingColors(false);
			}
		};

		getColors();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [setApiColors, setColorError, setIsLoadingColors]); // Empty dependency array ensures this runs only once at page load

	// Function to refresh templates from API
	const handleRefreshTemplates = async () => {
		try {
			setIsLoadingTemplates(true);
			setTemplatesError(null);
			const fetchedTemplates = await fetchTemplates();
			setTemplates(fetchedTemplates);
			console.log("Templates refreshed:", fetchedTemplates.length);
		} catch (error) {
			console.error("Error refreshing templates:", error);
			setTemplatesError("Fehler beim Laden der Vorlagen");
		} finally {
			setIsLoadingTemplates(false);
		}
	};

	// Add an effect to automatically hide borders when document is released
	useEffect(() => {
		if (isReleased) {
			setShowElementBorders(false);
			setShowFoldMarks(false);
		}
	}, [isReleased, setShowElementBorders, setShowFoldMarks]);

	// Function to handle template insertion - inspired by copy/paste logic
	const handleInsertTemplate = (
		templateData: { blockElement: Element; childElements: Element[] },
		insertX?: number,
		insertY?: number,
	) => {
		if (!currentPage) {
			console.warn("No current page selected for template insertion");
			return;
		}

		// Helper function to check if element would be sufficiently visible at given position
		const isElementSufficientlyVisible = (
			x: number,
			y: number,
			width: number,
			height: number,
			pageId: string,
		): boolean => {
			const targetPage = pages.find((p) => p.id === pageId);
			if (!targetPage) return false;

			// Get page dimensions in mm
			const pageWidthMm = targetPage.width * 10; // cm to mm
			const pageHeightMm = targetPage.height * 10; // cm to mm

			// Calculate element bounds
			const elementRight = x + width;
			const elementBottom = y + height;

			// Calculate visible area (intersection with page bounds)
			const visibleLeft = Math.max(0, x);
			const visibleTop = Math.max(0, y);
			const visibleRight = Math.min(pageWidthMm, elementRight);
			const visibleBottom = Math.min(pageHeightMm, elementBottom);

			// Calculate visible dimensions
			const visibleWidth = Math.max(0, visibleRight - visibleLeft);
			const visibleHeight = Math.max(0, visibleBottom - visibleTop);
			const visibleArea = visibleWidth * visibleHeight;

			// Calculate total element area
			const totalArea = width * height;

			// Check if more than 50% is visible
			return totalArea > 0 && visibleArea / totalArea > 0.5;
		};

		// Determine target position
		let targetX: number;
		let targetY: number;

		if (insertX !== undefined && insertY !== undefined) {
			// Use explicitly provided coordinates
			targetX = insertX;
			targetY = insertY;
		} else {
			// Try to use original template position if it would be sufficiently visible
			const originalX = templateData.blockElement.x;
			const originalY = templateData.blockElement.y;
			const elementWidth = templateData.blockElement.width;
			const elementHeight = templateData.blockElement.height;

			if (
				isElementSufficientlyVisible(
					originalX,
					originalY,
					elementWidth,
					elementHeight,
					currentPage,
				)
			) {
				// Use original position
				targetX = originalX;
				targetY = originalY;
			} else {
				// Fall back to mouse position with offset
				targetX = mousePosition.x + 5;
				targetY = mousePosition.y + 5;
			}
		}

		try {
			// Check if template data has the structured format (blockElement + childElements)
			const isStructuredTemplate =
				templateData.blockElement && templateData.childElements;

			if (isStructuredTemplate) {
				// Handle structured template (blockElement with childElements) - similar to block paste
				const { blockElement: originalBlock, childElements: originalChildren } =
					templateData;

				const newBlockId = uuidv4();
				const newBlock: Element = {
					...originalBlock,
					id: newBlockId,
					x: targetX,
					y: targetY,
					currentPageId: currentPage,
					childElementIds: [],
				};

				const newChildren: Element[] = [];
				const newChildIds: string[] = [];

				originalChildren.forEach((child: Element) => {
					const newChildId = uuidv4();
					newChildren.push({
						...child,
						id: newChildId,
						parentId: newBlockId,
						currentPageId: currentPage,
					});
					newChildIds.push(newChildId);
				});

				newBlock.childElementIds = newChildIds;

				// Calculate proper z-index for the new block
				const elementsOnCurrentPage = elements.filter(
					(el) => el.currentPageId === currentPage && !el.parentId,
				);
				const highestZIndex = elementsOnCurrentPage.reduce((max, el) => {
					return Math.max(max, el.zIndex !== undefined ? el.zIndex : 0);
				}, 1);

				newBlock.zIndex = highestZIndex + 1;

				guardedSetEditorState(
					{
						pages,
						elements: [...elements, newBlock, ...newChildren],
					},
					true,
				);

				setSelectedElementId(newBlock.id);
			} else {
				console.warn("Template data format not recognized:", templateData);
			}
		} catch (error) {
			console.error("Failed to insert template:", error);
		}
	};

	// Keep the viewport centered when zooming
	const handleZoomChange = useCallback(
		(newZoom: number) => {
			const container = scrollContainerRef.current;
			if (container) {
				const { width: viewportWidth, height: viewportHeight } =
					container.getBoundingClientRect();
				const centerX = container.scrollLeft + viewportWidth / 2;
				const centerY = container.scrollTop + viewportHeight / 2;

				const ratio = newZoom / zoomLevel;
				const newCenterX = centerX * ratio;
				const newCenterY = centerY * ratio;

				const newScrollLeft = newCenterX - viewportWidth / 2;
				const newScrollTop = newCenterY - viewportHeight / 2;

				// Update zoom first so the content scales accordingly
				setZoomLevel(newZoom);

				// Clamp the scroll positions to valid ranges after the next paint
				requestAnimationFrame(() => {
					const maxScrollLeft = container.scrollWidth - viewportWidth;
					const maxScrollTop = container.scrollHeight - viewportHeight;

					container.scrollLeft = Math.max(
						0,
						Math.min(newScrollLeft, maxScrollLeft),
					);
					container.scrollTop = Math.max(
						0,
						Math.min(newScrollTop, maxScrollTop),
					);
				});
			} else {
				setZoomLevel(newZoom);
			}
		},
		[scrollContainerRef, zoomLevel, setZoomLevel],
	);

	// SESSION STATUS MONITORING ---------------------------------------------------
	type SessionState = "ok" | "expired" | "connection-lost";
	const [sessionStatus, setSessionStatus] = useState<SessionState>("ok");
	const failureStartRef = useRef<number | null>(null);

	useEffect(() => {
		let isMounted = true;

		const checkSession = async () => {
			try {
				const status = await fetchSessionStatus();
				if (!isMounted) return;

				if (status.session) {
					// Session valid – reset failures and hide overlays
					setSessionStatus("ok");
					failureStartRef.current = null;
				} else {
					// Session expired – show overlay immediately
					setSessionStatus("expired");
				}
			} catch (error) {
				console.error("Session status check failed", error);
				// Network or server unreachable
				if (failureStartRef.current === null) {
					failureStartRef.current = Date.now();
				}

				// If failures persist for 5 minutes, show connection lost overlay
				const elapsed = Date.now() - (failureStartRef.current ?? 0);
				if (elapsed >= 1 * 60 * 1000) {
					setSessionStatus("connection-lost");
				}
			}
		};

		// Initial check
		checkSession();

		// Check every minute
		const intervalId = setInterval(checkSession, 60 * 1000);

		return () => {
			isMounted = false;
			clearInterval(intervalId);
		};
	}, []);

	// Show loading states if needed
	if (isLoading || loadError) {
		return <LoadingStates isLoading={isLoading} loadError={loadError} />;
	}

	return (
		<div className="flex flex-col min-h-screen bg-gray-100">
			{/* SESSION STATUS OVERLAY */}
			{sessionStatus !== "ok" && (
				<div className="fixed inset-0 z-[10000] bg-black bg-opacity-60 flex items-center justify-center">
					<div className="bg-white rounded-lg shadow-lg p-8 max-w-md text-center text-lg font-semibold">
						{sessionStatus === "expired"
							? "Ihre Sitzung ist abgelaufen. Bitte laden Sie die Seite neu."
							: "Verbindung zum Server verloren. Wiederholungsversuch läuft …"}
					</div>
				</div>
			)}
			<TopBar
				documentId={documentId}
				pages={pages}
				elements={elements}
				onUndo={undo}
				onRedo={redo}
				canUndo={canUndo}
				canRedo={canRedo}
				highlightVariables={highlightVariables}
				setHighlightVariables={setHighlightVariables}
				showPageNumbers={showPageNumbers}
				deletedPageNumberIds={deletedPageNumberIds}
				testData={testData}
				selectedTestDataIndex={selectedTestDataIndex}
				setSelectedTestDataIndex={setSelectedTestDataIndex}
				setEditorState={guardedSetEditorState}
				isEditorLocked={isEditorLocked}
				setIsEditorLocked={setIsEditorLocked}
				isReleased={isReleased}
				setIsReleased={setIsReleased}
				role={role}
				allowRelease={allowRelease}
				apiFormats={apiFormats}
				activeEditor={activeEditor}
				isTextEditorFocused={isTextEditorFocused}
			/>
			<div className="flex flex-1 overflow-hidden">
				{!isReleased && (
					<Sidebar
						pages={pages}
						elements={elements}
						onAddPage={handleAddPage}
						onAddElement={handleAddElement}
						onAddElements={handleAddElements}
						onUpdateElement={handleSidebarUpdateElement}
						editor={activeEditor}
						currentPageId={currentPage || undefined}
						selectedElement={
							selectedElementId
								? elements.find((el) => el.id === selectedElementId) || null
								: null
						}
						isEditing={isEditing}
						currentPageFormat={
							currentPage
								? pages.find((p) => p.id === currentPage)?.format
								: undefined
						}
						apiFormats={apiFormats}
						documentVariables={documentVariables}
						determineWeight={determineWeight}
						apiFonts={apiFonts}
						isLoadingFonts={isLoadingFonts}
						loadedFonts={loadedFonts}
						showPageNumbers={showPageNumbers}
						onTogglePageNumbers={handleTogglePageNumbers}
						showElementBorders={showElementBorders}
						setShowElementBorders={setShowElementBorders}
						showFoldMarks={showFoldMarks}
						setShowFoldMarks={setShowFoldMarks}
						versionHistory={versionHistory}
						currentVersion={currentVersion}
						onVersionChange={handleVersionChange}
						onRefreshHistory={handleRefreshVersionHistory}
						isBlockEditingActive={editingBlockId !== null}
						apiColors={apiColors}
						isLoadingColors={isLoadingColors}
						colorError={colorError}
						role={role}
						isReleased={isReleased}
						templates={templates}
						isLoadingTemplates={isLoadingTemplates}
						templatesError={templatesError}
						onInsertTemplate={handleInsertTemplate}
						onRefreshTemplates={handleRefreshTemplates}
					/>
				)}
				<div
					ref={scrollContainerRef}
					className="flex-1 overflow-y-auto overflow-x-auto h-[calc(100vh-96px)] relative pr-48"
				>
					{isReleased && (
						<div className="sticky top-0 z-10 bg-yellow-100 border-b border-yellow-300 text-yellow-800 p-3 text-center text-sm -mr-48 pr-48">
							Dieses Dokument kann nicht bearbeitet werden, da es bereits
							freigegeben ist.
						</div>
					)}

					<div
						className="p-8 space-y-8 min-w-fit mx-auto"
						style={{
							transform: `scale(${zoomLevel})`,
							transformOrigin: "top center",
							padding: `${8 * zoomLevel}px`,
							marginBottom: `${50 * zoomLevel}px`,
							pointerEvents: isEditorLocked ? "none" : "auto",
							// Lift the entire page content container above the overlay when editing a block
							position: "relative", // Ensures z-index is applied
							zIndex: editingBlockId ? 5001 : "auto",
						}}
						onLoad={() => console.log("Rendering decision - pages:", pages)}
					>
						{pages.length === 0 ? (
							<div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
								<div className="text-gray-500 text-xl mb-4">
									Keine Seite vorhanden. Bitte füge eine neue Seite hinzu.
								</div>
								<PageInsertToolbar
									onAddPage={(format) => handleAddPage(format)}
									apiFormats={apiFormats}
								/>
							</div>
						) : (
							// Use a fragment to wrap both the pages and the insert toolbars
							pages.map((page, index) => {
								// Determine if this is a front or back page
								const isFrontPage = index % 2 === 0;
								const isBackPage = !isFrontPage;
								const isLastPageOfPhysicalPair =
									isBackPage || index === pages.length - 1;

								// Calculate outer dimensions including trim (in cm)
								const trimLeftCm = page.trim_left ?? 0;
								const trimRightCm = page.trim_right ?? 0;
								const trimTopCm = page.trim_top ?? 0;
								const trimBottomCm = page.trim_bottom ?? 0;

								const outerWidthCm = page.width + trimLeftCm + trimRightCm;
								const outerHeightCm = page.height + trimTopCm + trimBottomCm;

								return (
									<Fragment key={page.id}>
										<div className="flex flex-col items-center justify-center min-w-fit mb-8">
											{/* Paper Format and Page Type (Top Left) - Use outerWidth for alignment */}
											<div
												className="self-start text-gray-500 font-medium mb-1 flex items-center justify-between pr-4"
												style={{
													width: `${outerWidthCm}cm`,
													marginLeft: "auto",
													marginRight: "auto",
												}}
											>
												<div>
													{getFormatDescription(page.format)} (
													{isFrontPage ? "Vorderseite" : "Rückseite"})
												</div>
											</div>

											{/* Main Page Content */}
											{/* biome-ignore lint/a11y/useSemanticElements: Complex layout element with specific styling requirements */}
											<div
												ref={(el) => {
													pageRefs.current[page.id] = el;
												}}
												className="relative mx-auto bg-white shadow-lg overflow-hidden"
												style={{
													width: `${outerWidthCm}cm`, // Use outer width
													height: `${outerHeightCm}cm`, // Use outer height
													minWidth: `${outerWidthCm}cm`,
													flexShrink: 0,
												}}
												role="button"
												tabIndex={0}
												aria-label={`Page ${index + 1}: ${getFormatDescription(page.format)}`}
												data-page-id={page.id}
												onContextMenu={(e) => handlePageContextMenu(e, page)}
												onMouseMove={(e) => handlePageMouseMove(e, page)}
												onMouseLeave={handlePageMouseLeave}
												onClick={handlePageClick}
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														e.preventDefault();
														handlePageClick(
															e as unknown as React.MouseEvent<HTMLDivElement>,
														);
													}
												}}
											>
												{currentPage === page.id && (
													// Blue border for the entire outer container
													<div className="absolute inset-0 border-2 border-blue-500 pointer-events-none z-[9999]" />
												)}

												{/* Position the snap guides container */}
												{/* Check if guides are visible and relate to the current page OR a block on the current page */}
												{snapGuides.visible &&
													(currentPage === page.id ||
														elements.find(
															(el) => el.id === snapGuides.relativeToBlockId,
														)?.currentPageId === page.id) && (
														<div
															className="absolute pointer-events-none"
															style={
																snapGuides.relativeToBlockId
																	? {
																			// Guides are relative to a block
																			// Position the container using block's page coordinates PLUS page trim
																			top: `${(trimTopCm * 10 + (snapGuides.blockY ?? 0)) / 10}cm`,
																			left: `${(trimLeftCm * 10 + (snapGuides.blockX ?? 0)) / 10}cm`,
																			width: `${(snapGuides.blockWidth ?? 0) / 10}cm`,
																			height: `${(snapGuides.blockHeight ?? 0) / 10}cm`,
																			zIndex: 9999,
																		}
																	: {
																			// Guides are relative to the page
																			top: `${trimTopCm}cm`,
																			left: `${trimLeftCm}cm`,
																			width: `${page.width}cm`,
																			height: `${page.height}cm`,
																			zIndex: 9999,
																		}
															}
														>
															{snapGuides.guides.map((guide) => {
																const isHorizontal =
																	guide.type === "horizontal";
																const style: React.CSSProperties = {
																	position: "absolute",
																	backgroundColor: "#3b82f6", // Blue guideline
																	pointerEvents: "none",
																};

																// Guide positions/extents are in mm, relative to their container (block or page content area)
																if (isHorizontal) {
																	style.height = "1px";
																	style.left = `${guide.start}mm`; // Use start/end for extent
																	style.width = `${guide.end - guide.start}mm`;
																	style.top = `${guide.position}mm`;
																} else {
																	style.width = "1px";
																	style.top = `${guide.start}mm`; // Use start/end for extent
																	style.height = `${guide.end - guide.start}mm`;
																	style.left = `${guide.position}mm`;
																}

																return (
																	<div
																		key={`guide-${guide.type}-${guide.position}-${guide.start}-${guide.end}`}
																		className={`snap-guide ${isHorizontal ? "horizontal" : "vertical"}`}
																		style={style}
																	/>
																);
															})}
														</div>
													)}

												{/* Fold Marks */}
												<FoldMarks
													pageWidth={page.width}
													pageHeight={page.height}
													trimLeft={trimLeftCm}
													trimTop={trimTopCm}
													outerWidth={outerWidthCm}
													outerHeight={outerHeightCm}
													pageFormat={apiFormats.find(
														(f) => f.name === page.format,
													)}
													showFullLines={showFoldMarks}
												/>

												{/* Crop Marks */}
												<CropMarks
													pageWidth={page.width}
													pageHeight={page.height}
													trimLeft={trimLeftCm}
													trimRight={trimRightCm}
													trimTop={trimTopCm}
													trimBottom={trimBottomCm}
												/>

												<div
													// Inner Content Area - Represents the actual page boundary
													className="absolute"
													style={{
														top: `${trimTopCm}cm`,
														left: `${trimLeftCm}cm`,
														width: `${page.width}cm`,
														height: `${page.height}cm`,
														// Background color inherits from outer container (white)
													}}
												>
													{renderElements(page.id)}
												</div>
											</div>

											{/* Page Number and Delete Button (Bottom Right) - Use outerWidth for alignment */}
											<div
												className="text-gray-500 font-medium mt-1 flex items-center justify-between pr-4"
												style={{
													width: `${outerWidthCm}cm`,
													marginLeft: "auto",
													marginRight: "auto",
												}}
											>
												<div>
													Seite {index + 1} (
													{isFrontPage ? "Vorderseite" : "Rückseite"})
												</div>
												{!isReleased && (
													<button
														type="button"
														className="text-red-500 hover:text-red-700 focus:outline-none"
														onClick={(e) => {
															e.stopPropagation(); // Prevent event bubbling
															console.log(
																"Delete button clicked for page:",
																page.id,
															);
															handleDeletePage(page.id);
														}}
														title="Seite löschen"
													>
														<svg
															xmlns="http://www.w3.org/2000/svg"
															className="h-5 w-5"
															fill="none"
															viewBox="0 0 24 24"
															stroke="currentColor"
															aria-label="Seite löschen"
														>
															<title>Seite löschen</title>
															<path
																strokeLinecap="round"
																strokeLinejoin="round"
																strokeWidth={2}
																d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
															/>
														</svg>
													</button>
												)}
											</div>
										</div>

										{/* Add PageInsertToolbar after every physical page (a back page or a front page without matching back) */}
										{isLastPageOfPhysicalPair &&
											index < pages.length - 1 &&
											!isReleased && (
												<PageInsertToolbar
													onAddPage={(format) =>
														handleAddPage(format, index + 1)
													}
													apiFormats={apiFormats}
												/>
											)}

										{/* Add PageInsertToolbar after the very last page */}
										{index === pages.length - 1 && !isReleased && (
											<PageInsertToolbar
												onAddPage={(format) =>
													handleAddPage(format, pages.length)
												}
												apiFormats={apiFormats}
											/>
										)}
									</Fragment>
								);
							})
						)}
					</div>
				</div>
			</div>
			{/* Keyboard shortcuts button – positioned above zoom controls */}
			<div className="fixed bottom-20 right-4 z-10">
				<KeyboardShortcutsDialog />
			</div>
			{/* Zoom Controls */}
			<ZoomControls
				zoomLevel={zoomLevel}
				onZoomChange={handleZoomChange}
				className="fixed bottom-4 right-4 bg-white rounded-lg shadow-md p-3 z-10"
			/>
			{/* Context Menu */}
			{!isEditing && (
				<ContextMenu
					ref={contextMenuRef}
					contextMenu={contextMenu}
					hasClipboard={clipboard !== null}
					onPaste={handleContextMenuPaste}
					onClose={() => setContextMenu(null)}
				/>
			)}

			{/* Page Minimap */}
			{pages.length > 0 && (
				<PageMinimap
					pages={pages}
					elements={elements}
					currentPageId={currentPage}
					onPageSelect={setCurrentPage}
					onPageReorder={handleReorderPages}
					onPageSwap={handleSwapPages}
					scrollContainerRef={scrollContainerRef}
					isEditorLocked={isEditorLocked}
				/>
			)}
		</div>
	);
}
