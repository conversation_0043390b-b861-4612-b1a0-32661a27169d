import type { TestDataRecord } from "./apiService";

interface ContentProcessingOptions {
	content: string;
	isEditing?: boolean;
	highlightVariables?: boolean;
	selectedTestDataIndex?: number;
	testData?: TestDataRecord[];
}

/**
 * Processes content to handle variable highlighting and test data substitution
 * Used by both text elements and table cells
 */
export function processContentWithVariables({
	content,
	isEditing = false,
	highlightVariables = false,
	selectedTestDataIndex = -1,
	testData = [],
}: ContentProcessingOptions): string {
	let finalContent = content || "";

	// Decode common double-escaped entities to their functional single-escaped form
	finalContent = finalContent
		.replace(/&amp;nbsp;/g, "&nbsp;")
		.replace(/&amp;shy;/g, "&shy;");

	// Ensure all previous highlighting spans are removed first
	finalContent = finalContent.replace(
		/<span class="variable-highlight">(.*?)<\/span>/g,
		"$1",
	);

	// Don't process variables if editing
	if (isEditing) {
		return finalContent;
	}
	const variablePattern = /{{([^}\s]+)}}/g;

	// 1. Apply Highlighting First (if enabled)
	if (highlightVariables) {
		finalContent = finalContent.replace(variablePattern, (match) => {
			return `<span class="variable-highlight">${match}</span>`;
		});
	}

	// 2. Apply Test Data Substitution (if applicable)
	if (selectedTestDataIndex !== -1) {
		// Regex to find variables, potentially inside highlight spans
		const substitutionPattern =
			/(<span class="variable-highlight">)?{{([^}\s]+)}}(<\/span>)? ?/g;

		if (testData.length >= 1) {
			const actualTestDataIndex = selectedTestDataIndex;

			if (actualTestDataIndex < testData.length) {
				const testDataRecord = testData[actualTestDataIndex];
				const caseMap = new Map();
				Object.keys(testDataRecord).forEach((key) => {
					caseMap.set(key, testDataRecord[key]);
					caseMap.set(key.toUpperCase(), testDataRecord[key]);
					if (key.trim() !== key) {
						caseMap.set(key.trim(), testDataRecord[key]);
						caseMap.set(key.trim().toUpperCase(), testDataRecord[key]);
					}
				});

				finalContent = finalContent.replace(
					substitutionPattern,
					(match, openingSpan, variableName, closingSpan) => {
						const hasSpace = match[match.length - 1] === " ";

						const trimmedName = variableName.trim();
						const upperName = trimmedName.toUpperCase();
						const value =
							testDataRecord[variableName] ||
							testDataRecord[trimmedName] ||
							testDataRecord[upperName] ||
							caseMap.get(variableName) ||
							caseMap.get(trimmedName) ||
							caseMap.get(upperName);

						// Variable not found in test data, return the original match
						if (value === undefined) {
							return match + (hasSpace ? " " : "");
						}

						if (value === "") {
							return "";
						}

						if (openingSpan) {
							return `${openingSpan}${value}${closingSpan}${hasSpace ? " " : ""}`;
						}
						return value + (hasSpace ? " " : "");
					},
				);
			}
		}
	}

	return finalContent;
}
