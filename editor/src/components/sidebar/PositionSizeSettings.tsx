import { ChevronsDown, ChevronsUp } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { Slider } from "@/components/ui/slider";
import useDebouncedEffect from "@/hooks/useDebouncedEffect"; // Import the custom hook
import type { Element } from "@/types/element";
import { roundToTwoDecimals } from "@/utils/unitConversion";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";

interface PositionSizeSettingsProps {
	element: Element | null;
	onUpdate: (updatedElement: Element) => void;
}

const DEBOUNCE_DELAY = 50; // milliseconds

// Helper to normalise rotation to the range [-180, 180] *inclusive* so that 180° stays 180°
const normalizeRotation = (angle: number): number => {
	if (Number.isNaN(angle)) return NaN;
	// Preserve sign information so ±180° remain distinct
	let r = angle % 360; // now in (-360, 360)
	if (r > 180) r -= 360;
	if (r < -180) r += 360;
	return r;
};

export function PositionSizeSettings({
	element,
	onUpdate,
}: PositionSizeSettingsProps) {
	// Local state for input fields - allows free typing
	const [positionInput, setPositionInput] = useState({ x: "", y: "" });
	const [sizeInput, setSizeInput] = useState({ width: "", height: "" });
	const [rotationInput, setRotationInput] = useState("");
	// State for the slider, driven by the element prop or successful updates
	const [rotationSlider, setRotationSlider] = useState(0);

	// Keep a stable reference to the latest onUpdate handler
	const onUpdateRef = useRef(onUpdate);
	useEffect(() => {
		onUpdateRef.current = onUpdate;
	}, [onUpdate]);

	const stableOnUpdate = useCallback((updatedElement: Element) => {
		onUpdateRef.current(updatedElement);
	}, []);

	// Format number using German locale for display
	const formatNumber = useCallback((num: number | undefined | null): string => {
		if (num === undefined || num === null || Number.isNaN(num)) return "";
		const rounded = roundToTwoDecimals(num);
		return rounded.toString().replace(".", ",");
	}, []);

	// Parse input value (string with potential comma) to number
	const parseInputValue = useCallback((value: string): number => {
		if (
			!value ||
			value.trim() === "" ||
			value.trim() === "," ||
			value.trim() === "-"
		)
			return NaN;
		const sanitizedValue = value.replace(/,$/, ".0").replace(",", ".");
		return parseFloat(sanitizedValue);
	}, []);

	// Refs to track if the user is actively editing fields (to avoid premature resets)
	const isEditingPosXRef = useRef(false);
	const isEditingPosYRef = useRef(false);

	// Initialize/update local state when the selected element changes
	// biome-ignore lint: intentionally omit certain dependencies to prevent user input from being overwritten mid-edit
	useEffect(() => {
		if (element) {
			const currentParsedX = parseInputValue(positionInput.x);
			if (
				(Number.isNaN(currentParsedX) ||
					roundToTwoDecimals(currentParsedX) !== element.x) &&
				!isEditingPosXRef.current
			) {
				setPositionInput((prev) => ({ ...prev, x: formatNumber(element.x) }));
			}
			const currentParsedY = parseInputValue(positionInput.y);
			if (
				(Number.isNaN(currentParsedY) ||
					roundToTwoDecimals(currentParsedY) !== element.y) &&
				!isEditingPosYRef.current
			) {
				setPositionInput((prev) => ({ ...prev, y: formatNumber(element.y) }));
			}

			const currentParsedWidth = parseInputValue(sizeInput.width);
			if (
				Number.isNaN(currentParsedWidth) ||
				roundToTwoDecimals(Math.max(0, currentParsedWidth)) !== element.width
			) {
				setSizeInput((prev) => ({
					...prev,
					width: formatNumber(element.width),
				}));
			}
			const currentParsedHeight = parseInputValue(sizeInput.height);
			if (
				Number.isNaN(currentParsedHeight) ||
				roundToTwoDecimals(Math.max(0, currentParsedHeight)) !== element.height
			) {
				setSizeInput((prev) => ({
					...prev,
					height: formatNumber(element.height),
				}));
			}

			const currentParsedRotation = parseInputValue(rotationInput);
			const currentNormalizedRotation = !Number.isNaN(currentParsedRotation)
				? normalizeRotation(currentParsedRotation)
				: NaN;
			if (
				Number.isNaN(currentNormalizedRotation) ||
				currentNormalizedRotation !== element.rotation
			) {
				setRotationInput(formatNumber(element.rotation));
			}
			setRotationSlider(element.rotation);
		} else {
			setPositionInput({ x: "", y: "" });
			setSizeInput({ width: "", height: "" });
			setRotationInput("");
			setRotationSlider(0);
		}
		// NOTE: No need to clear timeouts here, useDebouncedEffect handles its own cleanup
		// Only run this effect when the element prop itself changes,
		// or the stable formatting/parsing functions change (which they shouldn't).
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [element, formatNumber, parseInputValue]);

	// --- Debounced Update Logic using useDebouncedEffect ---

	// Debounce Position X Update
	useDebouncedEffect(
		() => {
			if (!element) return;
			const rawValue = positionInput.x;
			const numValue = parseInputValue(rawValue);
			const elementValue = element.x;
			const newValue = roundToTwoDecimals(numValue);

			if (!Number.isNaN(numValue) && elementValue !== newValue) {
				stableOnUpdate({ ...element, x: newValue });
			} else if (rawValue === "" && elementValue !== 0) {
				stableOnUpdate({ ...element, x: 0 });
			}
		},
		DEBOUNCE_DELAY,
		[positionInput.x, element, stableOnUpdate, parseInputValue],
	);

	// Debounce Position Y Update
	useDebouncedEffect(
		() => {
			if (!element) return;
			const rawValue = positionInput.y;
			const numValue = parseInputValue(rawValue);
			const elementValue = element.y;
			const newValue = roundToTwoDecimals(numValue);

			if (!Number.isNaN(numValue) && elementValue !== newValue) {
				stableOnUpdate({ ...element, y: newValue });
			} else if (rawValue === "" && elementValue !== 0) {
				stableOnUpdate({ ...element, y: 0 });
			}
		},
		DEBOUNCE_DELAY,
		[positionInput.y, element, stableOnUpdate, parseInputValue],
	);

	// Debounce Size Width Update
	useDebouncedEffect(
		() => {
			if (!element) return;
			const rawValue = sizeInput.width;
			const numValue = parseInputValue(rawValue);
			const validValue = !Number.isNaN(numValue) ? Math.max(0, numValue) : NaN;
			const roundedValue = roundToTwoDecimals(validValue);

			if (!Number.isNaN(validValue)) {
				if (element.type === "image" && element.aspectRatio) {
					const currentWidth = roundedValue;
					const currentHeight = element.aspectRatio
						? roundToTwoDecimals(validValue / element.aspectRatio)
						: 0;
					if (
						element.width !== currentWidth ||
						element.height !== currentHeight
					) {
						// Visually update height input only if width change caused it
						if (element.width !== currentWidth) {
							setSizeInput((prev) => ({
								...prev,
								height: formatNumber(currentHeight),
							}));
						}
						stableOnUpdate({
							...element,
							width: currentWidth,
							height: currentHeight,
						});
					}
				} else if (element.width !== roundedValue) {
					stableOnUpdate({ ...element, width: roundedValue });
				}
			} else if (rawValue === "" && element.width !== 0) {
				if (element.type === "image" && element.aspectRatio) {
					setSizeInput((prev) => ({ ...prev, height: "" }));
					if (element.width !== 0 || element.height !== 0) {
						stableOnUpdate({ ...element, width: 0, height: 0 });
					}
				} else if (element.width !== 0) {
					stableOnUpdate({ ...element, width: 0 });
				}
			}
		},
		DEBOUNCE_DELAY,
		[sizeInput.width, element, stableOnUpdate, parseInputValue, formatNumber],
	);

	// Debounce Size Height Update
	useDebouncedEffect(
		() => {
			if (!element) return;
			const rawValue = sizeInput.height;
			const numValue = parseInputValue(rawValue);
			const validValue = !Number.isNaN(numValue) ? Math.max(0, numValue) : NaN;
			const roundedValue = roundToTwoDecimals(validValue);

			if (!Number.isNaN(validValue)) {
				if (element.type === "image" && element.aspectRatio) {
					const currentHeight = roundedValue;
					const currentWidth = roundToTwoDecimals(
						validValue * element.aspectRatio,
					);
					if (
						element.height !== currentHeight ||
						element.width !== currentWidth
					) {
						// Visually update width input only if height change caused it
						if (element.height !== currentHeight) {
							setSizeInput((prev) => ({
								...prev,
								width: formatNumber(currentWidth),
							}));
						}
						stableOnUpdate({
							...element,
							height: currentHeight,
							width: currentWidth,
						});
					}
				} else if (element.height !== roundedValue) {
					stableOnUpdate({ ...element, height: roundedValue });
				}
			} else if (rawValue === "" && element.height !== 0) {
				if (element.type === "image" && element.aspectRatio) {
					setSizeInput((prev) => ({ ...prev, width: "" }));
					if (element.height !== 0 || element.width !== 0) {
						stableOnUpdate({ ...element, height: 0, width: 0 });
					}
				} else if (element.height !== 0) {
					stableOnUpdate({ ...element, height: 0 });
				}
			}
		},
		DEBOUNCE_DELAY,
		[sizeInput.height, element, stableOnUpdate, parseInputValue, formatNumber],
	);

	// Debounce Rotation Update
	useDebouncedEffect(
		() => {
			if (!element) return;
			const rawValue = rotationInput;
			const numValue = parseInputValue(rawValue);
			const elementValue = element.rotation;

			if (!Number.isNaN(numValue)) {
				const normalizedRotation = normalizeRotation(numValue);
				if (elementValue !== normalizedRotation) {
					setRotationSlider(normalizedRotation); // Update slider when input is valid
					stableOnUpdate({ ...element, rotation: normalizedRotation });
				}
			} else if (rawValue === "" && elementValue !== 0) {
				setRotationSlider(0);
				stableOnUpdate({ ...element, rotation: 0 });
			}
		},
		DEBOUNCE_DELAY,
		[rotationInput, element, stableOnUpdate, parseInputValue],
	);

	// --- Input Change Handlers (Update Local State Only) ---

	const handleLocalPositionChange = useCallback(
		(axis: "x" | "y", value: string) => {
			if (axis === "x") {
				isEditingPosXRef.current = true;
			} else {
				isEditingPosYRef.current = true;
			}
			setPositionInput((prev) => ({ ...prev, [axis]: value }));
		},
		[],
	);

	const handleLocalSizeChange = useCallback(
		(dimension: "width" | "height", value: string) => {
			setSizeInput((prev) => ({ ...prev, [dimension]: value }));
		},
		[],
	);

	const handleLocalRotationChange = useCallback((value: string) => {
		setRotationInput(value);
	}, []);

	// --- Other Handlers ---

	const handleRotationSliderChange = useCallback(
		(value: number[]) => {
			if (!element) return;
			const numValue = value[0];
			setRotationInput(formatNumber(numValue));
			setRotationSlider(numValue);
			// NOTE: No need to clear timeouts explicitly, subsequent debounced effect runs will handle it.
			if (element.rotation !== numValue) {
				stableOnUpdate({ ...element, rotation: numValue });
			}
		},
		[element, formatNumber, stableOnUpdate],
	);

	const handleResetRotation = useCallback(() => {
		if (!element) return;
		setRotationInput("0");
		setRotationSlider(0);
		if (element.rotation !== 0) {
			stableOnUpdate({ ...element, rotation: 0 });
		}
	}, [element, stableOnUpdate]);

	const handleZIndexChange = useCallback(
		(value: string) => {
			if (!element) return;
			const numValue = value.trim() === "" ? 0 : parseInt(value, 10);
			if (Number.isNaN(numValue)) return;
			const validValue = Math.max(0, numValue);
			if (element.zIndex !== validValue) {
				stableOnUpdate({
					...element,
					zIndex: validValue,
					_action: "setZIndex",
				});
			}
		},
		[element, stableOnUpdate],
	);

	const handleMoveToFront = useCallback(() => {
		if (!element || !element.currentPageId) return;
		stableOnUpdate({ ...element, _action: "moveToFront" });
	}, [element, stableOnUpdate]);

	const handleMoveToBack = useCallback(() => {
		if (!element || !element.currentPageId) return;
		stableOnUpdate({ ...element, _action: "moveToBack" });
	}, [element, stableOnUpdate]);

	// Check if this is a table with cells selected
	const isTableWithCellsSelected =
		element?.type === "table" && element.tableProperties?.selection;
	const isSingleCellSelected =
		isTableWithCellsSelected &&
		element.tableProperties?.selection?.start.row ===
			element.tableProperties?.selection?.end.row &&
		element.tableProperties?.selection?.start.col ===
			element.tableProperties?.selection?.end.col;

	// Get the selected cell's column and row indices for single cell selection
	const selectedCellRow = isSingleCellSelected
		? element.tableProperties?.selection?.start.row
		: null;
	const selectedCellCol = isSingleCellSelected
		? element.tableProperties?.selection?.start.col
		: null;

	// Get current column width and row height for the selected cell
	const selectedColWidth =
		selectedCellCol !== null &&
		selectedCellCol !== undefined &&
		element?.tableProperties?.columnWidths &&
		element.tableProperties.columnWidths[selectedCellCol] !== undefined
			? element.tableProperties.columnWidths[selectedCellCol]
			: null;
	const selectedRowHeight =
		selectedCellRow !== null &&
		selectedCellRow !== undefined &&
		element?.tableProperties?.rowHeights &&
		element.tableProperties.rowHeights[selectedCellRow] !== undefined
			? element.tableProperties.rowHeights[selectedCellRow]
			: null;

	// Local state for column/row size inputs
	const [colWidthInput, setColWidthInput] = useState("");
	const [rowHeightInput, setRowHeightInput] = useState("");

	// Update column/row inputs when selection changes
	useEffect(() => {
		if (isSingleCellSelected && selectedColWidth !== null) {
			setColWidthInput(formatNumber(selectedColWidth));
		} else {
			setColWidthInput("");
		}
		if (isSingleCellSelected && selectedRowHeight !== null) {
			setRowHeightInput(formatNumber(selectedRowHeight));
		} else {
			setRowHeightInput("");
		}
	}, [isSingleCellSelected, selectedColWidth, selectedRowHeight, formatNumber]);

	// Debounced update for column width
	useDebouncedEffect(
		() => {
			if (
				!element ||
				!isSingleCellSelected ||
				selectedCellCol === null ||
				selectedCellCol === undefined ||
				!element.tableProperties ||
				!element.tableProperties.cells ||
				element.tableProperties.cells.length === 0
			)
				return;
			const numValue = parseInputValue(colWidthInput);
			if (!Number.isNaN(numValue) && numValue > 0) {
				const numCols =
					element.tableProperties.columns ||
					(element.tableProperties.cells[0]?.length ?? 0);
				const currentColumnWidths = element.tableProperties.columnWidths || [];
				const newColumnWidths = Array(numCols)
					.fill(null)
					.map((_, i) => {
						if (i === selectedCellCol) {
							return roundToTwoDecimals(numValue);
						}
						return roundToTwoDecimals(currentColumnWidths[i] || 15); // Default to 15mm if not set
					});

				const newTotalWidth = newColumnWidths.reduce(
					(sum, width) => sum + (width || 0),
					0,
				);

				stableOnUpdate({
					...element,
					width: roundToTwoDecimals(newTotalWidth),
					tableProperties: {
						...element.tableProperties,
						columnWidths: newColumnWidths,
						columns: newColumnWidths.length, // Ensure columns count is also updated
					},
				});
			}
		},
		DEBOUNCE_DELAY,
		[
			colWidthInput,
			element,
			isSingleCellSelected,
			selectedCellCol,
			stableOnUpdate,
			parseInputValue,
		],
	);

	// Debounced update for row height
	useDebouncedEffect(
		() => {
			if (
				!element ||
				!isSingleCellSelected ||
				selectedCellRow === null ||
				selectedCellRow === undefined ||
				!element.tableProperties ||
				!element.tableProperties.cells
			)
				return;
			const numValue = parseInputValue(rowHeightInput);
			if (!Number.isNaN(numValue) && numValue > 0) {
				const numRows =
					element.tableProperties.rows ||
					(element.tableProperties.cells.length ?? 0);
				const currentRowHeights = element.tableProperties.rowHeights || [];
				const newRowHeights = Array(numRows)
					.fill(null)
					.map((_, i) => {
						if (i === selectedCellRow) {
							return roundToTwoDecimals(numValue);
						}
						return roundToTwoDecimals(currentRowHeights[i] || 10); // Default to 10mm if not set
					});

				const newTotalHeight = newRowHeights.reduce(
					(sum, height) => sum + (height || 0),
					0,
				);

				stableOnUpdate({
					...element,
					height: roundToTwoDecimals(newTotalHeight),
					tableProperties: {
						...element.tableProperties,
						rowHeights: newRowHeights,
						rows: newRowHeights.length, // Ensure rows count is also updated
					},
				});
			}
		},
		DEBOUNCE_DELAY,
		[
			rowHeightInput,
			element,
			isSingleCellSelected,
			selectedCellRow,
			stableOnUpdate,
			parseInputValue,
		],
	);

	// After the debounced update commits, mark editing refs as false so effect can sync again
	useDebouncedEffect(
		() => {
			isEditingPosXRef.current = false;
		},
		DEBOUNCE_DELAY + 10,
		[positionInput.x],
	);
	useDebouncedEffect(
		() => {
			isEditingPosYRef.current = false;
		},
		DEBOUNCE_DELAY + 10,
		[positionInput.y],
	);

	// --- Render ---
	return (
		<div className="space-y-4">
			{/* Show column/row size controls for single cell selection */}
			{isSingleCellSelected && (
				<div>
					<Label className="text-sm font-medium">Zellengröße (mm)</Label>
					<div className="grid grid-cols-2 gap-2 mt-1">
						<div className="space-y-1">
							<Label htmlFor="col-width" className="text-xs">
								Spaltenbreite
							</Label>
							<Input
								id="col-width"
								type="text"
								inputMode="decimal"
								value={colWidthInput}
								onChange={(e) => setColWidthInput(e.target.value)}
								className="h-8"
							/>
						</div>
						<div className="space-y-1">
							<Label htmlFor="row-height" className="text-xs">
								Zeilenhöhe
							</Label>
							<Input
								id="row-height"
								type="text"
								inputMode="decimal"
								value={rowHeightInput}
								onChange={(e) => setRowHeightInput(e.target.value)}
								className="h-8"
							/>
						</div>
					</div>
				</div>
			)}

			{/* Show standard size controls for table when no cells selected */}
			{!isTableWithCellsSelected && (
				<>
					{/* Position Section */}
					<div>
						<Label className="text-sm font-medium">Position (mm)</Label>
						<div className="grid grid-cols-2 gap-2 mt-1">
							<div className="space-y-1">
								<Label htmlFor="position-x" className="text-xs">
									X
								</Label>
								<Input
									id="position-x"
									type="text"
									inputMode="decimal"
									value={positionInput.x}
									onChange={(e) =>
										handleLocalPositionChange("x", e.target.value)
									}
									className="h-8"
									disabled={!element}
								/>
							</div>
							<div className="space-y-1">
								<Label htmlFor="position-y" className="text-xs">
									Y
								</Label>
								<Input
									id="position-y"
									type="text"
									inputMode="decimal"
									value={positionInput.y}
									onChange={(e) =>
										handleLocalPositionChange("y", e.target.value)
									}
									className="h-8"
									disabled={!element}
								/>
							</div>
						</div>
					</div>

					{/* Size Section */}
					<div>
						<Label className="text-sm font-medium">Größe (mm)</Label>
						<div className="grid grid-cols-2 gap-2 mt-1">
							<div className="space-y-1">
								<Label htmlFor="size-width" className="text-xs">
									Breite
								</Label>
								<Input
									id="size-width"
									type="text"
									inputMode="decimal"
									value={sizeInput.width}
									onChange={(e) =>
										handleLocalSizeChange("width", e.target.value)
									}
									className="h-8"
									disabled={!element}
								/>
							</div>
							<div className="space-y-1">
								<Label htmlFor="size-height" className="text-xs">
									Höhe
								</Label>
								<Input
									id="size-height"
									type="text"
									inputMode="decimal"
									value={sizeInput.height}
									onChange={(e) =>
										handleLocalSizeChange("height", e.target.value)
									}
									className="h-8"
									disabled={!element}
								/>
							</div>
						</div>
					</div>

					{/* Z-Index Section */}
					<div>
						<Label className="text-sm font-medium">Ebene (Z-Index)</Label>
						<div className="flex items-center gap-2 mt-1">
							<Input
								id="z-index"
								type="number"
								value={
									element?.zIndex !== undefined
										? element.zIndex.toString()
										: "0"
								}
								onChange={(e) => handleZIndexChange(e.target.value)}
								className="h-8 flex-1"
								disabled={!element}
								step="1"
								min="0"
								data-testid="zindex-input"
								data-zindex={element?.zIndex}
							/>
							<Button
								variant="outline"
								size="sm"
								onClick={handleMoveToBack}
								disabled={!element}
								className="h-8"
								title="Nach ganz hinten"
							>
								<ChevronsDown className="h-4 w-4" />
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={handleMoveToFront}
								disabled={!element}
								className="h-8"
								title="Nach ganz vorn"
							>
								<ChevronsUp className="h-4 w-4" />
							</Button>
						</div>
					</div>

					{/* Rotation Section */}
					<div>
						<Label className="text-sm font-medium">Rotation (°)</Label>
						<div className="space-y-4 mt-1">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<Label htmlFor="rotation" className="text-xs">
										Winkel
									</Label>
									<div className="flex items-center space-x-2">
										<button
											type="button"
											onClick={handleResetRotation}
											className="text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:pointer-events-none"
											disabled={!element || element.rotation === 0}
										>
											Zurücksetzen
										</button>
										<Input
											id="rotation"
											type="text"
											inputMode="decimal"
											value={rotationInput}
											onChange={(e) =>
												handleLocalRotationChange(e.target.value)
											}
											className="h-8 w-20"
											disabled={!element}
										/>
									</div>
								</div>
								<div className="relative pt-1">
									<div className="flex justify-between text-xs text-gray-500 px-1 mb-1">
										<span>-180°</span>
										<span>-90°</span>
										<span>0°</span>
										<span>90°</span>
										<span>180°</span>
									</div>
									<Slider
										value={[rotationSlider]}
										min={-180}
										max={180}
										step={1}
										onValueChange={handleRotationSliderChange}
										disabled={!element}
										className="mt-2"
									/>
								</div>
							</div>
						</div>
					</div>
				</>
			)}
		</div>
	);
}
