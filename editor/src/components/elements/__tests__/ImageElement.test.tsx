import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";

import type { Element } from "@/types/element";
import { ImageElement } from "../ImageElement";

describe("ImageElement component", () => {
	const mockImageElement: Element = {
		id: "image-1",
		currentPageId: "page-1",
		type: "image",
		x: 10,
		y: 20,
		width: 100,
		height: 50,
		rotation: 0,
		content: "data:image/png;base64,abc123", // Mock base64 image data
	};

	it("renders image from objectURL when provided", () => {
		const { container } = render(
			<ImageElement
				element={mockImageElement}
				imageObjectURL="blob:http://example.com/image.png"
				previewMode={false}
			/>,
		);

		const img = container.querySelector("img");
		expect(img).toBeInTheDocument();
		expect(img).toHaveAttribute("src", "blob:http://example.com/image.png");
		expect(img).toHaveClass("object-contain");
		expect(img).toHaveAttribute("draggable", "false");
	});

	it("renders image from element content when no objectURL is provided", () => {
		const { container } = render(
			<ImageElement
				element={mockImageElement}
				imageObjectURL={null}
				previewMode={false}
			/>,
		);

		const img = container.querySelector("img");
		expect(img).toBeInTheDocument();
		expect(img).toHaveAttribute("src", "data:image/png;base64,abc123");
	});

	it("renders placeholder when no image is available", () => {
		const emptyElement = { ...mockImageElement, content: undefined };

		render(
			<ImageElement
				element={emptyElement}
				imageObjectURL={null}
				previewMode={false}
			/>,
		);

		expect(screen.getByText("Kein Bild")).toBeInTheDocument();
	});

	it("applies border radius when specified", () => {
		const elementWithBorderRadius = {
			...mockImageElement,
			borderRadius: [5, 10, 15, 20],
		};

		const { container } = render(
			<ImageElement
				element={elementWithBorderRadius}
				imageObjectURL={null}
				previewMode={false}
			/>,
		);

		const wrapper = container.firstChild as HTMLElement;
		expect(wrapper.style.borderRadius).toBe("5mm 10mm 15mm 20mm");
		expect(wrapper.style.overflow).toBe("hidden");
	});

	it("doesn't show content in preview mode without objectURL", () => {
		render(
			<ImageElement
				element={mockImageElement}
				imageObjectURL={null}
				previewMode={true}
			/>,
		);

		expect(screen.getByText("Kein Bild")).toBeInTheDocument();
		expect(screen.queryByRole("img")).not.toBeInTheDocument();
	});

	it("shows objectURL in preview mode", () => {
		const { container } = render(
			<ImageElement
				element={mockImageElement}
				imageObjectURL="blob:http://example.com/image.png"
				previewMode={true}
			/>,
		);

		const img = container.querySelector("img");
		expect(img).toBeInTheDocument();
		expect(img).toHaveAttribute("src", "blob:http://example.com/image.png");
	});
});
