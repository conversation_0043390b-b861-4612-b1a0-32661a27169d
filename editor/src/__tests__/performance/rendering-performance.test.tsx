import { act } from "@testing-library/react";
import * as React from "react";
import { useEffect } from "react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import type { Element as EditorElement } from "@/types/element";
import { renderWithProviders } from "../utils/test-utils";

// Mock PageRenderer component
vi.mock("../../components/PageRenderer", () => ({
	PageRenderer: ({ pageId }) => (
		<div data-testid={`page-${pageId}`}>Mocked Page Renderer</div>
	),
}));

describe("Rendering Performance Benchmarks", () => {
	// Performance measurement utilities
	const measurePerformance = (callback: () => void, iterations = 5) => {
		const times: number[] = [];

		for (let i = 0; i < iterations; i++) {
			const start = performance.now();
			callback();
			const end = performance.now();
			times.push(end - start);
		}

		const average = times.reduce((sum, time) => sum + time, 0) / times.length;
		const max = Math.max(...times);
		const min = Math.min(...times);

		return { average, max, min, times };
	};

	beforeEach(() => {
		// Reset performance metrics
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.restoreAllMocks();
		vi.useRealTimers();
	});

	it("should render large number of elements efficiently", () => {
		// Create editor state with many elements (100+)
		const largeDataset = {
			pages: [
				{
					id: "page1",
					format: "A4",
					orientation: "portrait" as const,
					width: 210,
					height: 297,
				},
			],
			elements: Array.from({ length: 100 }, (_, i) => ({
				id: `element-${i}`,
				currentPageId: "page1",
				type: "text" as const,
				x: 50,
				y: 50 + i * 10,
				width: 200,
				height: 30,
				rotation: 0,
				zIndex: i,
				content: `Element ${i}`,
			})),
		};

		// Measure initial render performance
		const renderMetrics = measurePerformance(() => {
			const { unmount } = renderWithProviders(
				<div data-testid="page-renderer">Mocked Page Renderer</div>,
				{ initialState: largeDataset },
			);
			unmount();
		});

		// Log performance metrics
		console.log("Large dataset render metrics:", renderMetrics);

		// Assert that average render time is below threshold (adjust based on your performance targets)
		expect(renderMetrics.average).toBeLessThan(500); // 500ms threshold
	});

	it("should handle frequent updates efficiently", () => {
		// Create component that triggers frequent updates
		const UpdateComponent = () => {
			const [count, setCount] = React.useState(0);

			useEffect(() => {
				const interval = setInterval(() => {
					setCount((c) => c + 1);
				}, 10); // Update every 10ms

				return () => clearInterval(interval);
			}, []);

			return <div data-testid="count">{count}</div>;
		};

		// Create editor state with moderate number of elements
		const editorState = {
			pages: [
				{
					id: "page1",
					format: "A4",
					orientation: "portrait" as const,
					width: 210,
					height: 297,
				},
			],
			elements: Array.from({ length: 50 }, (_, i) => ({
				id: `element-${i}`,
				currentPageId: "page1",
				type: "text" as const,
				x: 50,
				y: 50 + i * 10,
				width: 200,
				height: 30,
				rotation: 0,
				zIndex: i,
				content: `Element ${i}`,
			})),
		};

		// Render component
		const { unmount } = renderWithProviders(
			<>
				<div data-testid="page-renderer">Mocked Page Renderer</div>
				<UpdateComponent />
			</>,
			{ initialState: editorState },
		);

		// Wait for multiple renders
		act(() => {
			vi.advanceTimersByTime(100); // Trigger 10 updates
		});

		// Unmount
		unmount();
	});

	it("should maintain performance with complex nested elements", () => {
		// Create deeply nested block elements
		const createNestedBlocks = (
			depth: number,
			parentId: string | undefined = undefined,
		): EditorElement[] => {
			if (depth <= 0) return [];

			const blockId = `block-${depth}`;
			const block: EditorElement = {
				id: blockId,
				currentPageId: "page1",
				type: "block",
				x: 50,
				y: 100 * depth,
				width: 300 - depth * 20,
				height: 200 - depth * 20,
				rotation: 0,
				zIndex: depth,
				childElementIds: [],
				parentId,
				_delete: undefined,
				_action: undefined,
				content: undefined,
				shapeType: undefined,
				borderRadius: undefined,
				color: undefined,
				backgroundColor: undefined,
				isAbsolutePosition: undefined,
				aspectRatio: undefined,
				tableProperties: undefined,
				verticalAlign: undefined,
				isLocked: undefined,
				isAddressField: undefined,
				isPageNumber: undefined,
				addressLayout: undefined,
			};

			return [block, ...createNestedBlocks(depth - 1, blockId)];
		};

		// Create editor state with deeply nested blocks (depth of 5)
		const nestedElements = createNestedBlocks(5, undefined);
		const editorState = {
			pages: [
				{
					id: "page1",
					format: "A4",
					orientation: "portrait" as const,
					width: 210,
					height: 297,
				},
			],
			elements: [
				...nestedElements,
				...Array.from({ length: 20 }, (_, i) => ({
					id: `element-${i}`,
					currentPageId: "page1",
					type: "text" as const,
					x: 50,
					y: 50 + i * 10,
					width: 200,
					height: 30,
					rotation: 0,
					zIndex: i + 10,
					content: `Element ${i}`,
				})),
			],
		};

		// Measure render performance
		const renderMetrics = measurePerformance(() => {
			const { unmount } = renderWithProviders(
				<div data-testid="page-renderer">Mocked Page Renderer</div>,
				{ initialState: editorState },
			);
			unmount();
		});

		console.log("Nested elements render metrics:", renderMetrics);

		// Assert performance is within acceptable limits
		expect(renderMetrics.average).toBeLessThan(600); // 600ms threshold for complex nesting
	});
});

describe("Memory Usage and Cleanup Tests", () => {
	beforeEach(() => {
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.restoreAllMocks();
		vi.useRealTimers();
	});

	it("should properly clean up event listeners on unmount", () => {
		// Create a component with event listeners
		const ComponentWithListeners = () => {
			useEffect(() => {
				const handleClick = () => {};
				const handleResize = () => {};

				document.addEventListener("click", handleClick);
				window.addEventListener("resize", handleResize);

				return () => {
					document.removeEventListener("click", handleClick);
					window.removeEventListener("resize", handleResize);
				};
			}, []);

			return <div>Component with listeners</div>;
		};

		// Render and unmount component
		const { unmount } = renderWithProviders(<ComponentWithListeners />);

		// Unmount component
		unmount();

		// This test is primarily to ensure no errors occur during cleanup
		expect(true).toBe(true);
	});

	it("should clean up timeouts and intervals", () => {
		// Create a component with timeouts and intervals
		const TimerComponent = () => {
			useEffect(() => {
				const timeout = setTimeout(() => {}, 1000);
				const interval = setInterval(() => {}, 500);

				return () => {
					clearTimeout(timeout);
					clearInterval(interval);
				};
			}, []);

			return <div>Timer Component</div>;
		};

		// Render and unmount component
		const { unmount } = renderWithProviders(<TimerComponent />);

		// Unmount component
		unmount();

		// This test is primarily to ensure no errors occur during cleanup
		expect(true).toBe(true);
	});

	it("should handle large state changes without memory issues", () => {
		// Create component that updates large state objects
		const LargeStateComponent = () => {
			const [elements, setElements] = React.useState<EditorElement[]>([]);

			useEffect(() => {
				// Create large array of elements
				const newElements: EditorElement[] = Array.from(
					{ length: 1000 },
					(_, i) => ({
						id: `element-${i}`,
						currentPageId: "page1",
						type: "text",
						x: i % 100,
						y: Math.floor(i / 100),
						width: 10,
						height: 10,
						rotation: 0,
						zIndex: i,
						content: `Content ${i}`,
						childElementIds: undefined,
						parentId: undefined,
						_delete: undefined,
						_action: undefined,
						shapeType: undefined,
						borderRadius: undefined,
						color: undefined,
						backgroundColor: undefined,
						isAbsolutePosition: undefined,
						aspectRatio: undefined,
						tableProperties: undefined,
						verticalAlign: undefined,
						isLocked: undefined,
						isAddressField: undefined,
						isPageNumber: undefined,
						addressLayout: undefined,
					}),
				);

				setElements(newElements);

				// Update elements multiple times
				const interval = setInterval(() => {
					setElements((prev) => {
						// Create new array with modified elements
						return prev.map((el) => ({
							...el,
							content: `${el.content} updated`,
						}));
					});
				}, 100);

				return () => clearInterval(interval);
			}, []);

			return (
				<div>
					{elements.length > 0 && <div>Elements loaded: {elements.length}</div>}
				</div>
			);
		};

		// Render component
		const { unmount } = renderWithProviders(<LargeStateComponent />);

		// Advance timers to trigger state updates
		act(() => {
			vi.advanceTimersByTime(500); // 5 updates
		});

		// Unmount to check cleanup
		unmount();

		// No specific assertion here, but this test would catch memory leaks
		// in real browser environments or with memory profiling tools
		expect(true).toBe(true);
	});
});
