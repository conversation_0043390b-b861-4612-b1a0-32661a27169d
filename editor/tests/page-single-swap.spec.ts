import { expect, type Locator, type Page, test } from "@playwright/test";

const host = process.env.EDITOR_TEST_HOST;
if (!host) {
	throw new Error("EDITOR_TEST_HOST is not set");
}

/**
 * Helper: start a mouse drag operation from the center of a source element.
 */
async function startDrag(page: Page, source: Locator) {
	const box = await source.boundingBox();
	if (!box) return;

	await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
	await page.mouse.down();
	// tiny movement to trigger drag state
	await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2 + 5);
}

/**
 * Helper: perform drag-and-drop from source onto target.
 */
async function dragAndDrop(page: Page, source: Locator, target: Locator) {
	await startDrag(page, source);

	const targetBox = await target.boundingBox();
	if (targetBox) {
		await page.mouse.move(
			targetBox.x + targetBox.width / 2,
			targetBox.y + targetBox.height / 2,
			{ steps: 15 },
		);
		// allow any drag-over state to register
		await page.waitForTimeout(100);
	}

	await page.mouse.up();
}

test.describe("Single Page Swapping", () => {
	test.beforeEach(async ({ page }) => {
		await page.goto(`${host}/editor?id=105&version=latest`);
		// Wait for pages to render inside main editor area
		await page.waitForSelector('[data-page-id][role="button"]');
		// Wait for minimap to render
		await page.waitForSelector('.fixed.right-4 button[title^="Seite"]');
	});

	test("should swap pages of identical size via drag & drop", async ({
		page,
	}) => {
		// 1️⃣ Determine initial order in the main editor (authoritative order)
		const getPageOrder = async () =>
			page
				.locator('[data-page-id][role="button"]')
				.evaluateAll((els) => els.map((el) => el.getAttribute("data-page-id")));

		const initialOrder = await getPageOrder();

		// 2️⃣ In the minimap, locate a source page and a target page that share the same format (size)
		const minimapPages = page.locator('.fixed.right-4 button[title^="Seite "]');
		const _pageCount = await minimapPages.count();

		// Collect meta information about each minimap page
		const pagesMeta = await minimapPages.evaluateAll((elements) =>
			elements.map((el) => {
				const title = el.getAttribute("title") || "";
				// Example: "Seite 3 - A4"
				const match = title.match(/Seite\s+(\d+)\s+-\s+(.+?)(\s|$)/);
				const index = match ? parseInt(match[1], 10) : -1;
				const format = match ? match[2] : "Unknown";
				return { index, format };
			}),
		);

		// Choose first page as source
		const sourceIdx = 0;
		const sourceMeta = pagesMeta[sourceIdx];
		let targetIdx = -1;
		for (let i = 1; i < pagesMeta.length; i++) {
			if (pagesMeta[i].format === sourceMeta.format) {
				targetIdx = i;
				break;
			}
		}
		test.skip(
			targetIdx === -1,
			"No second page with identical size found to perform swap test",
		);

		// Perform drag from source onto target
		const sourceBtn = minimapPages.nth(sourceIdx);
		const targetBtn = minimapPages.nth(targetIdx);
		await dragAndDrop(page, sourceBtn, targetBtn);

		// Wait for potential reorder to settle
		await page.waitForTimeout(1500);

		const postSwapOrder = await getPageOrder();
		expect(postSwapOrder).not.toEqual(initialOrder);
		// Verify the same set of page IDs is still present
		expect(postSwapOrder.sort()).toEqual(initialOrder.sort());
	});

	test("should NOT swap pages of different size", async ({ page }) => {
		// Snapshot order before invalid attempt
		const getPageOrder = async () =>
			page
				.locator('[data-page-id][role="button"]')
				.evaluateAll((els) => els.map((el) => el.getAttribute("data-page-id")));

		const baselineOrder = await getPageOrder();

		// Identify a source and target of differing formats
		const minimapPages = page.locator('.fixed.right-4 button[title^="Seite "]');
		const pagesMeta = await minimapPages.evaluateAll((elements) =>
			elements.map((el) => {
				const title = el.getAttribute("title") || "";
				const match = title.match(/Seite\s+(\d+)\s+-\s+(.+?)(\s|$)/);
				const index = match ? parseInt(match[1], 10) : -1;
				const format = match ? match[2] : "Unknown";
				return { index, format };
			}),
		);

		let sourceIdx = -1;
		let targetIdx = -1;
		outer: for (let i = 0; i < pagesMeta.length; i++) {
			for (let j = i + 1; j < pagesMeta.length; j++) {
				if (pagesMeta[i].format !== pagesMeta[j].format) {
					sourceIdx = i;
					targetIdx = j;
					break outer;
				}
			}
		}
		test.skip(
			sourceIdx === -1 || targetIdx === -1,
			"Could not find pages with differing sizes",
		);

		const sourceBtn = minimapPages.nth(sourceIdx);
		const targetBtn = minimapPages.nth(targetIdx);

		await dragAndDrop(page, sourceBtn, targetBtn);
		// Wait briefly for any UI reaction
		await page.waitForTimeout(1000);

		const finalOrder = await getPageOrder();
		expect(finalOrder).toEqual(baselineOrder);
	});
});
