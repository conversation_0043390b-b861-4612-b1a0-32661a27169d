import { Image as ImageIcon, Square, Table } from "lucide-react";
import { useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { API_URL, storeImageFile } from "@/utils/apiService";
import { getScrollAwarePosition } from "@/utils/elementHelpers";
import { pxToMm, roundToTwoDecimals } from "@/utils/unitConversion";
import type { Element, ElementType } from "../../types/element";
import type { TableProperties } from "../../types/table";
import type {
	BaseToolbarProps,
	ColorToolbarProps,
	PageContextProps,
	RoleBasedProps,
} from "../../types/toolbar";
import { ImageBrowser } from "../ImageBrowser";
import { TableCreateDialog } from "../table/TableCreateDialog";
import { Button } from "../ui/button";
import { ColorPicker } from "../ui/ColorPicker";
import { CornerRadiusSettings } from "../ui/CornerRadiusSettings";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { PositionSizeSettings } from "./PositionSizeSettings";
import { ToolbarButton, ToolbarContainer } from "./ToolbarBase";

interface ElementToolbarProps
	extends BaseToolbarProps,
		ColorToolbarProps,
		PageContextProps,
		RoleBasedProps {
	elementType?: ElementType;
}

export function ElementToolbar({
	onAddElement,
	elementType,
	selectedElement,
	onUpdateElement,
	apiColors = [],
	isLoadingColors = false,
	colorError = null,
	role,
}: ElementToolbarProps) {
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [isTablePopoverOpen, setIsTablePopoverOpen] = useState(false);
	const [isImageBrowserOpen, setIsImageBrowserOpen] = useState(false);
	const isAgency = role === "agency";

	const handleImageClick = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			// Create a local blob URL for immediate display
			const blobUrl = URL.createObjectURL(file);

			// Load the image to get its natural dimensions
			const img = new window.Image();
			img.src = blobUrl;

			await new Promise<void>((resolve) => {
				img.onload = () => {
					resolve();
				};
			});

			// Calculate aspect ratio
			const aspectRatio = img.naturalWidth / img.naturalHeight;

			// Use a fixed width in mm and calculate height based on aspect ratio
			const width = 50; // mm
			const height = width / aspectRatio;

			// Get scroll-aware position
			const { x, y } = getScrollAwarePosition();

			try {
				// Upload the file to the server
				const imageUrl = await storeImageFile(file, file.name);
				console.log("File uploaded, URL:", imageUrl);

				// Create a new element with the image
				const newElement: Element = {
					id: uuidv4(),
					type: "image",
					content: `${API_URL}/image/get/${imageUrl}`,
					x,
					y,
					width,
					height,
					rotation: 0,
					currentPageId: "",
					borderRadius: [0, 0, 0, 0],
					aspectRatio,
				};

				onAddElement?.(newElement);
			} catch (error) {
				console.error("Error uploading image:", error);
				// Handle error - perhaps show a notification to the user
			} finally {
				// Clear the file input
				if (fileInputRef.current) {
					fileInputRef.current.value = "";
				}
			}

			// Release the blob URL
			URL.revokeObjectURL(blobUrl);
		}
	};

	const handleCreateTable = (tableProperties: TableProperties) => {
		// Default cell width and height in pixels
		const cellWidthPx = 50;
		const cellHeightPx = 50;

		// Calculate total width and height in pixels
		const widthPx = tableProperties.columns * cellWidthPx;
		const heightPx = tableProperties.rows * cellHeightPx;

		// Convert to millimeters
		const widthMm = roundToTwoDecimals(pxToMm(widthPx));
		const heightMm = roundToTwoDecimals(pxToMm(heightPx));

		// Get scroll-aware position
		const { x, y } = getScrollAwarePosition();

		const newElement: Element = {
			id: uuidv4(),
			type: "table",
			x,
			y,
			width: widthMm, // mm
			height: heightMm, // mm
			rotation: 0,
			currentPageId: "",
			tableProperties,
		};
		onAddElement?.(newElement);
	};

	const handleElementClick = (type: ElementType) => {
		if (type === "image") {
			handleImageClick();
		} else {
			// Get scroll-aware position
			const { x, y } = getScrollAwarePosition();

			const newElement: Element = {
				id: uuidv4(),
				type,
				content: "",
				x,
				y,
				width: 30, // mm
				height: 30, // mm
				rotation: 0,
				currentPageId: "",
				...(type === "shape" && {
					backgroundColor: "#e2e8f0", // Default color
					borderRadius: [0, 0, 0, 0], // Initialize with zero border radius
				}),
			};
			onAddElement?.(newElement);
		}
	};

	const handleUpdateElement = (updatedElement: Element) => {
		console.log(
			"[ElementToolbar] handleUpdateElement called with:",
			updatedElement,
		);
		if (onUpdateElement) {
			onUpdateElement(updatedElement);
		} else {
			console.log("[ElementToolbar] onUpdateElement is null/undefined");
		}
	};

	const handleSelectExistingImage = async (
		imageUrl: string,
		aspectRatio: number,
	) => {
		console.log("ElementToolbar: Creating element with image URL:", imageUrl);

		// Calculate dimensions based on aspect ratio
		const width = 50; // mm
		const height = width / aspectRatio;

		// Get scroll-aware position
		const { x, y } = getScrollAwarePosition();

		// Create element with the selected image URL
		// Note: API_URL is not needed here as the imageUrl already includes it
		const newElement: Element = {
			id: uuidv4(),
			type: "image",
			content: imageUrl, // URL already includes full path with API_URL
			x,
			y,
			width: width,
			height: height,
			rotation: 0,
			currentPageId: "",
			borderRadius: [0, 0, 0, 0],
			aspectRatio: aspectRatio,
		};

		onAddElement?.(newElement);
		setIsImageBrowserOpen(false);
	};

	// Render specific content based on elementType
	if (elementType === "image") {
		return (
			<ToolbarContainer>
				<input
					type="file"
					ref={fileInputRef}
					className="hidden"
					accept="image/*"
					onChange={handleFileChange}
				/>
				<div className="grid grid-cols-2 gap-2">
					<Button
						variant="outline"
						className={`w-full ${isAgency ? "flex" : "hidden"} items-center justify-center gap-2`}
						onClick={handleImageClick}
					>
						<ImageIcon className="w-4 h-4" />
						Hochladen
					</Button>

					<Button
						variant="outline"
						className="w-full flex items-center justify-center gap-2"
						onClick={() => setIsImageBrowserOpen(true)}
					>
						<ImageIcon className="w-4 h-4" />
						Auswählen
					</Button>

					<ImageBrowser
						isOpen={isImageBrowserOpen}
						onSelectImage={handleSelectExistingImage}
						onClose={() => setIsImageBrowserOpen(false)}
						role={role}
					/>
				</div>

				<PositionSizeSettings
					element={selectedElement || null}
					onUpdate={handleUpdateElement}
				/>

				<CornerRadiusSettings
					element={selectedElement || null}
					onUpdate={handleUpdateElement}
				/>
			</ToolbarContainer>
		);
	}

	if (elementType === "table") {
		return (
			<ToolbarContainer>
				<Popover open={isTablePopoverOpen} onOpenChange={setIsTablePopoverOpen}>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							className="w-full flex items-center justify-center gap-2"
						>
							<Table className="w-4 h-4" />
							Tabelle hinzufügen
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<TableCreateDialog
							onCreateTable={(props) => {
								handleCreateTable(props);
								setIsTablePopoverOpen(false);
							}}
							onClose={() => setIsTablePopoverOpen(false)}
						/>
					</PopoverContent>
				</Popover>

				<PositionSizeSettings
					element={selectedElement || null}
					onUpdate={handleUpdateElement}
				/>
			</ToolbarContainer>
		);
	}

	if (elementType === "shape") {
		return (
			<ToolbarContainer>
				<ToolbarButton
					icon={Square}
					label="Form hinzufügen"
					onClick={() => handleElementClick("shape")}
				/>

				<PositionSizeSettings
					element={selectedElement || null}
					onUpdate={handleUpdateElement}
				/>

				<ColorPicker
					element={selectedElement}
					onUpdate={handleUpdateElement}
					apiColors={apiColors}
					isLoadingColors={isLoadingColors}
					colorError={colorError}
					label="Formfarbe"
				/>

				<CornerRadiusSettings
					element={selectedElement || null}
					onUpdate={handleUpdateElement}
				/>
			</ToolbarContainer>
		);
	}

	return null;
}
