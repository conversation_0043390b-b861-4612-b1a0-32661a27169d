import { useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import type { PageFormat } from "@/utils/apiService";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

// Simple fallback page formats for when API formats are not available
const PAGE_FORMATS = {
	A4: { width: "21", height: "29.7" },
	A5: { width: "14.8", height: "21" },
	POSTCARD: { width: "10.5", height: "14.8" },
} as const;

interface PageOperationsProps {
	pages: PageSettings[];
	elements: Element[];
	setEditorState: (
		state: { pages: PageSettings[]; elements: Element[] },
		addToHistory?: boolean,
	) => void;
	currentPage: string | null;
	setCurrentPage: (pageId: string | null) => void;
	isEditorLocked: boolean;
	apiFormats: PageFormat[];
}

export function usePageOperations({
	pages,
	elements,
	setEditorState,
	currentPage,
	setCurrentPage,
	isEditorLocked,
	apiFormats,
}: PageOperationsProps) {
	const handleAddPage = useCallback(
		(format: string, insertAtIndex?: number) => {
			if (isEditorLocked) return;

			// Find the selected format in the API formats
			const findFormat = (): PageFormat | null => {
				// First, try to find the format in the API formats
				if (apiFormats && apiFormats.length > 0) {
					const apiFormat = apiFormats.find((f) => f.name === format);
					if (apiFormat) {
						// Return the full format object from API
						return apiFormat;
					}
				}

				// Fallback to static formats if API formats are not available
				const isLandscape = format.toLowerCase().includes("landscape");
				const baseFormat = isLandscape
					? format.replace("_landscape", "")
					: format;
				const staticFormat =
					PAGE_FORMATS[baseFormat as keyof typeof PAGE_FORMATS];

				// Construct a partial PageFormat-like object for fallback
				const fallbackWidth = staticFormat
					? parseFloat(staticFormat.width)
					: 21;
				const fallbackHeight = staticFormat
					? parseFloat(staticFormat.height)
					: 29.7;

				return {
					name: format,
					description: format, // Use name as description for fallback
					width: isLandscape ? fallbackHeight * 10 : fallbackWidth * 10, // Store in mm
					height: isLandscape ? fallbackWidth * 10 : fallbackHeight * 10, // Store in mm
					// Default trim values for fallback
					trim_top: 0,
					trim_right: 0,
					trim_bottom: 0,
					trim_left: 0,
					address_x: 0,
					address_y: 0,
					// Optional properties for fallback
					pagenumber_w: null,
					pagenumber_y: null,
				};
			};

			const apiFormat = findFormat(); // Get the full format details (API or fallback)

			if (!apiFormat) {
				console.error(
					"Seitenformat konnte nicht ermittelt werden für:",
					format,
				); // Changed to German
				return; // Exit if format couldn't be resolved
			}

			// Convert mm to cm for storing in PageSettings
			const widthCm = apiFormat.width / 10;
			const heightCm = apiFormat.height / 10;
			const trimCm = {
				trim_top: (apiFormat.trim_top ?? 0) / 10,
				trim_right: (apiFormat.trim_right ?? 0) / 10,
				trim_bottom: (apiFormat.trim_bottom ?? 0) / 10,
				trim_left: (apiFormat.trim_left ?? 0) / 10,
			};
			// Convert page number coords mm to cm, handle null
			const pageNumCm = {
				pagenumber_x:
					apiFormat.pagenumber_w !== null &&
					apiFormat.pagenumber_w !== undefined
						? apiFormat.pagenumber_w / 10
						: null,
				pagenumber_y:
					apiFormat.pagenumber_y !== null &&
					apiFormat.pagenumber_y !== undefined
						? apiFormat.pagenumber_y / 10
						: null,
			};
			// Convert address coords mm to cm, handle null
			const addressCm = {
				address_x: apiFormat.address_x / 10,
				address_y: apiFormat.address_y / 10,
			};

			// Create front page
			const frontPageId = uuidv4();
			const frontPage: PageSettings = {
				id: frontPageId,
				format: apiFormat.name,
				orientation: widthCm > heightCm ? "landscape" : "portrait",
				width: widthCm,
				height: heightCm,
				...trimCm,
				...pageNumCm, // Add page number coords
				...addressCm, // Add address coords
			};

			// Create back page
			const backPageId = uuidv4();
			const backPage: PageSettings = {
				id: backPageId,
				format: apiFormat.name,
				orientation: widthCm > heightCm ? "landscape" : "portrait",
				width: widthCm,
				height: heightCm,
				...trimCm,
				...pageNumCm, // Add page number coords
				...addressCm, // Add address coords
			};

			let newPagesArray: PageSettings[];
			// Check if insertAtIndex is a valid number and within bounds for insertion
			// pages.length is a valid index for splice to append
			if (
				typeof insertAtIndex === "number" &&
				insertAtIndex >= 0 &&
				insertAtIndex <= pages.length
			) {
				newPagesArray = [...pages];
				newPagesArray.splice(insertAtIndex, 0, frontPage, backPage);
			} else {
				// Default to appending if index is not provided or invalid
				newPagesArray = [...pages, frontPage, backPage];
			}

			setEditorState({ pages: newPagesArray, elements });
			setCurrentPage(frontPage.id);
		},
		[
			isEditorLocked,
			apiFormats,
			pages,
			elements,
			setEditorState,
			setCurrentPage,
		],
	);

	const handleDeletePage = useCallback(
		(pageId: string) => {
			if (isEditorLocked) return;
			console.log("Deleting page:", pageId);
			console.log("Current pages:", pages);

			// Find the page to delete
			const pageIndex = pages.findIndex((page) => page.id === pageId);
			if (pageIndex === -1) {
				console.error("Page not found:", pageId);
				return;
			}

			console.log("Page index:", pageIndex);

			// Check if this is a front or back page
			const isEvenIndex = pageIndex % 2 === 0;
			const otherPageIndex = isEvenIndex ? pageIndex + 1 : pageIndex - 1;

			// Check if the other page exists
			const hasOtherPage = otherPageIndex >= 0 && otherPageIndex < pages.length;
			if (!hasOtherPage) {
				throw new Error("Other page not found");
			}

			const otherPageId = hasOtherPage ? pages[otherPageIndex].id : null;

			console.log("Other page index:", otherPageIndex);
			console.log("Has other page:", hasOtherPage);
			console.log("Other page ID:", otherPageId);

			// Get non-page-number elements on the other page (if it exists)
			const elementsOnOtherPage = otherPageId
				? elements.filter(
						(el) => el.currentPageId === otherPageId && !el.isPageNumber,
					)
				: [];

			console.log(
				"Non-page-number elements on other page:",
				elementsOnOtherPage.length,
			);

			// delete both pages if the other page is empty (ignoring page numbers)
			if (elementsOnOtherPage.length === 0) {
				// Create new arrays without the pages and their elements
				const minIndex = Math.min(pageIndex, otherPageIndex);
				const maxIndex = Math.max(pageIndex, otherPageIndex);

				// Create a new array of pages without the deleted pages
				const newPages = [
					...pages.slice(0, minIndex),
					...pages.slice(maxIndex + 1),
				];

				console.log("Deleting both pages. New pages length:", newPages.length);

				// Update state
				setEditorState({
					pages: newPages,
					elements: elements.filter(
						(el) =>
							el.currentPageId !== pageId && el.currentPageId !== otherPageId,
					),
				});

				// Update current page if needed
				if (currentPage === pageId || currentPage === otherPageId) {
					setCurrentPage(newPages.length > 0 ? newPages[0].id : null);
				}
			} else {
				// If the other page has elements, just clear the elements of this page
				console.log("Clearing only elements of this page");

				// Remove only elements from this page
				const newElements = elements.filter(
					(el) => el.currentPageId !== pageId,
				);

				setEditorState({
					pages: pages,
					elements: newElements,
				});
			}
		},
		[
			isEditorLocked,
			pages,
			elements,
			setEditorState,
			currentPage,
			setCurrentPage,
		],
	);

	const handleReorderPages = useCallback(
		(fromPageIndex: number, toPageIndex: number) => {
			if (isEditorLocked) return;

			// Ensure we're working with page pairs (front and back pages)
			const fromPairIndex = Math.floor(fromPageIndex / 2);
			const toPairIndex = Math.floor(toPageIndex / 2);

			if (fromPairIndex === toPairIndex) return; // No change needed

			// Get the page pair to move
			const frontPageIndex = fromPairIndex * 2;
			const backPageIndex = frontPageIndex + 1;

			const frontPage = pages[frontPageIndex];
			const backPage = pages[backPageIndex];

			if (!frontPage) return; // Invalid page index

			// Create new pages array
			const newPages = [...pages];

			// Remove the pair from its current position
			const pagesToMove = backPage ? [frontPage, backPage] : [frontPage];
			newPages.splice(frontPageIndex, pagesToMove.length);

			// Calculate the new insertion index
			let insertIndex = toPairIndex * 2;

			// Adjust insertion index if moving to a position after the original position
			if (toPairIndex > fromPairIndex) {
				insertIndex -= pagesToMove.length;
			}

			// Insert the pages at the new position
			newPages.splice(insertIndex, 0, ...pagesToMove);

			// Update the editor state
			setEditorState({ pages: newPages, elements });
		},
		[isEditorLocked, pages, elements, setEditorState],
	);

	const handleSwapPages = useCallback(
		(fromIndex: number, toIndex: number) => {
			if (isEditorLocked) return;
			if (fromIndex === toIndex) return;

			// Ensure indices are in range
			if (
				fromIndex < 0 ||
				toIndex < 0 ||
				fromIndex >= pages.length ||
				toIndex >= pages.length
			) {
				console.warn("Invalid page indices for swap", { fromIndex, toIndex });
				return;
			}

			const newPages = [...pages];
			const temp = newPages[fromIndex];
			newPages[fromIndex] = newPages[toIndex];
			newPages[toIndex] = temp;

			setEditorState({ pages: newPages, elements });
		},
		[isEditorLocked, pages, elements, setEditorState],
	);

	return {
		handleAddPage,
		handleDeletePage,
		handleReorderPages,
		handleSwapPages,
	};
}
