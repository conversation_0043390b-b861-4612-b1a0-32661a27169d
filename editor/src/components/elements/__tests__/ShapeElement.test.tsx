import { render } from "@testing-library/react";
import { describe, expect, it } from "vitest";

import type { Element } from "@/types/element";
import { ShapeElement } from "../ShapeElement";

describe("ShapeElement component", () => {
	const mockRectangleElement: Element = {
		id: "shape-1",
		currentPageId: "page-1",
		type: "shape",
		shapeType: "rectangle",
		x: 10,
		y: 20,
		width: 100,
		height: 50,
		rotation: 0,
		backgroundColor: "#ff0000",
	};

	const mockCircleElement: Element = {
		id: "shape-2",
		currentPageId: "page-1",
		type: "shape",
		shapeType: "circle",
		x: 10,
		y: 20,
		width: 100,
		height: 100,
		rotation: 0,
		backgroundColor: "#00ff00",
	};

	const mockRoundedRectangleElement: Element = {
		id: "shape-3",
		currentPageId: "page-1",
		type: "shape",
		shapeType: "roundedRectangle",
		x: 10,
		y: 20,
		width: 100,
		height: 50,
		rotation: 0,
		backgroundColor: "#0000ff",
		borderRadius: [5, 10, 15, 20],
	};

	it("renders rectangle shape correctly", () => {
		const { container } = render(
			<ShapeElement element={mockRectangleElement} />,
		);

		const outerDiv = container.firstChild as HTMLElement;
		// Check style properties directly
		expect(outerDiv.style.width).toBe("100%");
		expect(outerDiv.style.height).toBe("100%");
		expect(outerDiv.style.borderRadius).toBe("0"); // Browser might normalize to '0' instead of '0px'

		const innerDiv = outerDiv.firstChild as HTMLElement;
		expect(innerDiv).toHaveClass("w-full h-full");
		expect(innerDiv.style.backgroundColor).toBe("rgb(255, 0, 0)");
	});

	it("renders circle shape correctly", () => {
		const { container } = render(<ShapeElement element={mockCircleElement} />);

		const outerDiv = container.firstChild as HTMLElement;
		// Check style properties directly
		expect(outerDiv.style.width).toBe("100%");
		expect(outerDiv.style.height).toBe("100%");
		expect(outerDiv.style.borderRadius).toBe("50%");

		const innerDiv = outerDiv.firstChild as HTMLElement;
		expect(innerDiv).toHaveClass("w-full h-full");
		expect(innerDiv.style.backgroundColor).toBe("rgb(0, 255, 0)");
	});

	it("renders rounded rectangle shape correctly", () => {
		const { container } = render(
			<ShapeElement element={mockRoundedRectangleElement} />,
		);

		const outerDiv = container.firstChild as HTMLElement;
		// Check style properties directly
		expect(outerDiv.style.width).toBe("100%");
		expect(outerDiv.style.height).toBe("100%");
		expect(outerDiv.style.borderRadius).toBe("5mm 10mm 15mm 20mm");

		const innerDiv = outerDiv.firstChild as HTMLElement;
		expect(innerDiv).toHaveClass("w-full h-full");
		expect(innerDiv.style.backgroundColor).toBe("rgb(0, 0, 255)");
	});

	it("uses default background color if none provided", () => {
		const elementWithoutBgColor = {
			...mockRectangleElement,
			backgroundColor: undefined,
		};

		const { container } = render(
			<ShapeElement element={elementWithoutBgColor} />,
		);

		const innerDiv = container.firstChild?.firstChild as HTMLElement;
		expect(innerDiv.style.backgroundColor).toBe("rgb(226, 232, 240)"); // Default color
	});
});
