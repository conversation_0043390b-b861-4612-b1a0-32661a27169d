{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**/*.ts", "**/*.tsx", "**/*.json"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}