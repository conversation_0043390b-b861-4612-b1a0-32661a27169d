import { useState } from "react";
import type {
	ColorInfo,
	FontInfo,
	PageFormat,
	TemplateInfo,
	TestDataRecord,
	VersionHistoryEntry,
} from "@/utils/apiService";

export function useApiData() {
	const [apiFormats, setApiFormats] = useState<PageFormat[]>([]);
	const [documentVariables, setDocumentVariables] = useState<string[]>([]);
	const [testData, setTestData] = useState<TestDataRecord[]>([]);
	const [versionHistory, setVersionHistory] = useState<VersionHistoryEntry[]>(
		[],
	);

	// Font-related state
	const [apiFonts, setApiFontsState] = useState<FontInfo[]>([]);
	const [isLoadingFonts, setIsLoadingFonts] = useState<boolean>(true);
	const [loadedFonts, setLoadedFonts] = useState<Set<string>>(new Set());

	// Color-related state
	const [apiColors, setApiColors] = useState<ColorInfo[]>([]);
	const [isLoadingColors, setIsLoadingColors] = useState<boolean>(true);
	const [colorError, setColorError] = useState<string | null>(null);

	// Template-related state
	const [templates, setTemplates] = useState<TemplateInfo[]>([]);
	const [isLoadingTemplates, setIsLoadingTemplates] = useState<boolean>(true);
	const [templatesError, setTemplatesError] = useState<string | null>(null);

	return {
		// API data state
		apiFormats,
		documentVariables,
		testData,
		versionHistory,
		apiFonts,
		isLoadingFonts,
		loadedFonts,
		apiColors,
		isLoadingColors,
		colorError,
		templates,
		isLoadingTemplates,
		templatesError,

		// Setters
		setApiFormats,
		setDocumentVariables,
		setTestData,
		setVersionHistory,
		setApiFontsState,
		setIsLoadingFonts,
		setLoadedFonts,
		setApiColors,
		setIsLoadingColors,
		setColorError,
		setTemplates,
		setIsLoadingTemplates,
		setTemplatesError,
	};
}
