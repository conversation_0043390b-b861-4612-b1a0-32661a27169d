import { Clipboard } from "lucide-react";
import { forwardRef } from "react";

interface ContextMenuState {
	visible: boolean;
	screenX: number;
	screenY: number;
	pasteX?: number;
	pasteY?: number;
	pastePageId?: string;
}

interface ContextMenuProps {
	contextMenu: ContextMenuState | null;
	hasClipboard: boolean;
	onPaste: () => void;
	onClose: () => void;
}

export const ContextMenu = forwardRef<HTMLDivElement, ContextMenuProps>(
	({ contextMenu, hasClipboard, onPaste, onClose }, ref) => {
		if (!contextMenu || !contextMenu.visible) {
			return null;
		}

		const handlePaste = () => {
			onPaste();
			onClose();
		};

		return (
			<div
				ref={ref}
				className="fixed bg-white border border-gray-300 rounded shadow-lg py-1 z-50"
				style={{
					left: `${contextMenu.screenX}px`,
					top: `${contextMenu.screenY}px`,
				}}
				role="menu"
				tabIndex={-1}
				onClick={(e) => e.stopPropagation()}
				onKeyDown={(e) => {
					if (e.key === "Escape") {
						e.stopPropagation();
					}
				}}
			>
				<button
					type="button"
					onClick={handlePaste}
					disabled={!hasClipboard}
					className="w-full px-4 py-2 text-left hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
				>
					<Clipboard size={16} />
					Paste
				</button>
			</div>
		);
	},
);
