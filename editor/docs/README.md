# Documentation Index

This directory contains comprehensive documentation for the TypeScript Editor Integration project - a sophisticated document editor application built with React and TypeScript.

## Documentation Structure

### 📋 [Architecture Overview](./architecture.md)
- Application structure and core technologies
- Architectural patterns and design decisions
- State management strategy
- Component hierarchy and data flow
- Performance optimizations

### 🧩 [Component Documentation](./components.md)
- Core components (Page, EditableElement, Sidebar)
- Element-specific components (Text, Shape, Image, Table, Block)
- UI components and utilities
- Export components for rendering
- Component communication patterns

### 🔄 [State Management](./state-management.md)
- Custom hooks architecture
- Editor state structure
- Operation hooks (Element, Page operations)
- State update patterns and optimizations
- Error handling and validation

### ✨ [Features](./features.md)
- Document management capabilities
- Element system and types
- Text editing and formatting
- Layout and design tools
- Export and output options
- User interface features

### 🛠️ [Development Guide](./development-guide.md)
- Getting started and setup
- Project structure and organization
- Development patterns and guidelines
- Testing strategy
- Code quality standards
- Deployment process

### 🔌 [API Integration](./api-integration.md)
- External API integrations
- Font and color management
- Template system
- Document management APIs
- Error handling and caching
- Authentication and real-time updates

### 🎨 [Design System](./design.md)
- UI/UX design principles
- Component specifications
- Feature requirements
- User workflows
- Phase-based development plan

## Application Overview

This is a professional document editor that enables users to:

- Create multi-page documents with various formats
- Add and edit text, shapes, images, tables, and blocks
- Apply rich text formatting and styling
- Export to HTML and PDF formats
- Manage templates and reusable components
- Integrate with external APIs for fonts, colors, and data

## Key Technologies

- **React 19.1.0** - Modern UI framework
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool
- **TipTap** - Rich text editing
- **Tailwind CSS** - Utility-first styling
- **Radix UI** - Accessible components

## Architecture Highlights

- **Custom Hooks**: Sophisticated state management with undo/redo
- **Element System**: Universal component for all content types
- **Batched Operations**: Performance-optimized state updates
- **Export System**: Clean HTML/PDF generation
- **API Integration**: Dynamic content and resource loading

## Getting Started

```bash
# Install dependencies
npm install

# Build the editor in watch mode
npm run dev-build

# Run tests
npm test

# Build for production
npm run build
```

For detailed setup instructions, see the [Development Guide](./development-guide.md).

## Contributing

Please read the [Development Guide](./development-guide.md) for information about:
- Code style and standards
- Testing requirements
- Pull request process
- Documentation updates

## Support

For questions about the codebase:
1. Check the relevant documentation section
2. Review the component source code
3. Look at existing tests for examples
4. Consult the development team