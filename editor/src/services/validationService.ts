import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

export interface ValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

export interface ElementValidationResult extends ValidationResult {
	correctedElement?: Element;
}

export interface PageValidationResult extends ValidationResult {
	correctedPage?: PageSettings;
}

/**
 * Validate element properties and constraints
 */
export function validateElement(
	element: Element,
	page?: PageSettings,
): ElementValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];
	let correctedElement: Element | undefined;

	// Check required properties
	if (!element.id) {
		errors.push("Element must have an ID");
	}

	if (!element.type) {
		errors.push("Element must have a type");
	}

	if (typeof element.x !== "number" || Number.isNaN(element.x)) {
		errors.push("Element x position must be a valid number");
	}

	if (typeof element.y !== "number" || Number.isNaN(element.y)) {
		errors.push("Element y position must be a valid number");
	}

	if (
		typeof element.width !== "number" ||
		Number.isNaN(element.width) ||
		element.width <= 0
	) {
		errors.push("Element width must be a positive number");
	}

	if (
		typeof element.height !== "number" ||
		Number.isNaN(element.height) ||
		element.height <= 0
	) {
		errors.push("Element height must be a positive number");
	}

	// Validate position and size constraints
	if (page) {
		const pageWidthMm = page.width * 10; // Convert cm to mm
		const pageHeightMm = page.height * 10;

		// Check if element is within page bounds
		if (element.x < 0) {
			warnings.push("Element x position is negative");
			correctedElement = { ...element, x: 0 };
		}

		if (element.y < 0) {
			warnings.push("Element y position is negative");
			correctedElement = { ...(correctedElement || element), y: 0 };
		}

		if (element.x + element.width > pageWidthMm) {
			warnings.push("Element extends beyond page width");
			const maxX = Math.max(0, pageWidthMm - element.width);
			correctedElement = { ...(correctedElement || element), x: maxX };
		}

		if (element.y + element.height > pageHeightMm) {
			warnings.push("Element extends beyond page height");
			const maxY = Math.max(0, pageHeightMm - element.height);
			correctedElement = { ...(correctedElement || element), y: maxY };
		}
	}

	// Validate type-specific properties
	switch (element.type) {
		case "text":
			if (!element.content) {
				warnings.push("Text element should have content");
			}
			break;

		case "image":
			if (!("src" in element) || !element.src) {
				errors.push("Image element must have a source URL");
			}
			break;

		case "block":
			if (element.childElementIds && !Array.isArray(element.childElementIds)) {
				errors.push("Block element childElementIds must be an array");
			}
			break;

		case "table":
			if (!("tableData" in element) || !element.tableData) {
				errors.push("Table element must have table data");
			}
			break;
	}

	// Validate z-index
	if (
		element.zIndex !== undefined &&
		(typeof element.zIndex !== "number" || Number.isNaN(element.zIndex))
	) {
		warnings.push("Element z-index must be a valid number");
		correctedElement = { ...(correctedElement || element), zIndex: 0 };
	}

	// Validate rotation
	if (
		element.rotation !== undefined &&
		(typeof element.rotation !== "number" || Number.isNaN(element.rotation))
	) {
		warnings.push("Element rotation must be a valid number");
		correctedElement = { ...(correctedElement || element), rotation: 0 };
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
		correctedElement,
	};
}

/**
 * Validate page properties and constraints
 */
export function validatePage(page: PageSettings): PageValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];
	let correctedPage: PageSettings | undefined;

	// Check required properties
	if (!page.id) {
		errors.push("Page must have an ID");
	}

	if (!page.format) {
		errors.push("Page must have a format");
	}

	if (
		typeof page.width !== "number" ||
		Number.isNaN(page.width) ||
		page.width <= 0
	) {
		errors.push("Page width must be a positive number");
	}

	if (
		typeof page.height !== "number" ||
		Number.isNaN(page.height) ||
		page.height <= 0
	) {
		errors.push("Page height must be a positive number");
	}

	// Validate trim values
	const trimProperties = [
		"trim_left",
		"trim_right",
		"trim_top",
		"trim_bottom",
	] as const;
	trimProperties.forEach((prop) => {
		const value = page[prop];
		if (
			value !== undefined &&
			(typeof value !== "number" || Number.isNaN(value) || value < 0)
		) {
			warnings.push(`Page ${prop} must be a non-negative number`);
			correctedPage = { ...(correctedPage || page), [prop]: 0 };
		}
	});

	// Validate page number coordinates
	if (page.pagenumber_x !== undefined) {
		if (
			typeof page.pagenumber_x !== "number" ||
			Number.isNaN(page.pagenumber_x)
		) {
			warnings.push("Page number X coordinate must be a valid number");
			correctedPage = { ...(correctedPage || page), pagenumber_x: undefined };
		}
	}

	if (page.pagenumber_y !== undefined) {
		if (
			typeof page.pagenumber_y !== "number" ||
			Number.isNaN(page.pagenumber_y)
		) {
			warnings.push("Page number Y coordinate must be a valid number");
			correctedPage = { ...(correctedPage || page), pagenumber_y: undefined };
		}
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
		correctedPage,
	};
}

/**
 * Validate element relationships (parent-child consistency)
 */
export function validateElementRelationships(
	elements: Element[],
): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	const elementMap = new Map(elements.map((el) => [el.id, el]));

	elements.forEach((element) => {
		// Check parent relationship
		if (element.parentId) {
			const parent = elementMap.get(element.parentId);
			if (!parent) {
				errors.push(
					`Element ${element.id} references non-existent parent ${element.parentId}`,
				);
			} else if (parent.type !== "block") {
				errors.push(
					`Element ${element.id} has parent ${element.parentId} which is not a block`,
				);
			} else if (!parent.childElementIds?.includes(element.id)) {
				errors.push(
					`Element ${element.id} claims parent ${element.parentId} but parent doesn't list it as child`,
				);
			}
		}

		// Check child relationships for blocks
		if (element.type === "block" && element.childElementIds) {
			element.childElementIds.forEach((childId) => {
				const child = elementMap.get(childId);
				if (!child) {
					errors.push(
						`Block ${element.id} references non-existent child ${childId}`,
					);
				} else if (child.parentId !== element.id) {
					errors.push(
						`Block ${element.id} lists ${childId} as child but child doesn't reference it as parent`,
					);
				}
			});
		}
	});

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Validate page-element relationships
 */
export function validatePageElementRelationships(
	pages: PageSettings[],
	elements: Element[],
): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	const pageIds = new Set(pages.map((p) => p.id));

	elements.forEach((element) => {
		if (!pageIds.has(element.currentPageId)) {
			errors.push(
				`Element ${element.id} references non-existent page ${element.currentPageId}`,
			);
		}
	});

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Validate entire document structure
 */
export function validateDocument(
	pages: PageSettings[],
	elements: Element[],
): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Validate individual pages
	pages.forEach((page) => {
		const pageValidation = validatePage(page);
		errors.push(...pageValidation.errors);
		warnings.push(...pageValidation.warnings);
	});

	// Validate individual elements
	elements.forEach((element) => {
		const page = pages.find((p) => p.id === element.currentPageId);
		const elementValidation = validateElement(element, page);
		errors.push(...elementValidation.errors);
		warnings.push(...elementValidation.warnings);
	});

	// Validate relationships
	const relationshipValidation = validateElementRelationships(elements);
	errors.push(...relationshipValidation.errors);
	warnings.push(...relationshipValidation.warnings);

	const pageElementValidation = validatePageElementRelationships(
		pages,
		elements,
	);
	errors.push(...pageElementValidation.errors);
	warnings.push(...pageElementValidation.warnings);

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Check if element can be safely deleted
 */
export function canDeleteElement(
	elementId: string,
	elements: Element[],
): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	const element = elements.find((el) => el.id === elementId);
	if (!element) {
		errors.push(`Element ${elementId} not found`);
		return { isValid: false, errors, warnings };
	}

	// Check if other elements depend on this one
	const dependentElements = elements.filter((el) => el.parentId === elementId);
	if (dependentElements.length > 0) {
		warnings.push(
			`Deleting element ${elementId} will also delete ${dependentElements.length} child elements`,
		);
	}

	return {
		isValid: true,
		errors,
		warnings,
	};
}

/**
 * Check if page can be safely deleted
 */
export function canDeletePage(
	pageId: string,
	pages: PageSettings[],
	elements: Element[],
): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	const page = pages.find((p) => p.id === pageId);
	if (!page) {
		errors.push(`Page ${pageId} not found`);
		return { isValid: false, errors, warnings };
	}

	if (pages.length === 1) {
		errors.push("Cannot delete the last remaining page");
		return { isValid: false, errors, warnings };
	}

	// Check if page has elements
	const elementsOnPage = elements.filter((el) => el.currentPageId === pageId);
	if (elementsOnPage.length > 0) {
		warnings.push(
			`Deleting page ${pageId} will also delete ${elementsOnPage.length} elements`,
		);
	}

	return {
		isValid: true,
		errors,
		warnings,
	};
}
