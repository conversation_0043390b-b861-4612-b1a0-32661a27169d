import type React from "react";

interface ZoomControlsProps {
	zoomLevel: number;
	onZoomChange: (zoom: number) => void;
	className?: string;
}

export function ZoomControls({
	zoomLevel,
	onZoomChange,
	className = "",
}: ZoomControlsProps) {
	const handleZoomOut = () => {
		onZoomChange(Math.max(0.5, zoomLevel - 0.1));
	};

	const handleZoomIn = () => {
		onZoomChange(Math.min(2, zoomLevel + 0.1));
	};

	const handleResetZoom = () => {
		onZoomChange(1);
	};

	const handleRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		onZoomChange(parseFloat(e.target.value));
	};

	return (
		<div
			className={`fixed bottom-4 right-4 bg-white rounded-lg shadow-md p-3 flex items-center gap-3 z-10 ${className}`}
		>
			<button
				type="button"
				className="text-gray-600 hover:text-gray-900 focus:outline-none"
				onClick={handleZoomOut}
				title="Zoom Out"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					className="h-4 w-4"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
					aria-label="Zoom Out"
				>
					<title>Zoom Out</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M20 12H4"
					/>
				</svg>
			</button>
			<span className="text-xs text-gray-600 font-medium min-w-[50px] text-center">
				{Math.round(zoomLevel * 100)}%
			</span>
			<input
				type="range"
				min="0.5"
				max="2"
				step="0.1"
				value={zoomLevel}
				onChange={handleRangeChange}
				className="w-24"
				title="Zoom Level"
			/>
			<button
				type="button"
				className="text-gray-600 hover:text-gray-900 focus:outline-none"
				onClick={handleZoomIn}
				title="Zoom In"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					className="h-4 w-4"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
					aria-label="Zoom In"
				>
					<title>Zoom In</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 4v16m8-8H4"
					/>
				</svg>
			</button>
			<button
				type="button"
				className="text-gray-600 hover:text-gray-900 focus:outline-none ml-1"
				onClick={handleResetZoom}
				title="Reset Zoom"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					className="h-4 w-4"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
					aria-label="Reset Zoom"
				>
					<title>Reset Zoom</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
					/>
				</svg>
			</button>
		</div>
	);
}
